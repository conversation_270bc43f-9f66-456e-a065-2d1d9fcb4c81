<div class="wrapper wrapper-home" fxLayout="column">
  <!-- <div class="filter-container" fxLayout="row" fxLayoutGap="20px" fxFlex="100"> -->
    <!-- <mat-button-toggle-group #group="matButtonToggleGroup" class="toggle-btn" [value]="selectedValForToggle"
      class="toggle-btn" (change)="onButtonToggle(group.value)">
      <mat-button-toggle value="email">Email Notification</mat-button-toggle>
      <mat-button-toggle value="in-app ">In-app Notification</mat-button-toggle>
    </mat-button-toggle-group> -->
    <!-- <p>Email Notification</p> -->
  <!-- </div> -->
  <!-- settings page content -->
  <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
  </div>

  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <div>
      <p class="header">Email Notifications from us</p>
      <table mat-table [dataSource]="dataSource">
        <!-- Position Column -->
        <ng-container matColumnDef="Actions">
          <th mat-header-cell *matHeaderCellDef>
            <div fxLayout="row" fxLayoutAlign="space-between center">
              <span> Actions</span>
              <mat-slide-toggle class="text-theme-primary" [checked]="errors_in_uploaded_file" (change)="setAllToggleSwitches($event)">
              </mat-slide-toggle>
            </div>
          </th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="row" fxLayoutAlign="space-between center">
              <span class="main-data"> {{ element.action }}</span>
              <mat-slide-toggle class="text-theme-primary" [checked]="element.value" 
                (change)="setValue($event, element.action)"></mat-slide-toggle>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>
  </div>
</div>
