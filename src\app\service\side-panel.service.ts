import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, throwError } from 'rxjs';
import { Globals } from '../_globals/endpoints.global';

@Injectable({
  providedIn: 'root',
})
export class SidePanelService {
  private showNav$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false
  );
  private activePanelId$: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  constructor(private globals: Globals, private http: HttpClient) {}

  showNavStatus = this.showNav$.asObservable();

  getShowNav() {
    return this.showNav$.asObservable();
  }

  getActivePanelId() {
    return this.activePanelId$.asObservable();
  }

  getCurrentActivePanelId(): string | null {
    return this.activePanelId$.value;
  }

  /**
   * show / hide panel
   */
  setShowNav(showHide: boolean, panelId?: string) {
    if (showHide && panelId) {
      this.activePanelId$.next(panelId);
    } else if (showHide && !panelId) {
      // If opening a panel but no ID specified, use null for backward compatibility
      this.activePanelId$.next(null);
    } else if (!showHide) {
      this.activePanelId$.next(null);
    }
    this.showNav$.next(showHide);
  }

  toggleNavState() {
    this.showNav$.next(!this.showNav$.value);
  }

  isNavOpen() {
    return this.showNav$.value;
  }
}
