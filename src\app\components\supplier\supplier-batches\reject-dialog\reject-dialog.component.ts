import { Component, Inject, OnInit, Optional } from '@angular/core';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { HomeService } from '../../../../service/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { SupplierBatchesComponent } from '../supplier-batches.component';

@Component({
  selector: 'supplier-reject-dialog',
  templateUrl: './reject-dialog.component.html',
  styleUrls: ['./reject-dialog.component.scss']
})
export class SupplierRejectDialogComponent implements OnInit {
  public reasonForm: FormGroup;
  subscriptionId;
  batch_id;
  formValues;
  fromDialog;
  values;
  constructor(
    private fb: FormBuilder,
    private homeService: HomeService,
    public matSnackbar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<SupplierBatchesComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public datas: any
    
  ) { }

  ngOnInit(): void {
    // console.log(this.data)
    this.subscriptionId = this.data.sub_id;
    this.batch_id = this.data.batch_id
    this.reasonForm = this.fb.group({
      comment: ['', Validators.required],
    });
  }

   closeDialog() {
    this.values = this.reasonForm.value;
    // console.log(this.values);
    this.dialogRef.close({ event: 'close', data: this.values.comment });
  }
   
}
