import { NgModule } from '@angular/core';
//angular material
import { MaterialModule } from '../material';
import { BrowserModule } from '@angular/platform-browser';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';

import { TruncatePipe } from 'src/app/_pipe/truncate.pipe';
import { UnderscoreAsSpacePipe } from './_pipe/underscore-as-space.pipe';
import { CommmaSeperatorPipe } from './_pipe/commma-seperator.pipe';
import { SafeContentPipe } from './_pipe/safe-content.pipe';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HomeComponent } from './components/home/<USER>';
import { SideNavComponent } from './components/header/side-nav/side-nav.component';
import { TopNavComponent } from './components/header/top-nav/top-nav.component';
import { SidePanelComponent } from './components/side-panel/side-panel.component';
import { BatchesComponent } from './components/home/<USER>/batches.component';
import { SupplierComponent } from './components/supplier/supplier.component';
import { DataQualityComponent } from './components/data-quality/data-quality.component';
import { SettingsComponent } from './components/settings/settings.component';
import { HelpComponent } from './components/help/help.component';
import { BatchActivitesComponent } from './components/home/<USER>/batch-activites.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuthHttpInterceptor, AuthModule } from '@auth0/auth0-angular';
import { environment } from '../environments/environment';
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
  HttpClientXsrfModule,
} from '@angular/common/http';
import { HttpConfigInterceptor } from './_interceptors/http-config.interceptor';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { LoadingComponent } from './components/loading/loading.component';
import { CommentsComponent } from './components/comments/comments.component';
import { MentionsModule } from '@flxng/mentions';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { RoundOffPipe } from './_pipe/round-off.pipe';
import { RejectDialogComponent } from './components/home/<USER>/reject-dialog/reject-dialog.component';
import { ImageViewerComponent } from './_dialog/image-viewer/image-viewer.component';
import { OfflineComponent } from './components/offline/offline.component';
import { SupplierBatchesComponent } from './components/supplier/supplier-batches/supplier-batches.component';
import { SupplierRejectDialogComponent } from './components/supplier/supplier-batches/reject-dialog/reject-dialog.component';
import { ClickDebounceDirective } from './directives/click-debounce.directive';
import { DeleteTagComponent } from './_dialog/delete-tag/delete-tag.component';
import { SupplierConfigurationComponent } from './components/supplier/supplier-configuration/supplier-configuration.component';
import { FileDetailsComponent } from './components/supplier/file-details/file-details.component';
import { ConfirmDialogComponent } from './_dialog/confirm-dialog/confirm-dialog.component';
import { TicketViewComponent } from './components/ticket-view/ticket-view.component';
import { MarkdownModule } from 'ngx-markdown';
import { QuillModule } from 'ngx-quill';
import { TimeAgoPipe } from './_pipe/time-ago.pipe';
@NgModule({
  declarations: [
    AppComponent,
    TruncatePipe,
    UnderscoreAsSpacePipe,
    CommmaSeperatorPipe,
    SafeContentPipe,
    HomeComponent,
    SideNavComponent,
    TopNavComponent,
    SidePanelComponent,
    BatchesComponent,
    SupplierComponent,
    SupplierBatchesComponent,
    DataQualityComponent,
    SettingsComponent,
    HelpComponent,
    BatchActivitesComponent,
    LoadingComponent,
    CommentsComponent,
    RoundOffPipe,
    RejectDialogComponent,
    SupplierRejectDialogComponent,
    ImageViewerComponent,
    OfflineComponent,
    ClickDebounceDirective,
    DeleteTagComponent,
    SupplierConfigurationComponent,
    FileDetailsComponent,
    ConfirmDialogComponent,
    TicketViewComponent,
    TimeAgoPipe,
  ],
  imports: [
    HttpClientModule,
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MaterialModule,
    NgxDropzoneModule,
    FormsModule,
    ReactiveFormsModule,
    MentionsModule,
    ClipboardModule,
    InfiniteScrollModule,
    AuthModule.forRoot({
      domain: environment.auth0.domain,
      clientId: environment.auth0.clientId,
      redirectUri: environment.auth0.callbackURL,
      useRefreshTokens: environment.auth0.useRefreshTokens,
      audience: environment.auth0.audience,
      prompt: 'none',
      httpInterceptor: {
        allowedList: ['/api/*'],
      },
    }),
    HttpClientXsrfModule.withOptions({
      cookieName: 'csrftoken',
      headerName: 'x-csrf',
    }),
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MarkdownModule.forRoot(),
    QuillModule.forRoot(),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthHttpInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpConfigInterceptor,
      multi: true,
    },
    // AppPermissionsService,
    { provide: MAT_DATE_LOCALE, useValue: 'en-GB' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
