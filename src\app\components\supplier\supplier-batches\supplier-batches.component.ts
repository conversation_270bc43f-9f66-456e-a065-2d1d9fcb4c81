import {
  <PERSON>mpo<PERSON>,
  ElementRef,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SidePanelService } from '../../../service/side-panel.service';
import moment from 'moment';
import { MatSort, Sort } from '@angular/material/sort';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  ActivatedRoute,
  Router,
  Params,
  NavigationStart,
} from '@angular/router';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { HomeService } from '../../../service/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import { UserService } from 'src/app/service/user.service';
import { User } from '@auth0/auth0-angular';
import { SupplierRejectDialogComponent } from './reject-dialog/reject-dialog.component';
import { FileUploadService } from '../../../service/file-upload.service';
import { SnackbarService } from '../../../service/snackbar.service';
import { Subscription } from 'rxjs';
import { DeleteTagComponent } from 'src/app/_dialog/delete-tag/delete-tag.component';

export interface BatchTableList {
  Batch_ID: any;
  Supplier_Code: any;
  Supplier_Brand_Code: any;
  Name: string;
  Source: any;
  Total_Rows: number;
  Last_Updated_Time: any;
  Actions: number;
}

@Component({
  selector: 'app-supplier-batches',
  templateUrl: './supplier-batches.component.html',
  styleUrls: ['./supplier-batches.component.scss'],
})
export class SupplierBatchesComponent implements OnInit, OnDestroy {
  uploadBatch: boolean = false;
  batchLog: boolean = false;
  addTag: boolean = false;
  files: File[] = [];
  fileToUpload: File;
  multipleFilesUpload: FileList;
  isUpload: boolean = true;
  progress: boolean = false;
  cancelBatch: boolean = false;
  public uploadForm: FormGroup;
  public multiUploadForm: FormGroup;
  displayedColumns: string[] = [
    'Batch_ID',
    'Supplier',
    'Brand',
    'Name & Description',
    'Tags',
    'Total_Rows',
    'Last_Updated',
    'Actions',
    'Comments',
  ];
  batchDataSource;
  selected = 'recent';
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: Date;
  customStartDate: Date;
  maxDate: Date;
  minDate: Date;
  start_date;
  end_date;
  page: number;
  size: number;
  total_pages: number;
  totalItems: number;
  subscriptionId: any;
  supplier_code: any;
  supplier_name: any;
  selectedTab;
  selectedIndex;
  headercount: any;
  batchList: any;
  search;
  selectedStatus = '';
  status;
  label;
  searchedMultipleVals: string[] = [];
  filterObj: Object = {};
  searchedItems: string[] = [];
  filterList: any;
  selectedSupplier;
  datePickerValue;
  tagList: any;
  tableLoading: boolean = true;
  dataLoading: boolean = false;
  createlabelFormValues: any;
  labelColor: any;
  textColor: any;
  labelList: any;
  public createLabelForm: FormGroup;
  viewAllBrands: boolean = false;
  brandList: any;
  supplier_brand: any;
  currentBatch;
  pagePermissions;
  routeFrom;
  tabs = ['in_queue', 'completed', 'approved', 'published', 'cancelled'];
  tabIndexWithData;
  selectedSupplierUpload;
  inputFormat;
  outputFormat;
  chanelList;
  manualUpload: boolean = false;
  pimUpload: boolean = false;
  channel;
  err_info: boolean = false;
  info_log;
  IOvalues: boolean = false;
  mainFile = [];
  fileCounter: number = 0;
  @ViewChild('mainFileInput') mainFileInput;
  @ViewChild('additionalFileInput') additionalFileInput: ElementRef;

  additionalFilesData = [];
  isFileServiceBusy: boolean = false;
  uuid = '';
  tagCheckRegex = '^(?!.*<[^>]+>).*';
  retailer_name: string;
  routeSubscription: Subscription;
  currentUser;

  constructor(
    private sidepanel: SidePanelService,
    public dialog: MatDialog,
    private router: Router,
    private activatedroute: ActivatedRoute,
    public matSnackbar: MatSnackBar,
    private homeService: HomeService,
    private fb: FormBuilder,
    private userService: UserService,
    public fileUploadService: FileUploadService,
    private snackbarService: SnackbarService
  ) {}

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  /**
   * Tab/Browser exit alert
   * @param event
   * @returns
   */
  @HostListener('window:beforeunload', ['$event'])
  beforeunloadHandler(event: any) {
    if (this.isFileServiceBusy) {
      return false;
    }
  }

  ngOnInit() {
    this.currentUser = JSON.parse(localStorage.getItem('user'));
    this.routeSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dialog.closeAll();
      }
    });
    this.dataLoading = true;
    this.createLabelForm = this.fb.group({
      name: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
      title: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
    });
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    // page permissions
    this.pagePermissions = this.userService.appPermissions;
    this.activatedroute.queryParams.subscribe((params: Params) => {
      // this.subscriptionId = params.sub;
      this.supplier_code = params.supplier_code;
      this.supplier_name = params.supplier_name;

      if (params.from == 'comments') {
        this.routeFrom = 'supplier';
      } else {
        this.routeFrom = params.from;
      }

      localStorage.setItem('supplier_code', this.supplier_code);
      localStorage.setItem('supplier_name', this.supplier_name);
    });

    // this.batchDataSource.sort = this.sort;
    const d = new Date();
    this.search = '';
    this.page = 1;
    this.size = 50;
    // this.status = 'in_queue';
    // this.selectedTab = 'in_queue';
    this.start_date = '';
    this.end_date = '';
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentyear = new Date().getFullYear();
    this.minDate = new Date(currentyear - 20, 0, 1);
    this.maxDate = new Date();
    this.selected = 'recent';
    this.selectedStatus = '';
    this.selectedIndex = 0;
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];

    /**
     * File upload
     */
    this.multiUploadForm = this.fb.group({
      supplierName: ['', Validators.required],
      inputFormat: ['', Validators.required],
      batchName: [
        '',
        [
          Validators.required,
          this.noWhitespaceValidator,
          Validators.pattern(this.tagCheckRegex),
          Validators.maxLength(100),
        ],
      ],
      reference: ['', [this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex), Validators.maxLength(200)]],
      outputFormat: ['', Validators.required],
      description: [
        '',
        [this.noWhitespaceValidator, Validators.maxLength(2000), Validators.pattern(this.tagCheckRegex)],
      ],
    });

    /**
     * Pim upload
     */
    this.uploadForm = this.fb.group({
      uploadSupplier: ['', Validators.required],
      uploadname: ['', [Validators.required, this.noWhitespaceValidator, Validators.pattern(this.tagCheckRegex)]],
      uploadInput: ['', Validators.required],
      uploadOutput: ['', Validators.required],
    });
    // to retrieve client information
    this.getUserDetails();
    this.getSupplierList(this.subscriptionId);
    this.getTagList(this.subscriptionId);
    this.getChannelList(this.subscriptionId);

    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
  }

  getUserDetails = () => {
    if (this.subscriptionId) {
      this.userService.me(this.subscriptionId).subscribe({
        next: (resp) => {
          this.retailer_name = resp.result.retailer_name;
          // get data for table
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
        },
      });
    }
  }

  ngOnDestroy(): void {
    this.routeSubscription.unsubscribe();
  }

  /**
  Custom Validator to restrict whitespace in Input fields 
  **/
  public noWhitespaceValidator(control: FormControl) {
    if (control.value) {
      const isWhitespace = (control.value || '').trim().length == 0;
      const isValid = !isWhitespace;
      return isValid ? null : { whitespace: true };
    }
    return null;
  }

  /**
   * TabChange event
   * @param tabChangeEvent
   */
  tabChanged = (tabChangeEvent): void => {
    this.tableLoading = true;
    this.selectedTab = tabChangeEvent.tab.textLabel;
    this.selectedIndex = tabChangeEvent.index;
    this.batchDataSource = [];
    this.status = this.selectedTab;
    this.page = 1;
    // this.getCount(this.subscriptionId, this.supplier_code);
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  closeInfo = () => {
    this.err_info = false;
  };

  /**
   * toggleSideNav
   */
  toggleSideNav = (value, batch_id, status) => {
    this.currentBatch = batch_id;
    this.status = status;
    if (value == 'uploadBatch') {
      this.batchLog = false;
      this.addTag = false;
      this.viewAllBrands = false;
      this.manualUpload = false;
      this.pimUpload = false;
      this.uploadBatch = true;
    } else if (value == 'batchLog') {
      this.addTag = false;
      this.uploadBatch = false;
      this.viewAllBrands = false;
      this.manualUpload = false;
      this.pimUpload = false;
      this.batchLog = true;
    } else if (value == 'FILE_UPLOAD') {
      this.multiUploadForm.reset();
      this.uploadForm.reset();
      this.files = [];
      this.selectedSupplierUpload = this.supplier_name;
      this.addTag = false;
      this.uploadBatch = false;
      this.viewAllBrands = false;
      this.batchLog = false;
      this.pimUpload = false;
      this.manualUpload = true;
      this.channel = value;

      this.getInputOutput(this.subscriptionId, this.supplier_code, this.supplier_name);
    } else if (value == 'PIM_UPLOAD') {
      this.multiUploadForm.reset();
      this.uploadForm.reset();
      this.files = [];
      this.selectedSupplierUpload = this.supplier_name;
      this.pimUpload = true;
      this.addTag = false;
      this.uploadBatch = false;
      this.viewAllBrands = false;
      this.batchLog = false;
      this.manualUpload = false;
      this.channel = value;

      this.getInputOutput(this.subscriptionId, this.supplier_code, this.supplier_name);
    } else {
      this.uploadBatch = false;
      this.viewAllBrands = false;
      this.batchLog = false;
      this.manualUpload = false;
      this.pimUpload = false;
      this.addTag = true;
    }
    this.sidepanel.setShowNav(true);
  };

  // toggleSideNavForUpload = (channel) => {
  //   console.log(channel)
  //   this.manualUpload == true ;
  //   this.sidepanel.setShowNav(true);
  // };

  // uploadChannelSupplier = (supplier) => {
  //   const code = supplier.supplier_code;

  // }

  getChannelList = (subscription_id) => {
    this.homeService.getChannelList(subscription_id).subscribe({
      next: (resp) => {
        this.chanelList = resp.channels;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  getInputOutput = (subscription_id, supplier_code, supplier_name) => {
    this.homeService
      .getInputOutput(subscription_id, supplier_code, supplier_name, this.channel)
      .subscribe({
        next: (resp) => {
          this.selectedSupplierUpload = this.supplier_name;
          this.inputFormat = resp.input_templates;
          this.outputFormat = resp.output_templates;
          if (this.manualUpload) {
            this.uploadInput = this.inputFormat[0];
            this.uploadOutput = this.outputFormat[0];
          }
          this.IOvalues = true;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  uploadBatchFormValues: any;
  uploadProgress: number;
  fileUploadStatus: any = 'Idle';
  uploadButtonLabel: String = 'Upload';
  dropzoneLabel: String = 'Drag or Drop your files here';

  upload = () => {
    this.dropzoneLabel = 'Uploading..';
    this.fileUploadStatus = 'Uploading';
    // retry upload
    this.uploadButtonLabel === 'Retry' &&
    this.uploadProgress > 0 &&
    this.uploadProgress < 100
      ? this.retryPimUpload()
      : this.manualUpload
      ? this.manualUploadBatch()
      : this.pimUploadBatch();
  };

  showMessage = (msg: string, duration: number = 5000): void => {
    this.matSnackbar
      .open(msg, 'OK', {
        duration,
        horizontalPosition: 'right',
        verticalPosition: 'bottom',
      })
      .onAction()
      .subscribe(() => this.matSnackbar.dismiss());
  };

  /* Manual Upload
     1. upload main file
     2. upload additional files
     3. upload form details
  */
  manualUploadBatch() {
    this.isFileServiceBusy = true;
    this.uploadBatchFormValues = this.multiUploadForm.value;
    this.multiUploadForm.controls['batchName'].disable();
    this.multiUploadForm.controls['inputFormat'].disable();
    this.multiUploadForm.controls['outputFormat'].disable();
    this.multiUploadForm.controls['reference'].disable();
    this.multiUploadForm.controls['description'].disable();
    this.getUUID('manual');
  }

  pimUploadBatch() {
    this.isFileServiceBusy = true;
    this.getUUID('pim');
  }

  getUUID(mode) {
    this.fileUploadService
      .getUUID(this.subscriptionId, this.supplier_code, this.supplier_name)
      .subscribe({
        next: (resp) => {
          this.uuid = resp['result']['uuid'];
          if (mode && mode === 'manual') {
            this.uploadMainFile(this.mainFile[0]);
          }
          if (mode && mode === 'pim') {
            this.uploadPimfile();
          }
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  }

  uploadMainFile(file) {
    this.fileUploadService
      .uploadData(
        this.subscriptionId,
        this.supplier_code,
        this.channel,
        this.uploadBatchFormValues.batchName,
        this.uploadBatchFormValues.inputFormat,
        this.uploadBatchFormValues.outputFormat,
        this.mainFile[0],
        true,
        this.uuid,
        this.supplier_name
      )
      .subscribe({
        next: (progress) => {
          this.uploadProgress = Math.round(progress);
          if (progress && progress === 100) {
            const fileType = file.name.split('.').pop();
            if (
              fileType === 'csv' ||
              fileType === 'xlsx' ||
              fileType === 'xls'
            ) {
              this.validateMainFile();
            } else {
              if (this.additionalFiles.length > 0) {
                this.uploadAdditionalFiles();
              } else {
                this.completeUpload();
              }
            }
          }
        },
        error: (error) => {
          this.retryMainFile = true;
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
          this.sidepanel.setShowNav(false);
        },
      });
  }
  retryMainFile = false;

  validateMainFile() {
    this.fileUploadService
      .validateMainFile(
        this.subscriptionId,
        this.supplier_code,
        this.channel,
        this.uploadBatchFormValues.inputFormat,
        this.uploadBatchFormValues.outputFormat,
        this.uuid,
        this.supplier_name
      )
      .subscribe({
        next: (resp) => {
          if (resp['status'] === 'success') {
            if (this.additionalFiles.length > 0) {
              this.uploadAdditionalFiles();
            } else {
              this.completeUpload();
            }
          }
        },
        error: (error) => {
          this.enableSidePanel('manual');
          if (navigator.onLine) {
            this.showMessage(error['error']['detail'], 0); // msg from api
          } else {
            this.showMessage('Error uploading the batch');
          }
        },
      });
  }

  validatePimFile() {
    this.fileUploadService
      .validateMainFile(
        this.subscriptionId,
        this.supplier_code,
        this.channel,
        this.uploadBatchFormValues.uploadInput,
        this.uploadBatchFormValues.uploadOutput,
        this.uuid,
        this.supplier_name
      )
      .subscribe({
        next: (resp) => {
          if (resp['status'] === 'success') {
            // upload complete API
            this.fileToUpload = undefined;
            this.files = [];
            this.fileUploadStatus = 'Uploaded';
            this.dropzoneLabel = 'Drag or Drop your files here';
            this.uploadButtonLabel = 'Upload';
            // this.uploadForm.reset();
            this.isUpload = true;
            this.completePimUpload();
          }
        },
        error: (error) => {
          this.enableSidePanel('pim');
          if (navigator.onLine) {
            this.showMessage(error['error']['detail'], 0); // msg from api
          } else {
            this.showMessage('Error uploading the batch');
          }
        },
      });
  }

  completePimUpload() {
    this.fileUploadService
      .uploadCompelte(
        this.channel,
        this.uploadBatchFormValues.uploadname,
        this.uploadBatchFormValues.uploadInput,
        this.uploadBatchFormValues.uploadOutput,
        this.fileToUpload,
        '',
        '',
        this.uuid
      )
      .subscribe({
        next: (resp) => {
          this.info_log = resp?.body;
          //  this.uploadForm.reset();
          // this.resetUploadpanel();
        },
        error: (error) => {
          this.resetUploadpanel();
          if (navigator.onLine) {
            this.showMessage(error['error']['detail'], 0); // msg from api
          } else {
            this.showMessage('Error uploading the batch');
          }
        },
        complete: () => {
          this.fileToUpload = undefined;
          this.files = [];
          this.fileUploadStatus = 'Uploaded';
          this.dropzoneLabel = 'Drag or Drop your files here';
          this.uploadButtonLabel = 'Upload';
          this.showMessage('Batch uploaded successfully');
          // this.uploadForm.reset();
          this.isUpload = false;

          this.getCount(
            this.subscriptionId,
            this.supplier_code,
            this.search,
            this.start_date,
            this.end_date
          );
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
          this.resetUploadpanel();
        },
      });
  }

  enableSidePanel(mode) {
    this.isFileServiceBusy = false;
    this.uploadProgress = null;
    this.uuid = '';
    if (mode && mode === 'manual') {
      this.multiUploadForm.controls['batchName'].enable();
      this.multiUploadForm.controls['inputFormat'].enable();
      this.multiUploadForm.controls['outputFormat'].enable();
      this.multiUploadForm.controls['reference'].enable();
      this.multiUploadForm.controls['description'].enable();
    }
  }

  completeUpload() {
    this.fileUploadService
      .uploadCompelte(
        this.channel,
        this.uploadBatchFormValues.batchName,
        this.uploadBatchFormValues.inputFormat,
        this.uploadBatchFormValues.outputFormat,
        '',
        this.uploadBatchFormValues.reference,
        this.uploadBatchFormValues.description,
        this.uuid
      )
      .subscribe({
        next: (resp) => {},
        error: (error) => {
          this.resetUploadpanel();
          if (navigator.onLine) {
            this.showMessage(error['error']['detail'], 0); // msg from api
          } else {
            this.showMessage('Error uploading the batch');
          }
        },
        complete: () => {
          this.isFileServiceBusy = false;
          this.showMessage('Batch uploaded successfully');
          this.getCount(
            this.subscriptionId,
            this.supplier_code,
            this.search,
            this.start_date,
            this.end_date
          );
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
          this.resetUploadpanel();
        },
      });
  }

  uploadAdditionalFiles() {
    if (this.fileCounter < this.additionalFiles.length) {
      this.sequentiallyUpload(this.fileCounter);
    }
  }

  sequentiallyUpload(index) {
    this.fileUploadService
      .uploadData(
        this.subscriptionId,
        this.supplier_code,
        this.channel,
        this.uploadBatchFormValues.batchName,
        this.uploadBatchFormValues.inputFormat,
        this.uploadBatchFormValues.outputFormat,
        this.additionalFiles[index],
        false,
        this.uuid,
        this.supplier_name
      )
      .subscribe({
        next: (progress) => {
          this.additionalFilesData[index]['progress'] = Math.round(progress);
          if (progress && progress === 100) {
            if (this.fileCounter === this.additionalFiles.length - 1) {
              this.completeUpload();
            } else {
              this.fileCounter++;
              this.uploadAdditionalFiles();
            }
          }
        },
        error: (error) => {
          this.additionalFilesData[index]['status'] = 'failed';
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
        },
      });
  }

  uploadPimfile = () => {
    this.err_info = false;
    this.uploadProgress = 0;
    this.uploadBatchFormValues = this.uploadForm.value;
    this.fileUploadService
      .uploadData(
        this.subscriptionId,
        this.supplier_code,
        this.channel,
        this.uploadBatchFormValues.uploadname,
        this.uploadBatchFormValues.uploadInput,
        this.uploadBatchFormValues.uploadOutput,
        this.fileToUpload,
        true,
        this.uuid,
        this.supplier_name
      )
      .subscribe({
        next: (progress) => {
          this.uploadProgress = progress;
          if (this.uploadProgress === 100) {
            const fileType = this.fileToUpload.name.split('.').pop();
            if (
              fileType === 'csv' ||
              fileType === 'xlsx' ||
              fileType === 'xls'
            ) {
              this.validatePimFile();
            } else {
              this.completePimUpload();
            }
          }
        },
        error: (error) => {
          // this.info_log = error?.error?.detail;
          // this.fileUploadStatus = 'Failed';
          // this.uploadButtonLabel = 'Retry';
          // this.err_info = true;
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
          this.sidepanel.setShowNav(false);
        },
      });
  };

  retryPimUpload = () => {
    this.fileUploadService.resumeFileUpload().subscribe({
      next: (progress) => {
        this.uploadProgress = progress;
        if (this.uploadProgress === 100) {
          this.fileToUpload = undefined;
          this.files = [];
          this.fileUploadStatus = 'Uploaded';
          this.dropzoneLabel = 'Drag or Drop your files here';
          this.uploadButtonLabel = 'Upload';
          this.showMessage('Upload Complete');
          this.err_info = false;
          this.uploadForm.reset();
        }
      },
      error: (error: HttpErrorResponse) => {
        this.fileUploadStatus = 'Failed';
        this.uploadButtonLabel = 'Retry';

        this.err_info = false;
        this.showMessage(`Couldn't resume upload, Please try again!`);
        this.resetUploadpanel();
      },
    });
  };

  resumeMainFile(isMain, index?) {
    this.retryMainFile = false;
    this.fileUploadService
      .resumeManualFileUpload(
        isMain ? true : index,
        index,
        isMain ? this.mainFile[0] : this.additionalFiles[index]
      )
      .subscribe({
        next: (progress) => {
          this.uploadProgress = Math.round(progress);

          if (progress && progress === 100) {
            if (this.additionalFiles.length > 0) {
              this.uploadAdditionalFiles();
            } else {
              this.completeUpload();
            }
          }
        },
        error: (error) => {
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
        },
      });
  }

  resumeAdditionalFile(index) {
    this.additionalFilesData[index]['status'] = 'resume';
    this.fileUploadService
      .resumeManualFileUpload(false, index, this.additionalFiles[index])
      .subscribe({
        next: (progress) => {
          this.additionalFilesData[index]['progress'] = Math.round(progress);
          if (progress && progress === 100) {
            if (this.fileCounter === this.additionalFiles.length - 1) {
              this.completeUpload();
            } else {
              this.fileCounter++;
              this.uploadAdditionalFiles();
            }
          }
        },
        error: (error) => {
          this.showMessage(JSON.stringify(error.error.detail) || 'Error uploading file, Retry!');
        },
      });
  }

  viewAllBrandsPanelToggle = (brandListValue, supplier_brandValue) => {
    this.brandList = brandListValue;
    this.supplier_brand = supplier_brandValue;
    this.uploadBatch = false;
    this.batchLog = false;
    this.addTag = false;
    this.manualUpload = false;
    this.pimUpload = false;
    this.viewAllBrands = true;

    this.sidepanel.setShowNav(true);
  };

  /**
   * add label
   */
  addLabel = (addlabel_id, removelabel_id) => {
    this.homeService
      .addLabel(
        this.subscriptionId,
        this.currentBatch,
        addlabel_id,
        removelabel_id
      )
      .subscribe({
        next: (resp) => {
          this.sidepanel.setShowNav(false);
          this.snackbarService.openSnackBar(resp.detail, 'OK');

          this.getCount(
            this.subscriptionId,
            this.supplier_code,
            this.search,
            this.start_date,
            this.end_date
          );

          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * Remove label for batch
   * @param batch_id
   * @param removelabel_id
   * @param addlabel_id
   */
  removeLabel = (batch_id, removelabel_id, addlabel_id) => {
    this.homeService
      .addLabel(this.subscriptionId, batch_id, addlabel_id, removelabel_id)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * closeSidepanel
   */
  closeSidepanel = () => {
    this.sidepanel.setShowNav(false);
    this.resetTagSelected();
    this.createLabelForm.reset();
    this.err_info = false;
  };
  uploadOutput;
  uploadInput;
  uploadname;

  resetUploadpanel = () => {
    this.IOvalues = false;
    this.err_info = false;
    // this.selectedSupplierUpload = '';
    this.uploadname = '';
    this.uploadProgress = 0;
    this.uploadInput = '';
    this.uploadOutput = '';
    this.files = [];
    this.mainFile = [];
    this.additionalFiles = [];
    this.additionalFilesData = [];
    this.uploadForm.reset();
    this.multiUploadForm.reset();
    this.multiUploadForm.controls['batchName'].enable();
    this.multiUploadForm.controls['inputFormat'].enable();
    this.multiUploadForm.controls['outputFormat'].enable();
    this.multiUploadForm.controls['reference'].enable();
    this.multiUploadForm.controls['description'].enable();
    this.fileCounter = 0;
    this.isFileServiceBusy = false;
    this.description = false;
    this.uuid = '';
    this.fileUploadService.allSignedURLs.main = '';
    this.fileUploadService.allSignedURLs.additional = [];
    this.fileUploadService.additionalFileList = [];
    if (this.mainFileInput) {
      this.mainFileInput.nativeElement.value = '';
    }
    if (this.additionalFileInput) {
      this.additionalFileInput.nativeElement.value = '';
    }
    this.sidepanel.setShowNav(false);
  };

  description: boolean = false;
  additionalFiles = [];

  /**
   * on file select
   * @param e
   */
  onAdditionalFileSelect(e) {
    const selectedFiles = e.addedFiles;
    const fileLimit = 5;
    selectedFiles.forEach((file, index) => {
      if (file.size == 0) {
        this.showMessage(`File can't be empty`);
        selectedFiles.splice(index, 1);
      }
    });
    // // scenario when selecting more than 5 files in one go
    if (selectedFiles.length > 5) {
      this.showMessage('Limit exceeded, files will be truncated');
    }
    this.pushFiles(selectedFiles.length, selectedFiles);
  }

  pushFiles = (limit, selectedFiles) => {
    for (let i = 0; i < limit; i++) {
      const data = {
        name: selectedFiles[i].name,
        type: selectedFiles[i].type,
        status: 'loaded',
        progress: 0,
        index: i,
        size: selectedFiles[i].size,
      };
      // secenario when selecting files one by one
      if (data.size > 0) {
        if (this.additionalFilesData.length < 5) {
          this.additionalFilesData.push(data); //data for UI
          this.additionalFiles.push(selectedFiles[i]); // files to upload
        } else {
          this.showMessage('Limit exceeded, files will be truncated');
        }
      } else {
        this.showMessage(
          'The file ' +
            this.trimFileName(data.name, 10) +
            " is empty, hence it won't be added"
        );
      }
    }
  };

  /***
   * trim file name, keeping extension intact
   */
  trimFileName = (str, noOfChars) => {
    let nameArray = str.split('.');
    let fileType = `.${nameArray.pop()}`;
    let fileName = nameArray.join(' ');
    if (fileName.length >= noOfChars) {
      fileName = fileName.substr(0, noOfChars) + '...';
    }
    return fileName + fileType;
  };

  clearAdditionalFile(i) {
    this.additionalFilesData.splice(i, 1);
    this.additionalFileInput.nativeElement.value = '';
    this.additionalFiles.splice(i, 1);
  }

  addDescription = (val) => {
    val == true ? (this.description = true) : (this.description = false);
  };

  // main file selection
  onMainFileSelect(e) {
    if (e.target.files[0].size !== 0) {
      this.mainFile.push(e.target.files[0]);
      this.setBatchName();
    } else {
      this.showMessage(`File can't be empty`);
      this.mainFileInput.nativeElement.value = '';
    }
  }

  setBatchName() {
    this.uploadname = this.mainFile[0]['name'].replace(/\.[^/.]+$/, '');
  }

  clearMainFile() {
    this.mainFile = [];
    this.uploadname = '';
    this.mainFileInput.nativeElement.value = ''; // allow to select the same file
  }
  /**
   * File select
   * @param event
   */
  onSelect = (event) => {
    if (this.files.length < 1) {
      // console.log(event)
      if (event?.addedFiles[0]?.size == 0) {
        this.showMessage(`File can't be empty`);
      } else {
        this.fileToUpload = event?.addedFiles ? event.addedFiles[0] : undefined;
        event?.addedFiles && this.files.push(...event?.addedFiles);
      }
    } else {
      this.onRemove(event);
    }
    this.isUpload = false;
  };
  /**
   * remove file selected
   * @param event
   */
  onRemove = (event) => {
    this.files.splice(this.files.indexOf(event), 1);
    this.fileToUpload = null;
    this.onSelect(event.hasOwnProperty('addedFiles') ? event : null);
  };

  /**
   * Tage color
   * @param i
   * @param item
   */
  toggleTagColor = (i, item) => {
    this.labelColor = item;
  };

  resetTagSelected = () => {
    this.labelColor = null;
    this.textColor = null;
  };

  /**
   * Tag Text Color
   * @param i
   * @param item
   */
  toggleTagtextColor = (i, item) => {
    this.textColor = item;
  };

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = null), (this.customEndDate = null);
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }
    this.page = 1;
    this.size = 50;
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };
  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (dateRangeStart, dateRangeEnd) => {
    if (moment(dateRangeStart).isValid() && moment(dateRangeEnd).isValid()) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart).format('YYYY-MM-DD');
      this.ed = moment(dateRangeEnd).format('YYYY-MM-DD');
      this.page = 1;
      this.size = 50;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
    }
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  /**
   * search filter
   * @param event
   */
  getSearchValue = (event) => {
    this.search = event.trim();
    this.page = 1;
    this.size = 50;
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
    this.paginator.firstPage();
  };

  /**
   * product filters
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedSupplier,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    this.selectedSupplier.forEach((element) => {
      this.searchedMultipleVals.push(element.supplier_code);
    });
    this.label = this.searchedMultipleVals;
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  resetFilters = () => {
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.status = 'in_queue';
    this.start_date = '';
    this.end_date = '';
    this.customStartDate = null;
    this.customEndDate = null;
    this.searchedMultipleVals = [];
    this.filterObj = [];
    this.selectedSupplier = '';
    this.label = '';
    this.selected = 'recent';
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  resetSearch = () => {
    this.search = '';
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  openDialog = (batch_id) => {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = true;
    dialogConfig.data = {
      sub_id: this.subscriptionId,
      batch_id: batch_id,
    };

    const dialogRef = this.dialog.open(
      SupplierRejectDialogComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
      // console.log(result)
      if (result.event == 'close') {
        this.rejectBatch(batch_id, result.data);
      }
    });
  };

  rejectBatch = (batch_id, comment?) => {
    this.homeService
      .rejectBatch(this.subscriptionId, batch_id, comment)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getCount(
            this.subscriptionId,
            this.supplier_code,
            this.search,
            this.start_date,
            this.end_date
          );
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  batchActions = (batch_id, action) => {
    this.homeService.action(this.subscriptionId, batch_id, action).subscribe({
      next: (resp) => {
        this.matSnackbar.open(resp.detail, 'OK', {
          duration: 3000,
        });
        this.getCount(
          this.subscriptionId,
          this.supplier_code,
          this.search,
          this.start_date,
          this.end_date
        );
        this.getTable(
          this.page,
          this.size,
          this.status,
          this.search,
          this.start_date,
          this.end_date,
          this.label,
          this.subscriptionId,
          this.supplier_code
        );
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   *
   * @param subscription_id
   */
  getSupplierList = (subscription_id) => {
    this.homeService.getSupplierList(subscription_id).subscribe({
      next: (resp) => {
        this.filterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * update batch status
   * @param module_slug
   */
  createLabel = () => {
    this.createlabelFormValues = this.createLabelForm.value;
    this.homeService
      .createLabel(
        this.subscriptionId,
        this.createlabelFormValues.name,
        this.createlabelFormValues.title,
        this.labelColor,
        this.textColor
      )
      .subscribe({
        next: (resp) => {
          this.labelList = resp.result;
          this.createLabelForm.reset();
          this.resetTagSelected();
          this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.getTagList(this.subscriptionId);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * Get module table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginationEvent = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  /**
   * Jump pages
   * @param event
   */
  goToPage = (event) => {
    this.page = event;
    this.batchDataSource = [];
    this.tableLoading = true;
    this.getCount(
      this.subscriptionId,
      this.supplier_code,
      this.search,
      this.start_date,
      this.end_date
    );
    this.getTable(
      this.page,
      this.size,
      this.status,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.subscriptionId,
      this.supplier_code
    );
  };

  /**
   * get list of labels
   * @param module_slug
   */
  getCount = (subscription_id, supplier_code, q, start_date, end_date) => {
    this.homeService
      .getStatusCount(
        subscription_id,
        this.supplier_code,
        q,
        start_date,
        end_date,
        this.supplier_name
      )
      .subscribe({
        next: (resp) => {
          this.headercount = resp.result;
          // go to the first tab which has the data
          this.tabIndexWithData = this.tabs.findIndex(
            (tab) => this.headercount[tab] > 0
          );
          this.selectedIndex = this.tabIndexWithData;
          this.status = this.tabs[this.tabIndexWithData];
          this.selectedTab = this.tabs[this.tabIndexWithData];
          this.getTable(
            this.page,
            this.size,
            this.status,
            this.search,
            this.start_date,
            this.end_date,
            this.label,
            this.subscriptionId,
            this.supplier_code
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${HttpResponse.error.detail}`,
            'OK'
          );
        },
      });
  };

  /**
   * Batch table
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param label
   * @param subscription_id
   * @param supplier_code
   */
  getTable = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    label,
    subscription_id,
    supplier_code
  ) => {
    this.tableLoading = true;
    this.homeService
      .getBatchList(
        page,
        size,
        status,
        search,
        start_date,
        end_date,
        label,
        subscription_id,
        this.supplier_code,
        this.supplier_name
      )
      .subscribe({
        next: (resp) => {
          this.batchList = resp.result;
          const HOME_DATA: BatchTableList[] = this.batchList;

          this.batchDataSource = new MatTableDataSource<BatchTableList>(
            HOME_DATA
          );

          this.totalItems = resp.total_items;
          this.size = resp.page_size;
          this.page = resp.page;
          this.total_pages = resp.total_pages;
          this.dataLoading = false;
          this.tableLoading = false;
          // this.getLabelList(subscription_id);
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableLoading = false;
          this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', {
            duration: 3000,
          });
        },
      });
  };

  /**
   * Table Tab list
   * @param subscription_id
   */
  getTagList = (subscription_id) => {
    this.homeService.getTagList(subscription_id).subscribe({
      next: (resp) => {
        this.tagList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * on successful copy to clipboard
   */
  showSnackbar = () => {
    this.matSnackbar.open('Copied!', 'OK', {
      duration: 3000,
      horizontalPosition: 'left',
      verticalPosition: 'bottom',
    });
  };

    /**
   * get tag delete confirmation
   * @param labelIdToBeRemoved 
   * @param labelName 
   */
    getDeleteConfirmation = (labelIdToBeRemoved, labelName) => {
      const dialogRef = this.dialog.open(DeleteTagComponent, {
        data: { name: labelName },
        width: '600px'
      });
      dialogRef.afterClosed().subscribe(canDelete => {
        console.log(canDelete)
        if(canDelete) {
          this.deleteTag(labelIdToBeRemoved)
        }
      });
  
    }
  
  
    /**
     * delete tag 
     * @param labelIdToBeRemoved 
     */
    deleteTag = (labelIdToBeRemoved) => { 
      console.log(labelIdToBeRemoved)
      this.homeService.deleteTag(this.subscriptionId, labelIdToBeRemoved).subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(
            `${resp.detail}`,
            'OK'
          );
          this.getTagList(this.subscriptionId)
        }, 
        error: (error: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(
            `${error.error.detail}`,
            'OK'
          );
        }
      })
    }
}
