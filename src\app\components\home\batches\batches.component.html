<div class="wrapper" fxLayout="column">
  <!-- Banner -->
  <!-- <div class="config-banner"> -->
  <div
    *ngIf="batchSelectionModel.selected?.length == batchList?.length && batchSelectionModel.selected.length < totalItems"
    class="banner-text" fxLayout="row" fxLayoutAlign="center center">
    <span>All
      {{!isGlobalSelect ? batchSelectionModel.selected.length + ' batches on this page are selected.' : totalItems + ' batches are selected.'}}
      <u
        (click)="isGlobalSelect = !isGlobalSelect; !isGlobalSelect ? batchSelectionModel.clear():onGlobalSelect()">{{!isGlobalSelect ? 'Select all '+ totalItems + ' batches': 'Clear selection'}}</u></span>
  </div>
  <!-- </div> -->

  <div class="filter-container" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-between center">
    <!-- <div class="back-icon">
      <mat-icon [routerLink]="['/' + routeFrom]" [queryParams]="{ sub: subscriptionId }">west</mat-icon>
    </div>
    <div class="selected-batch">
      <span>{{ supplier_name }} ({{ supplier_code }})</span>
    </div> -->
    <!-- search box -->
    <mat-form-field appearance="none" class="search-filter">
      <input [disabled]="tableLoading" id="search" matInput placeholder="Search by Batch Id, Supplier name or code"
        #searchVal name="searchVal" [(ngModel)]="search" (keydown.enter)="getSearchValue(search)" />
      <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)">search</mat-icon>
      <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
    </mat-form-field>
    <!-- supplier dropdown -->
    <!-- <mat-form-field appearance="none" class="app-dropdown">
      <mat-select placeholder="All Suppliers" [(ngModel)]="selectedSupplier" (ngModelChange)="getProductSelection()"
        name="selectedProducts" multiple>
        <mat-option #mulVal *ngFor="let mulVals of filterList" [value]="mulVals">{{ mulVals.supplier_name }}
        </mat-option>
      </mat-select>
    </mat-form-field> -->
    <!-- date range dropdown-->
    <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
      <mat-form-field appearance="none" class="date-range-filter">
        <mat-select appClickDebounce [debounceTime]="1000" (debounceClick)="getDataForDate($event.value)"
          [typeaheadDebounceInterval]="1000" [disabled]="tableLoading" [(value)]="selected">
          <div *ngFor="let option of datePickerOptions">
            <!-- Recent -->
            <mat-option *ngIf="option.value === 'recent'" value="recent">
              <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                <span> Recent</span> &nbsp;
                <span class="date-range"> </span>
              </div>
            </mat-option>
            <!-- Last Week / Month / Quarter -->
            <mat-option [value]="option.value" *ngIf="
                option.value !== 'recent' && option.value !== 'custom_range'
              ">
              <div fxLayout="column" class="range-category">
                {{ option.display }}
                <span class="date-range">
                  {{ option.start_date | date: "mediumDate" }} -
                  {{ currentDate | date: "mediumDate" }}</span>
              </div>
            </mat-option>
            <!-- Custom range  -->
            <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()">
              <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                <span>Custom Range</span>
                <span class="date-range" *ngIf="customStartDate && customEndDate">
                  {{ customStartDate | date: "mediumDate" }} -
                  {{ customEndDate | date: "mediumDate" }}</span>
                <span fxLayout style="margin: 0 0 0 8px">
                  <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate" style="display: none">
                    <input matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                    &nbsp;
                    <input matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                        dateRangeChange(customStartDate, customEndDate)
                      " />
                  </mat-date-range-input>
                  <!-- date picker -->
                  <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                  <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                </span>
              </div>
            </mat-option>
          </div>
        </mat-select>
        <div class="date-range-icon">
          <img src="assets/images/home-icons/calendar.svg" />
        </div>
      </mat-form-field>
    </div>
    <!-- supplier filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select [typeaheadDebounceInterval]="1000" appClickDebounce [debounceTime]="1000"
        (debounceClick)="getProductSelection()" [disabled]="tableLoading" placeholder="All Suppliers"
        [(ngModel)]="supplier_code" name="selectedProducts" multiple>
        <mat-option #mulVal *ngFor="let mulVals of filterList" [value]="mulVals.supplier_code">
          {{ mulVals.supplier_name }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- region filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select [disabled]="tableLoading" placeholder="Region" [typeaheadDebounceInterval]="1000" appClickDebounce [debounceTime]="1000"
      (debounceClick)="getDataBasedOnRegion()" [(ngModel)]="selectedRegions" multiple>
        <mat-option *ngFor="let each of regions" [value]="each">{{ each }}
        </mat-option>
      </mat-select>
    </mat-form-field>


    <!-- status filter [def:completed]-->
    <mat-form-field appearance="none" class="app-dropdown status-filter">
      <mat-select appClickDebounce [debounceTime]="1000" (debounceClick)="tabChanged()"
        [typeaheadDebounceInterval]="1000" [disabled]="tableLoading" placeholder="Status" [(ngModel)]="selectedStatus"
        multiple>
        <mat-option *ngFor="let each of filterStatusList" [value]="each.slug">{{ each.display_name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <!-- upload file -->
    <!-- <div>
      <button *ngIf="supplier_code.length == 1" class="upload-btn filled-btn-primary" [matMenuTriggerFor]="module_status" #t="matMenuTrigger"
        mat-stroked-button fxLayout="row" fxLayoutAlign="space-between center">
        <span style="margin-right: 20px">Upload New Batch</span>
        <img [src]="
            t.menuOpen
              ? 'assets/images/home-icons/drop.svg'
              : 'assets/images/home-icons/down-arrow-white.svg'
          " />
      </button>
      <button *ngIf="supplier_code.length != 1" disabled class="upload-btn" [matMenuTriggerFor]="module_status" #t="matMenuTrigger"
        mat-stroked-button fxLayout="row" fxLayoutAlign="space-between center">
        <span style="margin-right: 20px">Upload New Batch</span>
        <img [src]="
            t.menuOpen
              ? 'assets/images/home-icons/drop.svg'
              : 'assets/images/home-icons/down-arrow-white.svg'
          " />
      </button>
      <mat-menu #module_status="matMenu">
        <button mat-menu-item *ngFor="let item of chanelList" fxLayout="row" fxLayoutGap="5px"
          (click)="toggleSideNav(item.slug, '', '')">
          <span>{{ item.display_name }}</span>
        </button>
      </mat-menu>
    </div> -->
    <button [disabled]="tableLoading" mat-flat-button color="primary" class="reset-btn" fxLayoutAlign="center center"
      (click)="resetFilters(); selectedIndex = 0">
      Reset
    </button>
    <div class="messages-icon" fxFlex fxLayoutAlign="start center">
      <!-- <div class="dot"></div> -->
      <img matTooltip="Comments" matTooltipPosition="above" [routerLink]="['/comments']"
        [queryParams]="{ sub: subscriptionId, origin: '/home' }" src="assets/images/home-icons/message.svg" />
    </div>
  </div>

  <!-- table begins here -->
  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">

    <div class="stats-container" fxLayout="row" fxLayoutAlign=" center" fxLayoutGap="20px">
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_suppliers }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of suppliers with at least one batch" matTooltipPosition="above" />
            <!-- Number of suppliers with at least one completed batch -->
          </div>
          <span class="card-description"> Total Suppliers </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_batches }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg" matTooltip="Total number of batches"
              matTooltipPosition="above" />
            <!-- Number of batches processed -->
          </div>
          <span class="card-description"> Total Batches </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of SKUs processed across all batches" matTooltipPosition="above" />
            <!-- Number of SKUs processed across all batches -->
          </div>
          <span class="card-description"> {{retailer_name == 'netplus_alliance' ? 'Total Submitted' : "Total SKU's processed"}} </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_new_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of new SKUs which doesn't exist in the PIM" matTooltipPosition="above" />
            <!-- Number of new SKUs which doesn't exist in the retailer PIM -->
          </div>
          <span class="card-description"> {{retailer_name == 'netplus_alliance' ? 'Total Imported' : "Total New SKUs"}} </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_error_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of processed SKUs that have errors" matTooltipPosition="above" />
            <!-- Number of processed SKUs which have errors -->
          </div>
          <span class="card-description"> {{retailer_name == 'netplus_alliance' ? 'Total Rejected' : "Total Error SKUs"}} </span>
        </div>
      </mat-card>
    </div>

    <div fxLayout="row" fxLayoutAlign="space-between center">
      <!-- send to module -->
      <div class="send-to-btn" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
        <button [disabled]="(selectedStatus.length ==1 && (selectedStatus[0] == 'in_queue' || selectedStatus[0] == 'published' || selectedStatus[0] == 'approved')) ? true :
      (!batchSelectionModel.selected.length || tableLoading)" (click)="moveBatchTo('approved')" mat-flat-button
          color="primary">Move to approved</button>
        <button [disabled]="(selectedStatus.length ==1 && (selectedStatus[0] == 'approved')) ? true :
      (!batchSelectionModel.selected.length || tableLoading)" (click)="moveBatchToCancel('cancelled')" mat-flat-button
          color="primary">Move to cancelled</button>
        <button [disabled]="(selectedStatus.length ==1 && (selectedStatus[0] == 'in_queue' || selectedStatus[0] == 'completed') ) ? true :
      (!batchSelectionModel.selected.length || tableLoading)" (click)="moveBatchTo('published')" mat-flat-button
          color="primary">Move to published</button>
        <button [disabled]="(selectedStatus.length ==1 && (selectedStatus[0] != 'in_queue') ) ? true :
        (!batchSelectionModel.selected.length || tableLoading)" (click)="moveBatchTo('completed')" mat-flat-button
            color="primary">Move to completed</button>
      </div>
      <button [disabled]="tableLoading || batchList.length == 0" (click)="exportTable()" mat-flat-button
          color="primary" matTooltip="Table data in CSV format will be sent to logged email"
          matTooltipPosition="above">Export Table</button>
    </div>
    

    <!-- <mat-tab-group #tabGroup (selectedTabChange)="tabChanged($event)" [selectedIndex]="selectedIndex"> -->
    <!-- <mat-tab [label]="tabHeader" *ngFor="let tabHeader of tabs"> -->
    <!-- <ng-template mat-tab-label>
          <span>{{ tabHeader | underscoreAsSpace | titlecase }} </span>&nbsp;
          <span class="batch-count" fxLayoutAlign="center center">
            {{ headercount && headercount[tabHeader] }}</span>
        </ng-template> -->
    <div class="table-section">
      <!-- table -->
      <table mat-table [dataSource]="batchDataSource">
        <!-- Position Column -->
        <ng-container matColumnDef="{{ item }}" *ngFor="let item of displayedColumns">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox *ngIf="item == 'select_all'" [disabled]="selectedStatus.length && 
                (selectedStatus[0] == 'cancelled') ||
                (selectedStatus[0] == 'published')
                ? true : (tableLoading || batchList.length == 0)"
              [checked]="batchList?.length && batchSelectionModel.selected.length == batchList.length"
              (change)="toggleAll()">
            </mat-checkbox>
            <span *ngIf="item != 'select_all'" [ngStyle]="item == 'Batch_ID' ? {'padding-left':'10px'}:''">
              {{ item | underscoreAsSpace }}
            </span>
          </th>

          <!-- [routerLink]="['/home/<USER>/batch-activities']" [queryParams]="{
                sub: subscriptionId,
                supplier_code: element.supplier_code,
                supplier_name: element.supplier_name,
                batch_id: element.batch_id
              }" -->

          <td mat-cell *matCellDef="let element">

            <div class="select-batch" *ngIf="item == 'select_all'" fxLayout="column" fxLayoutAlign="center start">
              <mat-checkbox [checked]="batchSelectionModel.isSelected(element)" [disabled]="selectedStatus.length && 
                    (selectedStatus[0] == 'cancelled') ||
                    (selectedStatus[0] == 'published') 
                    ? true : false" (change)="onBatchToggle(element)">
              </mat-checkbox>
            </div>

            <div class="batch-link" fxLayout="column" *ngIf="item == 'Batch_ID'">
              <div fxLayout="row" fxLayoutAlign="start start">
                <a fxLayout="row" class="active-text" fxLayoutAlign="start center"
                [ngStyle]="{
                  'pointer-events': retailer_name == 'netplus_alliance' ?'none':'auto'
                  }"
                  [routerLink]="['/home/<USER>/batch-activities']" [queryParams]="{
                        sub: subscriptionId,
                        supplier_code: element.supplier_code,
                        supplier_name: element.supplier_name,
                        batch_id: element.batch_id,
                        from: 'home'
                      }">
                  <span class="batch-id" 
                  [ngStyle]="{
                    'text-decoration': retailer_name == 'netplus_alliance' ? 'none' : 'underline'
                  }">{{ element.batch_id }}</span>
                </a>
                <mat-icon matTooltip="Copy" matTooltipPosition="above" fontSet="material-icons-round" class="copy-icon text-theme-primary"
                  (cdkCopyToClipboardCopied)="showSnackbar()" [cdkCopyToClipboard]="element.batch_id">content_copy
                </mat-icon>
              </div>
              <span class="refrence" *ngIf="element.reference" fxLayout="row" fxLayoutAlign="start start"> 
                <span>Ref: {{ element.reference|truncate: 10 }}</span>
                <mat-icon matTooltip="Copy" matTooltipPosition="above" fontSet="material-icons-round" class="copy-icon"
                  (cdkCopyToClipboardCopied)="showSnackbar()" [cdkCopyToClipboard]="element.reference">content_copy
                </mat-icon>
            </span>
              <span class="batch-date"> {{ element.created_at | date }}</span>
            </div>
            <div fxLayout="column" *ngIf="item == 'Supplier Name & Code'">
              <p class="batch-name" style="margin-top: 18px ;" matTooltip="{{ element.supplier_name }}"
                matTooltipPosition="above">
                {{ element.supplier_name }}
              </p>
              <p class="description">
                {{ element.supplier_code }}
              </p>
            </div>

            <div fxLayout="column" *ngIf="item == 'Brand'">
              <span class="batch-name" matTooltip="{{ brand_list }}" matTooltipPosition="above" *ngFor="
                      let brand_list of element.brand | slice: 0:1;
                      let i = index
                    ">
                {{ brand_list }}
              </span>
              <span class="sub-text-id text-theme-primary" *ngIf="
                      element.brand?.length > 1 && element.brand?.length > 0
                    " (click)="
                      viewAllBrandsPanelToggle(
                        element.brand,
                        element.supplier_name
                      )
                    ">
                + {{ element.brand?.length - 1 }} more
              </span>
            </div>

            <div fxLayout="column" *ngIf="item == 'Name & Description'">
              <div class="batch-name-wrapper" fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="4px">
                <span class="batch-name" matTooltip="{{ element.input_file_name }}" matTooltipPosition="above">
                  {{ (element.input_file_name?.length>7)? (element.input_file_name | slice:0:7)+'...':(element.input_file_name) }}
                </span>

                <div class="download-icon" fxLayout="row" style="width: 42px" fxLayoutAlign="space-between start">
                  <!-- download input file -->
                  <a *ngIf="element.input_file_url !== null" [href]="element.input_file_url" download target="_blank">
                    <mat-icon class="download-batch active" matTooltip="Download Input File" matTooltipPosition="above">
                      arrow_downward</mat-icon>
                  </a>&nbsp;
                  <!-- download additional file -->
                  <mat-icon *ngIf="element.additional_files.length > 0" [matMenuTriggerFor]="menu"
                    class="download-batch active" matTooltip="Download Additional Files" matTooltipPosition="above">
                    description</mat-icon>
                  <mat-menu #menu="matMenu">
                    <button *ngFor="let files of element.additional_files" mat-menu-item fxLayout="row">
                      <img style="cursor: pointer" class="additional-file-icon" src="assets/images/file-formats/zip.svg"
                        *ngIf="files.file_type == '.zip'" />
                      <img style="cursor: pointer" class="additional-file-icon" src="assets/images/file-formats/xls.svg"
                        *ngIf="files.file_type == '.csv' || files.file_type == '.xls' || files.file_type == '.xlsx'" />
                      <a class="active-text" [href]="files.file_url" download target="_blank">
                        <span [matTooltip]="files.file_name" matTooltipPosition="above">
                          {{trimFileName(files.file_name, 20)}}</span>
                      </a>
                    </button>
                  </mat-menu>
                </div>


              </div>
                <div fxLayout="row" fxLayoutAlign="start start" fxLayoutGap="4px">
                  <span class="name-description" [matTooltip]="element.description" matTooltipPosition="above"
                  *ngIf="element.description">{{element.description | truncate:40}}</span>
                  <mat-icon *ngIf="element.description" matTooltip="Copy" matTooltipPosition="above" fontSet="material-icons-round" class="copy-icon text-theme-primary" style="margin-top: 4px !important"
                  (cdkCopyToClipboardCopied)="showSnackbar()" [cdkCopyToClipboard]="element.description">content_copy
                  </mat-icon>
                </div>
            </div>

            <div class="tags-container" fxLayout="column" *ngIf="item == 'Tags'">
              <mat-chip-list fxLayout="column">
                <mat-chip class="label-chip" *ngFor="let item of element.tags" [matTooltip]="item.tag"
                  matTooltipPosition="above" [ngStyle]="{
                        'background-color': item.tag_colour,
                        color: item.text_colour
                      }">
                  {{ item.tag | underscoreAsSpace }}
                  <mat-icon class="remove-label-icon" matPrefix (click)="removeLabel(element.batch_id, item.id, '')">
                    close</mat-icon>
                </mat-chip>
              </mat-chip-list>

              <button class="add-label-btn stroked-btn-primary" fxLayoutAlign="center center" (click)="
                      toggleSideNav('addTag', element.batch_id, element.status)
                    ">
                <mat-icon class="add-label-icon text-theme-primary" matPrefix>add</mat-icon>
                Add Tag
              </button>
              <!-- <div
                      *ngFor="let item of element[item]"
                      class="label-chip"
                      fxLayout="row"
                      fxLayoutAlign="start center"
                      [ngStyle]="{ 'background-color': '#F5CEBE' }"
                    >
                      <span>{{ item }}</span>
                    </div> -->
            </div>

            <div fxLayout="column" *ngIf="item == 'Status'">
              <p class="batch-name" matTooltip="{{ element.status }}" matTooltipPosition="above">
                {{ element.status | underscoreAsSpace | titlecase }}
              </p>
            </div>

            <div fxLayout="column" *ngIf="item == 'Total_Rows'" class="table-data">
              <span>{{retailer_name == 'netplus_alliance' ? 'Total Submitted' : "Total rows"}}: {{ element.total_rows }}</span>
              <span *ngIf="retailer_name != 'netplus_alliance'">Maintenance: {{ element.maintenance_rows }}</span>
              <span>{{retailer_name == 'netplus_alliance' ? 'Total Imported' : "New"}}: {{ element.new_rows }}</span>
              <span *ngIf="retailer_name != 'netplus_alliance'">In process: {{ element.inprogress }}</span>
              <span class="err-count">{{retailer_name == 'netplus_alliance' ? 'Total Rejected' : "Errors"}} : {{ element.errors }}</span>
            </div>

            <div fxLayout="column" *ngIf="item == 'Last_Updated'">
              <p>{{ element.updated_at | date }}</p>
              <p class="description">
                {{ element.time }}
              </p>
            </div>

            <div fxLayout="column" *ngIf="item == 'Actions'" fxLayoutAlign="center center">
              <div *ngIf="selectedTab == 'in_progress'" class="progress-wrapper" fxLayout="row" fxLayoutGap="5px">
                <!-- <mat-progress-bar mode="determinate" [value]="element[item]" fxFlex="90" [color]="'accent'">
                    </mat-progress-bar>
                    <p class="progress-bar-value" fxFlex="10">
                      {{ element[item] }}%
                    </p> -->
                <p class="text-theme-primary">Processing</p>
              </div>
              <!-- <div fxLayoutAlign="start center" style="margin-bottom: 10px;" *ngIf="
                      element.status == 'in_queue' &&
                      pagePermissions['cancel_batch']
                    " (click)="openDialog(element.batch_id)">
                <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/cancel-icon.svg"
                  class="svg_icon" />
              </div> -->
              <button *ngIf="
                element.status == 'in_queue' &&
                pagePermissions['cancel_batch']
                " class="reject-btn" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                (click)="openDialog(element.batch_id)">
                    Reject
              </button>

              <div *ngIf="element.status == 'published'" fxLayoutAlign="start center" class="batch-published" fxLayoutGap="4px">
                <span>Published</span>
                <!-- download input file -->
                 <div class="download-icon">
                  <a *ngIf="element.output_file_url !== null" [href]="element.output_file_url" download
                  target="_blank">
                  <mat-icon class="download-batch published" matTooltip="Download Output File" matTooltipPosition="above">arrow_downward</mat-icon>
                  </a>
                </div>
              </div>
              <p *ngIf="element.status == 'cancelled'" fxLayoutAlign="center center" class="batch-cancelled">
                Cancelled
              </p>
              <div fxLayout="column" fxLayoutGap="10px" class="action-btn">
                <button *ngIf="
                        element.status == 'completed' &&
                        pagePermissions['approve_batch']
                      " class="filled-btn-primary approve-btn" fxLayoutAlign="center center" mat-button
                  fxFlexAlign="center" (click)="batchActions(element.batch_id, 'approve')">
                  Approve
                </button>
                <button *ngIf="
                        element.status == 'completed' &&
                        pagePermissions['approve_batch_with_errors']
                      " class="approve-with-errs stroked-btn-primary" fxLayoutAlign="center center" mat-button
                  fxFlexAlign="center" (click)="
                        batchActions(element.batch_id, 'approve with errors')
                      ">
                  Approve with Errors
                </button>
                <button *ngIf="
                    element.status == 'in_queue'
                      " class="filled-btn-primary complete-btn" fxLayoutAlign="center center" mat-button
                  fxFlexAlign="center" (click)="
                        batchActions(element.batch_id, 'completed')
                      ">
                  Complete
                </button>
                <a *ngIf="
                        (element.status == 'approved' ||
                        element.status == 'completed') &&
                        pagePermissions['download_batch'] &&
                        element.output_file_url
                      " [href]="element.output_file_url" download target="_blank">
                  <button class="filled-btn-primary" fxLayoutAlign="center center" mat-button fxFlexAlign="center">
                    Download
                  </button>
                </a>
                <!-- <a [href]="element.output_file_url" download target="_blank">
                    <button *ngIf="selectedTab == 'approved'" class="filled-btn-primary"
                      [matMenuTriggerFor]="module_status" #btn="matMenuTrigger" mat-stroked-button fxLayout="row"
                      fxLayoutAlign="center center">
                      <span style="margin-right: 10px">Download</span>
                      <img [src]="
                          btn.menuOpen
                            ? 'assets/images/home-icons/drop.svg'
                            : 'assets/images/home-icons/down-arrow-white.svg'
                        " />
                    </button>
                    </a>
                    <mat-menu #module_status="matMenu">
                      <a [href]="element.output_file_url" download target="_blank">
                        <button mat-menu-item *ngFor="let item of ['Download']" fxLayout="row" fxLayoutGap="5px">
                          <span>{{ item }}</span>
                        </button>
                      </a>
                    </mat-menu> -->
                <button *ngIf="
                        element.status == 'approved' &&
                        pagePermissions['publish_batch']
                      " class="stroked-btn-primary" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                  (click)="batchActions(element.batch_id, 'publish')">
                  Publish
                </button>
                <!-- cancelBatch -->
                <button *ngIf="
                        element.status == 'completed' &&
                        pagePermissions['cancel_batch']
                      " class="reject-btn" fxLayoutAlign="center center" mat-button fxFlexAlign="center"
                  (click)="openDialog(element.batch_id)">
                  Reject
                </button>
                <!-- (click)="batchActions(element.batch_id, 'reject')" -->
              </div>
            </div>
            <div fxLayout="column" *ngIf="item == 'Comments'">
              <div class="commets-icon-container">
                <div class="dot-comments" *ngIf="element.has_comments"></div>
                <img [routerLink]="['/comments']" [queryParams]="{
                        category_id: element.batch_id,
                        category: 'batch',
                        sub: subscriptionId,
                        origin: '/home'
                      }" src="assets/images/home-icons/message.svg" />
              </div>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <!-- progress spinner -->
      <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableLoading">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>
      <!-- no data section -->
      <div class="no-data" *ngIf="batchList?.length == 0 && !tableLoading" fxLayout="row" fxLayoutGap="10px">
        <mat-icon fontSet="material-icons-outlined">info</mat-icon>
        <span>Nothing to display</span>
      </div>
      <div class="custom-paginator" fxLayout="row" fxLayoutAlign="space-between center">
        <div class="jump-to-page" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="center center"
          *ngIf="batchList.length > 0 && !tableLoading">
          <span> Page</span>
          <input type="number" min="1" [max]="total_pages" type="number" [value]="page"
            (keydown.enter)="goToPage($event.target.value)" />
          <span>of</span>
          <span>{{ total_pages }}</span>
        </div>
        <mat-paginator #paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons [length]="totalItems"
          [pageSize]="size" [pageIndex]="page - 1" showFirstLastButtons (page)="onPaginationEvent($event)"
          *ngIf="batchList.length > 0 && !tableLoading">
        </mat-paginator>
      </div>
    </div>
    <!-- </mat-tab> -->
    <!-- </mat-tab-group> -->
  </div>
  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
</div>

<!-- upload file side panel -->
<ng-template #panelContent>
  <mat-progress-bar *ngIf="isFileServiceBusy" class="panel-top-progressbar" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="uploadBatch" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Upload New Batch</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <!-- drop box -->
    <ngx-dropzone (change)="onSelect($event)"
      accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/JSON, application/zip, image/*, .xls, .xlsx, .csv, .pdf, .zip"
      style="text-align: center">
      <ngx-dropzone-label fxLayout="column">
        <mat-icon fxLayoutAlign="space-between center">folder_open</mat-icon>
        Drag and Drop your files here
      </ngx-dropzone-label>
      <ng-container>
        <ngx-dropzone-preview *ngFor="let f of files" [removable]="true" (removed)="onRemove(f)">
          <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
        </ngx-dropzone-preview>
      </ng-container>
    </ngx-dropzone>
    <p class="dropzone-info">
      Upload the data similar to the format specified in the
      <a href="#" class="sample-docs text-theme-primary">
        Sample Document
        <span>
          <img class="download-sample-doc" src="../../../assets/images/home-icons/download.svg" class="svg_icon" />
        </span>
      </a>
    </p>

    <!-- <mat-progress-bar
      mode="determinate"
      [value]="uploadProgress"
      matTooltip="{{ uploadProgress | number: '1.0-0' }}%"
      matTooltipPosition="above"
      *ngIf="fileUploadStatus === 'Uploading' || fileUploadStatus === 'Failed'"
    >
    </mat-progress-bar> -->

    <!-- description box -->
    <form class="upload-form">
      <p>Batch Name</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input matInput placeholder="Batch_104.CSV" />
      </mat-form-field>
      <p>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea matInput rows="5" placeholder="Type your text here…"></textarea>
      </mat-form-field>

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-primary" fxLayout fxLayoutAlign="center center">
        Upload
      </button>
    </form>
  </div>
  <!-- module chip -->
  <div *ngIf="addTag" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Add Tag</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="closeSidepanel()" />
    </div>
    <div class="chip-container" fxLayout="row">
      <mat-chip-list>
        <mat-chip *ngFor="let item of tagList" [matTooltip]="item.tag" matTooltipPosition="above" [ngStyle]="{
            'background-color': item.tag_colour,
            color: item.text_colour
          }" fxLayout="row" fxLayoutGap="4px">
          <span (click)="addLabel(item.id, '')" style="cursor: pointer">{{ item.tag }}</span>
          <mat-icon style="cursor: pointer;" fontSet="material-icons-outlined" *ngIf="currentUser.username == item.created_by" class="delete-tag-icon"  matPrefix (click)="getDeleteConfirmation(item.id, item.tag)">
            delete</mat-icon>
        </mat-chip>
      </mat-chip-list>
    </div>
    <hr />
    <form class="upload-form" [formGroup]="createLabelForm" (ngSubmit)="createLabel()">
      <p>Create New Tag</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <input matInput placeholder="Type your text here..." formControlName="name" />
        <mat-error *ngIf="createLabelForm.controls['name'].hasError('pattern') || createLabelForm.controls['name'].hasError('whitespace')">Please enter a valid tag name </mat-error>
      </mat-form-field>
      <p>Description</p>
      <mat-form-field class="example-full-width" appearance="outline">
        <textarea formControlName="title" matInput rows="5" placeholder="Type your text here…"></textarea>
        <mat-error *ngIf="createLabelForm.controls['title'].hasError('pattern') || createLabelForm.controls['title'].hasError('whitespace')">Please enter a valid description </mat-error>
      </mat-form-field>

      <p>Tag Color</p>
      <div fxLayout="row" fxLayoutAlign="start start">
        <div *ngFor="
            let item of ['#96CEB4', '#FFEEAD', '#D9534F', '#FFAD60'];
            let i = index
          ">
          <div class="color-palette" [ngStyle]="{ 'background-color': item }" (click)="toggleTagColor(i, item)">
            <mat-icon class="iccon" *ngIf="labelColor == item">done</mat-icon>
          </div>
        </div>
      </div>

      <p>Text Color</p>
      <div fxLayout="row" fxLayoutAlign="start start">
        <div *ngFor="let item of ['#E6E8F0', '#3B3E48']; let i = index">
          <div class="color-palette" [ngStyle]="{ 'background-color': item }" (click)="toggleTagtextColor(i, item)">
            <mat-icon class="iccon" *ngIf="textColor == item">done</mat-icon>
          </div>
        </div>
      </div>

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
        [disabled]="createLabelForm.invalid || !labelColor || !textColor">
        Create Tag
      </button>
    </form>
  </div>
  <div *ngIf="viewAllBrands" class="batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <span class="panel-header">{{ supplier_brand }} Supplier Brands</span>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="closeSidepanel()" />
    </div>

    <div class="brand-container">
      <ul>
        <li *ngFor="let item of brandList" class="brand-list">
          {{ item }}
        </li>
      </ul>
    </div>
  </div>

  <div *ngIf="manualUpload" class="upload-batch-panel">

    <div fxLayout="row" fxLayoutAlign="space-between">
      <p *ngIf="channel == 'FILE_UPLOAD'" class="panel-header">
        {{ channel | underscoreAsSpace | titlecase }}
      </p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="resetUploadpanel()" *ngIf="!isFileServiceBusy" />
    </div>

    <form class="upload-form" [formGroup]="multiUploadForm" (ngSubmit)="upload()">
      <p>Supplier Name*</p>
      <mat-form-field appearance="none" class="app-dropdown supplier-drop">
        <mat-select placeholder="Select supplier" formControlName="supplierName" disabled name="uploadSupplier"
          [(ngModel)]="selectedSupplierUpload">
          <mat-option *ngFor="let each of filterList" [value]="each.supplier_code">{{ each.supplier_name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <!-- <mat-error *ngIf="
          uploadForm.controls['uploadSupplier'].hasError('required') &&
          uploadForm.controls['uploadSupplier'].touched
        ">
        Supplier is required*
      </mat-error> -->
      <p>Input Format*</p>
      <div fxLayout="row" class="input-division">
        <div fxLayout="column">
          <mat-form-field appearance="none" class="app-dropdown input">
            <mat-select placeholder="Select Input" formControlName="inputFormat" name="uploadInput"
              [(ngModel)]="uploadInput">
              <mat-option #mulVal *ngFor="let input of inputFormat" [value]="input">{{ input }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <!-- <mat-error *ngIf="
              uploadForm.controls['uploadInput'].hasError('required') &&
              uploadForm.controls['uploadInput'].touched
            ">
            Input Format is required*
          </mat-error> -->
          <div *ngIf="mainFile.length == 1 && additionalFiles.length < 5 && !isFileServiceBusy" fxLayout="row"
            class="additional-file-wrap">
            <label class="additional-actions text-theme-primary text-fileUpload" for="upload">
              Add Additional Files
            </label>
            <mat-icon class="active-text" fontSet="material-icons-outlined" matTooltip="You can add upto 5 files"
              matTooltipPosition="above">info</mat-icon>
          </div>
        </div>

        <div fxLayout="column">
          <input type="file" #mainFileInput (change)="onMainFileSelect($event)"
            accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/JSON, application/zip, image/*, .xls, .xlsx, .csv, .pdf, .zip"
            hidden />
          <button type="button" [disabled]="mainFile?.length == 1" mat-raised-button class="filled-btn-primary" fxLayout
            fxLayoutAlign="center center" (click)="mainFileInput.click()">
            Add File
          </button>

          <input type="file" id="upload" type="file" #additionalFileInput (change)="onAdditionalFileSelect($event)"
            accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/JSON, application/zip, image/*, .xls, .xlsx, .csv, .pdf, .zip"
            hidden multiple />
        </div>
      </div>

      <div fxLayoutAlign="space-between " fxLayout="row" *ngIf="mainFile.length == 1"
        class="main-file text-theme-primary">
        <span>
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 'application/json'"
            src="assets/images/file-formats/json.svg" />
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 
          'text/csv' 
          " src="assets/images/file-formats/xls.svg" />
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 
         'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'  
          " src="assets/images/file-formats/xls.svg" />
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 
          'application/vnd.ms-excel'  
           " src="assets/images/file-formats/xls.svg" />
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 
           'xls'  
            " src="assets/images/file-formats/xls.svg" />
          <img class="file-icon" *ngIf="mainFile[0]['type'] === 
            'xlsx'  
             " src="assets/images/file-formats/xls.svg" />


          {{ trimFileName(mainFile[0]["name"], 20)}}</span>
        <div>
          <span>
            <!-- <img (click)="resumeMainFile(true)"  class="file-icon" src="assets/images/home-icons/resume-upload.svg" /> -->
            <mat-icon *ngIf="retryMainFile" class="file-icon" (click)="resumeMainFile(true)"> replay</mat-icon>
          </span>
          <span *ngIf="!isFileServiceBusy" class="close-icon" (click)="clearMainFile()">
            <mat-icon>close</mat-icon>
          </span>
        </div>

      </div>

      <div fxLayout="column" *ngIf="mainFile.length == 1" class="progress-division">
        <div *ngIf="uploadProgress" fxLayout="row" fxLayoutAlign="space-between center" class="progress-bar-block">
          <mat-progress-bar class="progressbar" color="primary" mode="determinate" [value]="uploadProgress">
          </mat-progress-bar>
          <span class="progress-percentage">{{ uploadProgress }}%</span>
        </div>
        <span *ngIf="retryMainFile" class="upload-err-msg">Error Uploading File</span>
        <hr />
      </div>

      <ng-container *ngIf="additionalFiles.length > 0">
        <span class="additional-actions" style="cursor: auto;">Additional Documents</span>
        <div fxLayout="column" *ngFor="let each of additionalFilesData; let i = index">
          <div fxLayout="row" fxLayoutAlign="space-between" class="main-file text-theme-primary">
            <span>
              <img class="file-icon"
                *ngIf="each['type'] === 'application/zip' || each['type'] === 'application/x-zip-compressed'"
                src="assets/images/file-formats/zip.svg" />
              <img class="file-icon" *ngIf="each['type'] === 'text/csv'" src="assets/images/file-formats/xls.svg" />
              <img class="file-icon"
                *ngIf="each['type'] === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'"
                src="assets/images/file-formats/xls.svg" />
              <img class="file-icon" *ngIf="each['type'] === 'application/vnd.ms-excel'"
                src="assets/images/file-formats/xls.svg" />
              <img class="file-icon" *ngIf="each['type'] === 'xls'" src="assets/images/file-formats/xls.svg" />
              <img class="file-icon" *ngIf="each['type'] === 'xlsx'" src="assets/images/file-formats/xls.svg" />
              {{ trimFileName(each.name, 20)}}
            </span>

            <div>
              <span>
                <!-- <img (click)="resumeAdditionalFile(i)" class="file-icon" *ngIf="each['status'] == 'failed'"
                  src="assets/images/home-icons/resume-upload.svg" /> -->
                <mat-icon (click)="resumeAdditionalFile(i)" class="file-icon" *ngIf="each['status'] == 'failed'">replay
                </mat-icon>
              </span>
              <span *ngIf="!isFileServiceBusy" class="close-icon" (click)="clearAdditionalFile(i)">
                <mat-icon>close</mat-icon>
              </span>
            </div>
          </div>
          <div *ngIf="each.progress" fxLayout="row" class="progress-bar-block" fxLayoutAlign="space-between">
            <mat-progress-bar color="primary" mode="determinate" [value]="each.progress" class="progressbar">
            </mat-progress-bar>
            <span>{{each['progress']}}%</span>
          </div>
          <span *ngIf="each['status'] == 'failed'" class="upload-err-msg">Error Uploading File</span>
        </div>
      </ng-container>

      <ng-container *ngIf="mainFile.length == 1">
        <p>Batch Name*</p>
        <mat-form-field class="manual-upload-input" appearance="none">
          <input matInput placeholder="Enter your file name here..." name="batchName" formControlName="batchName"
            [(ngModel)]="uploadname" />
        </mat-form-field>
        <mat-error *ngIf="
      multiUploadForm.controls['batchName'].hasError('required') &&
      multiUploadForm.controls['batchName'].touched
        ">
          Batch Name is required*
        </mat-error>
        <mat-error *ngIf="multiUploadForm.controls['batchName'].hasError('maxlength')">
          Batch Name should not exceed 100 characters*
        </mat-error>
        <mat-error *ngIf="multiUploadForm.controls['batchName'].hasError('whitespace')">
          Batch Name is invalid*
        </mat-error>
      </ng-container>


      <!-- <span *ngIf="!description && mainFile.length == 1 && !isFileServiceBusy"
        class="additional-actions text-theme-primary  text-fileUpload" (click)="addDescription(true)">Add
        Description</span> -->

      <div>
        <div fxLayout="row" fxLayoutAlign="space-between end">
          <p>Description</p>
          <!-- <img style="cursor: pointer" class="close-description" src="assets/images/home-icons/info-close.svg"
            (click)="addDescription(false)" /> -->
        </div>

        <mat-form-field class="description" appearance="outline">
          <textarea autofocus matInput placeholder="Add description here" rows="5"
            formControlName="description"></textarea>
        </mat-form-field>
        <mat-error *ngIf="multiUploadForm.controls['description'].hasError('maxlength')">
          Description should not exceed 2000 characters*
        </mat-error>
      </div>

      <p>Reference</p>
      <mat-form-field class="manual-upload-input" appearance="none">
        <input matInput formControlName="reference" placeholder="Enter your reference" />
      </mat-form-field>
      <mat-error *ngIf="multiUploadForm.controls['reference'].hasError('maxlength')">
        Reference should not exceed 200 characters*
      </mat-error>

      <p>Output Format*</p>
      <mat-form-field appearance="none" class="app-dropdown">
        <mat-select placeholder="Select Output" formControlName="outputFormat" name="uploadOutput"
          [(ngModel)]="uploadOutput">
          <mat-option #mulVal *ngFor="let output of outputFormat" [value]="output">{{ output }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <!-- <mat-error *ngIf="
          uploadForm.controls['uploadOutput'].hasError('required') &&
          uploadForm.controls['uploadOutput'].touched
        " [(ngModel)]="uploadOutput">
        Output Format is required*
      </mat-error> -->


      <div class="upload-info" *ngIf="err_info">
        <div fxLayout="row" class="info" fxLayoutGap="10px">
          <span>
            {{ info_log }}
          </span>
          <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/info-close.svg"
            (click)="closeInfo()" />
        </div>
      </div>

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
        *ngIf="manualUpload" [disabled]="mainFile.length == 0 || !multiUploadForm.valid || isFileServiceBusy">
        <span *ngIf="!isFileServiceBusy">Upload</span>
        <span *ngIf="isFileServiceBusy">Uploading...</span>
      </button>
      <!-- <mat-progress-bar mode="indeterminate" class="active" *ngIf="uploadProgress > 0 && uploadProgress < 100">
      </mat-progress-bar> -->
    </form>
  </div>

  <div *ngIf="pimUpload" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between">
      <p *ngIf="channel == 'FILE_UPLOAD'" class="panel-header">
        {{ channel | underscoreAsSpace | titlecase }}
      </p>
      <p *ngIf="channel == 'PIM_UPLOAD'" class="panel-header">PIM Upload</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="resetUploadpanel()" *ngIf="!isFileServiceBusy" />
    </div>
    <form class="upload-form" [formGroup]="uploadForm" (ngSubmit)="upload()">
      <!-- drop box -->
      <ngx-dropzone (change)="onSelect($event)" multiple=false
        accept="text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/JSON, application/zip, image/*, .xls, .xlsx, .csv, .pdf, .zip"
        style="text-align: center">
        <ngx-dropzone-label fxLayout="column">
          <mat-icon fxLayoutAlign="space-between center">folder_open</mat-icon>
          Drag and Drop your files here
        </ngx-dropzone-label>
        <ng-container>
          <ngx-dropzone-preview *ngFor="let f of files" [removable]="true" (removed)="onRemove(f)">
            <ngx-dropzone-label [matTooltip]="f.name" matTooltipPosition="above">{{ f.name | truncate:25}}
            </ngx-dropzone-label>
          </ngx-dropzone-preview>
        </ng-container>
      </ngx-dropzone>

      <p>Supplier Name</p>
      <mat-form-field appearance="none" class="app-dropdown supplier-drop">
        <mat-select placeholder="Select supplier" formControlName="uploadSupplier" disabled name="uploadSupplier"
          [(ngModel)]="selectedSupplierUpload">
          <mat-option *ngFor="let each of filterList" [value]="each.supplier_code">{{ each.supplier_name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-error *ngIf="
          uploadForm.controls['uploadSupplier'].hasError('required') &&
          uploadForm.controls['uploadSupplier'].touched
        ">
        Supplier is required*
      </mat-error>

      <p>File Name</p>
      <mat-form-field class="manual-upload-input" appearance="none">
        <input matInput placeholder="Enter your file name here..." name="uploadname" formControlName="uploadname"
          [(ngModel)]="uploadname" />
      </mat-form-field>
      <mat-error *ngIf="
          uploadForm.controls['uploadname'].hasError('required') &&
          uploadForm.controls['uploadname'].touched
        ">
        File Name is required*
      </mat-error>

      <div *ngIf="IOvalues">
        <p>Input Format</p>
        <mat-form-field appearance="none" class="app-dropdown">
          <mat-select placeholder="Select Input" formControlName="uploadInput" name="uploadInput"
            [(ngModel)]="uploadInput">
            <mat-option #mulVal *ngFor="let input of inputFormat" [value]="input">{{ input }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-error *ngIf="
            uploadForm.controls['uploadInput'].hasError('required') &&
            uploadForm.controls['uploadInput'].touched
          ">
          Input Format is required*
        </mat-error>

        <p>Output Format</p>
        <mat-form-field appearance="none" class="app-dropdown">
          <mat-select placeholder="Select Output" formControlName="uploadOutput" name="uploadOutput"
            [(ngModel)]="uploadOutput">
            <mat-option #mulVal *ngFor="let output of outputFormat" [value]="output">{{ output }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-error *ngIf="
            uploadForm.controls['uploadOutput'].hasError('required') &&
            uploadForm.controls['uploadOutput'].touched
          " [(ngModel)]="uploadOutput">
          Output Format is required*
        </mat-error>
      </div>

      <div class="upload-info" *ngIf="err_info">
        <div fxLayout="row" class="info" fxLayoutGap="10px">
          <span>
            {{ info_log }}
          </span>
          <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/info-close.svg"
            (click)="closeInfo()" />
        </div>
      </div>

      <!-- side panel upload button -->
      <button mat-raised-button class="sidePanel-upload-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
        *ngIf="pimUpload" [disabled]="
          !uploadForm.valid ||
          this.files.length < 1 ||
          isFileServiceBusy ||
          (uploadProgress > 0 && uploadProgress < 100)
        ">
        Upload
      </button>
      <mat-progress-bar mode="indeterminate" class="active" *ngIf="uploadProgress > 0 && uploadProgress < 100">
      </mat-progress-bar>
    </form>
  </div>
</ng-template>

<app-side-panel [sidenavTemplateRef]="panelContent" [direction]="'right'" [navWidth]="380" [duration]="0.5">
</app-side-panel>
