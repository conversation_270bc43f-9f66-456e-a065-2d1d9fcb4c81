@import "../../../../styles/variables";

.back-icon {
  margin-right: 10px !important;
}

.selected-batch {
  margin-right: 10px !important;
}

.reset-btn {
  margin-right: 7px !important;
}

.table-right-margin {
  margin-right: 40px;
}

.commets-icon-container {
  cursor: pointer;
  position: relative;
  padding-left: 40px;
}

.dot-comments {
  position: relative;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #D30505;
  top: 6px;
  left: 18px;

}

.data-loading-spinner {
  height: 500px;
}

.tooltip-list {
  white-space: pre;
}

.product-img {
  width: 40px;
  height: 50px;
  padding: 10px;
  cursor: pointer;
}

.filter-container {
  line-height: 20px !important;
}

.search-filter {
  flex-grow: 1 !important;
  margin-right: 10px !important;
  width: 180px !important;
  // width: inherit !important;
}

.app-dropdown {
  margin-right: 10px !important;
  width: 120px;

  @media screen and (min-width: 1400px) {
    width: 140px;
  }

  @media screen and (min-width: 1450px) {
    width: 120px;
  }

  @media screen and (min-width: 1600px) {
    width: 165px;
  }
}

.send-to-btn {
  padding: 20px 40px 0 16px;
}

// sidepanel
.panel-top-progressbar {
  position: absolute;
  margin-top: 38px;
  width: 100%;
  margin-left: -32px;
}

.upload-batch-panel {
  margin-top: 50px;

  hr {
    border: 1px solid #e6e8f0;
  }

  .panel-header {
    font-weight: 600;
    font-size: 16px;
    // line-height: 24px;
  }

  .upload-form {
    p {
      margin-top: 14px;
      font-weight: 600;
      font-size: 14px;
      color: #222329;
    }

    .supplier-drop {
      margin-bottom: 20px;
    }

    mat-form-field {
      width: 320px;
    }

    .sidePanel-upload-btn {
      margin-top: 50px;
      width: 320px;
      height: 40px;
      border-radius: 4px;
    }

    .upload-info {
      margin-top: 30px;
      padding: 10px;
      width: 100%;
      border-radius: 5px;
      background-color: #fff8f8;
      border: solid 1px #e84545;

      .info {
        width: 100%;
        height: 100%;
        color: #e84545;

        .batch-stop-icon {
          height: 17.5px;
          width: 17.5px;
          left: 1.75px;
          top: 1.75px;
          border-radius: 0px;
          color: #e84545;
        }
      }
    }
  }

  .manual-upload-input {
    border: 1px solid #c1c4d6;
    height: 40px;
    border-radius: 4px;

    // background-color: red;
    input {
      position: absolute;
      width: 90%;
      margin-left: 10px;
      // padding-bottom: 45px !important;
      margin-top: -8px;
    }
  }

  .upload-form {

    .input-division {
      .input {
        width: 90%;
      }
    }
  }
}
