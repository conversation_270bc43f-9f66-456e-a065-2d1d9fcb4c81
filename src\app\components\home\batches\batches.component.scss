@import "../../../../styles/variables";

  .banner-text {
    width: 100%;
    height: 42px;
    background-color: black;
    color: #f0f3f9;
    text-align: center;
    font-weight: 300;
    font-size: 14px;
    line-height: 20px;
    padding: 0 20px;
    // border: 2px solid red;
    u {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .banner-close {
    margin-top: 0;
    cursor: pointer;
  }
  .banner-close:hover {
    color: grey;
  }
.select-batch {
  margin-bottom: 10px;
}
.batch-link {
  padding-left: 10px;
}
.batch-id {
  text-align: center;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  text-decoration-line: underline;
  height: 21px;
  padding-right: 3px;
}
.view-messages-icon {
  color: #3b3e48;
  font-size: 40px;
  cursor: pointer;
  .dot {
    position: fixed;
    height: 8px;
    width: 8px;
    background-color: #d30505;
    border-radius: 50%;
    margin-left: 18px;
    margin-top: 10px;
  }
}

.commets-icon-container {
  cursor: pointer;
  position: relative;
  margin-left: 10px;
}
.dot-comments {
  position: relative;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #d30505;
  top: 6px;
  left: 18px;
}

.batch-count {
  width: 30px;
  height: 24px;
  background: #e6e8f0;
  border-radius: 12px;
}

.copy-icon {
  cursor: pointer;
  font-size: 15px;
  width: 15px;
  height: 15px;
  margin-top: 4px;
}
.batch-name-wrapper {
  width: 120px;
  @media screen and (min-width: 1400px) {
    width: 120px;
  }
  .batch-name {
    font-style: normal;
    font-weight: 400;
  }
}
.name-description {
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  width: 70%;
  max-width: 60%;
  word-wrap: break-word;
}
.sub-text-id {
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 18px;
  text-decoration: underline;
  cursor: pointer;
  width: 120px;
  word-break: break-word;
}

.description {
  margin-top: -10px;
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
}

.err-count {
  color: #e84545;
}

.color-palette {
  height: 20px;
  width: 20px;
  margin-right: 5px;
  border-radius: 5px;
  margin-bottom: 20px;
  cursor: pointer;
}

.download-icon {
  cursor: pointer;
  .download-batch {
    margin-top: 4px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    padding: 2px;
    height: 15px;
    width: 15px;
  }
}

.published {
  background-color: #52bd94
}

.add-label-btn {
  cursor: pointer;
  margin-top: 10px;
  box-sizing: border-box;
  border-radius: 20.5px;
  background-color: #ffffff;
  font-size: 10px;
  line-height: 15px;
  height: 24px;
  margin-bottom: 5px;
  text-align: left;
  width: max-content;
  padding: 10px 10px;
  font-family: "Poppins", sans-serif;

  .add-label-icon {
    margin-left: -5px;
    margin-top: 0px;
    font-size: 15px;
    width: 15px;
    height: 15px;
  }
}

.tags-container {
  width: 100%;
  padding: 10px 0;

  .label-chip {
    margin-right: 1px;
    height: 24px;
    border-radius: 40px;
    width: max-content;
    padding: 10px;
    font-style: normal;
    font-weight: normal;
    font-size: 10px;
    line-height: 15px;
  }

  .remove-label-icon {
    font-size: 15px;
    margin-top: 9px;
    margin-left: 5px;
    margin-right: -10px;
    cursor: pointer;
  }
}

.action-btn {
  width: 100%;
  .mat-button {
    height: 30px;
    width: 100%;
  }
  .approve-with-errs {
    font-size: 11px;
    font-weight: bold;
  }
}

.reject-btn {
  border: 1px solid #e84545;
  color: #e84545;
  height: 30px;
  width: 100%;
}

.batch-published {
  color: #52bd94;
  width: 100%;
}
.batch-cancelled {
  color: #e84545;
}

// progress bar inactions
.progress-wrapper {
  width: 100%;
  align-items: start;

  mat-progress-bar {
    justify-content: center;
    border-radius: 5px;
    opacity: 1;
  }

  .progress-bar-value {
    text-align: left;
    letter-spacing: 0px;
    color: #3b3e48;
    opacity: 1;
    margin-top: -10px;
    font-weight: 400;
    margin-bottom: 0;
  }
}

.upload-batch-panel {
  margin-top: 50px;
  hr {
    border: 1px solid #e6e8f0;
  }

  .panel-header {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }

  ngx-dropzone {
    height: 120px;
    justify-content: center;
    align-items: center;
    border: 1px dashed #c1c4d6;
    border-radius: 4px;
    margin-bottom: 20px;
    margin-top: 10px;

    ngx-dropzone-label {
      font-family: "Poppins", sans-serif;
      font-weight: 300;
      font-size: 12px;
      line-height: 18px;
      letter-spacing: 0px;
      color: #222329;
      opacity: 1;

      mat-icon {
        margin-left: 60px;
      }
    }

    ngx-dropzone-preview {
      min-height: 80px !important;
      height: 80px !important;
      ngx-dropzone-label {
        width: 70%;
      }
    }
  }

  .dropzone-info {
    width: 340px;
    font-size: "Poppins", sans-serif;
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    color: #474d66;

    .sample-docs {
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      // color: $theme-button;

      img {
        padding-left: 5px;
        cursor: pointer;
      }
    }
  }

  .upload-form {
    p {
      font-weight: 600;
      font-size: 14px;
      color: #222329;
    }
    .supplier-drop {
      margin-bottom: 20px;
    }
    mat-form-field {
      width: 320px;
    }

    .sidePanel-upload-btn {
      margin-top: 50px;
      width: 320px;
      height: 40px;
      border-radius: 4px;
      font-weight: 600;
    }
    mat-progress-bar {
      margin-bottom: 20px;
    }
    .upload-info {
      margin-top: 30px;
      padding: 10px;
      width: 100%;
      border-radius: 5px;
      background-color: #fff8f8;
      border: solid 1px #e84545;
      .info {
        width: 100%;
        height: 100%;
        color: #e84545;
        .batch-stop-icon {
          height: 17.5px;
          width: 17.5px;
          left: 1.75px;
          top: 1.75px;
          border-radius: 0px;
          color: #e84545;
        }
      }
    }
  }

  .manual-upload-input {
    border: 1px solid #c1c4d6;
    height: 45px;
    border-radius: 4px;
    // background-color: red;
    input {
      position: absolute;
      width: 90%;
      margin-left: 10px;
      // padding-bottom: 45px !important;
      margin-top: -8px;
    }
  }

  .upload-form {
    .additional-actions {
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      cursor: pointer;
      // padding-top: -10px;
      margin-top: 5px;
    }

    .main-file {
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      margin-top: 10px;
      .file-icon {
        padding-right: 5px;
        width: 20px;
        height: 20px;
      }
      .close-icon {
        mat-icon {
          font-size: 15px;
          margin-top: 5px;
          cursor: pointer;
        }
      }
    }
    .progress-division {
      .progress-bar-block {
        margin-top: 5px;
        .progressbar {
          width: 80%;
          padding-top: 10px;
        }
        .progress-percentage {
          margin-top: -10px;
        }
      }
      hr {
        border: 0;
        display: block;
        width: 100%;
        background-color: #d0d3dd;
        height: 1px;
      }
    }

    .additionan-file-block {
      margin-top: 10px;
    }

    .input-division {
      .input {
        width: 90%;
      }
      .additional-file-wrap {
        label {
          padding-right: 5px;
          font-size: 14px;
        }
        mat-icon {
          font-size: 20px;
          padding-top: 4px;
        }
      }
    }
  }

  .chip-container {
    margin-top: 10px;
    margin-bottom: 20px;

    mat-chip {
      font-family: "Poppins", sans-serif;
      font-weight: 300;
      span {
        height: 32px;
        line-height: 32px;
      }
      .delete-tag-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: #E74545;
      }
    }
  }

  .batch-log-id {
    font-weight: 600;
    padding: 5px;
  }
}
.data-loading-spinner {
  height: 500px;
}

// to avoid red border
::ng-deep
  .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid
  .mat-form-field-outline-thick {
  color: #e6e8f0 !important;
  opacity: 0.8 !important;
}

.close-description {
  position: relative;
  margin-top: 10px;
}
.manual-upload {
  display: inline-block;

  border-radius: 4px;
  font-size: 14px;
  width: 100%;
  cursor: pointer;
  // margin-top: 1rem;
}
.text-fileUpload {
  display: inline-block;
  cursor: pointer;
}

//additional file progress bar
.progress-bar-block {
  margin-top: 5px;
  margin-left: 25px;
  .progressbar {
    width: 80%;
    padding-top: 10px;
  }

  .progress-percentage {
    margin-top: -10px;
  }
}

.upload-err-msg {
  margin-left: 25px;
  font-family: $site-font;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #e11e1e;
  // margin-top: -10px;
}
.panel-top-progressbar {
  position: absolute;
  margin-top: 38px;
  width: 100%;
  margin-left: -32px;
}

.refrence {
  font-size: 13px;
  word-wrap: break-word;
}
.batch-date {
  font-size: 11px;
}
.additional-file-icon {
  width: 25px;
  padding-right: 5px;
  margin-top: 10px;
}

// filters adjusted
.filter-container {
  padding-right: 12px;
  mat-form-field.search-filter {
    width: 28%;
  }
  .date-range-filter {
    width: 200px;
    mat-select {
      width: 160px;
    }
  }
  .status-filter {
    mat-select {
      height: 0;
      font: normal normal normal 14px/21px $site-font;
    }
  }
}

//stats cards
.stats-container {
  mat-card {
    background: $theme-white;
    box-shadow: 3px 3px 6px 1px rgba(59, 62, 72, 0.02);
    border-radius: 4px;
    height: 97px;
    width: 100%;

    .card-head {
      font-family: $site-font;
      font-style: normal;
      font-weight: bold;
      font-size: 18px;
      line-height: 27px;
      color: $theme-black;
 
    }
    img{
      cursor: pointer;
    }

    .card-sub-head {
      margin-top: 2px;
      padding: 10px;
    }

    .card-actions {
      img {
        height: 40%;
        cursor: pointer;
      }
    }
  }
}

