<!--Header Starts-->
<div class="top-nav" fxLayoutAlign="center center">
  <div class="nav-bar" layout="row" fxFlex="100" fxLayoutAlign.xs="space-between center"
    fxLayoutAlign="space-between center">
    <!--Logo-->
    <div class="top-nav-logo">
      <a class="top-nav-logo-link" href="{{ user?.dashboard_url }}">
        <img style="width: 60%;" src="assets/images/datax-updated-logo/dataX.aiWhite.svg" class="svg_icon" />
      </a>
    </div>
    <!-- topnav actions src/assets/images/datax-updated-logo/dataX.aiWhite.svg -->
    <div class="top-nav__menu">
      <div fxLayout="row" class="menu-notification" *ngIf="user">
        <!-- User display picture -->
        <a class="top-nav__menu-link">
          <div fxLayout="column" fxLayoutAlign="center center">
            <img [src]="user?.profile_picture" class="avatar" />
          </div>
        </a>
        <!-- User role -->
        <a class="top-nav__menu-user">
          <div fxLayout="column" fxLayoutAlign="space-between start" fxLayoutGap="5px">
            <p class="display-name" [matTooltip]="user?.name.length > 18 ? user?.name : null"
              matTooltipPosition="above">{{
                user?.name.length > 18
                  ? (user?.name | slice: 0:18) + ".."
                  : user?.name
              }}</p>
            <p class="role" [matTooltip]="user?.email.length > 18 ? user?.email : null" matTooltipPosition="above">{{
                user?.email.length > 18
                  ? (user?.email | slice: 0:18) + ".."
                  : user?.email
              }}</p>
          </div>
        </a>
        <!-- Dropdown action buttons -->
        <div class="menu-drop" [matMenuTriggerFor]="menu" #t="matMenuTrigger">
          <!-- <mat-icon mat-icon-button  class="material-icons-outlined">
                  arrow_drop_down_circle</mat-icon> -->
          <img [src]="
              t.menuOpen
                ? 'assets/images/home-icons/drop.svg'
                : 'assets/images/home-icons/down-arrow-white.svg'
            " />
        </div>

        <mat-menu #menu="matMenu">
          <button mat-menu-item (click)="logUserOut()">
            <mat-icon>power_settings_new</mat-icon>
            <span class="menu-item">Log Out</span>
          </button>
        </mat-menu>

        <div class="client_logo">
          <img [src]="user?.client_logo" />
        </div>
      </div>
    </div>
  </div>
</div>
