@import "../../../styles/variables";

.white-bg {
  // background-color: #fff !important;
  height: calc(100vh - 70px);
}
.filter-container {
  height: 80px;
  .filter-head {
    z-index: 1000;
    margin: 0 !important;
    mat-icon {
      cursor: pointer;
    }
    .search-container {
      .back-icon {
        margin-top: -1px;
        padding-right: 10px;
      }
      .comment-header {
        p {
          font-family: $site-font;
          font-style: normal;
          font-weight: 600;
          font-size: 16px;
          line-height: 24px;
        }
        .search-filter {
          margin-left: 20px;
          margin-top: -5px;
          font-family: $site-font;
          color: #c1c4d6;
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          line-height: 21px;
          input {
            position: absolute;
            margin-top: -18px;
            margin-bottom: 10px;
            color: $theme-black;
          }
        }
      }
    }
  }
}

.comments-wrapper {
  height: calc(100vh - 150px);
  .comment-division {
    .division-1 {
      width: 100%;
      position: sticky;
      overflow-y: scroll;
      border-right: 1px solid #e6e8f0;
      // height: 100vh;
      .active-comment {
        background-color: $blue-10;
      }
      mat-card {
        cursor: pointer;
        box-shadow: none;
        border-top: 1px solid #e6e8f0;
        border-bottom: 1px solid #e6e8f0;
        border-radius: 0px;
        background: #ffffff;
        .card-info {
          .batch-caption {
            font-family: $site-font;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            line-height: 21px;
            text-decoration-line: underline;
          }
          mat-icon {
            position: absolute;
            right: 20px;
          }
        }
        .profile {
          img {
            width: 30.36px;
            height: 32px;
            border-radius: 5px;
          }
          .profile-info {
            font-style: normal;
            color: $theme-black;
            padding-left: 10px;
            .username {
              font-weight: 600;
              font-size: 12px;
              line-height: 18px;
              padding-bottom: -10px;
            }
            .comment-time {
              margin-top: -10px;
              font-weight: 300;
              font-size: 10px;
              line-height: 15px;
            }
          }
        }
        .comments {
          font-style: normal;
          font-weight: normal;
          font-size: 14px;
          line-height: 21px;
          p {
            width: 80%;
          }
          .tagged-users {
            margin-right: 5px;
          }
        }
      }
    }
    .division-2 {
      position: relative;
      background-color: red;
      background-color: $theme-white;
      // height: 100vh;
      .comment-section {
        box-shadow: 0 0 4px #00000033;
        .card-info {
          width: max-content;
          padding-left: 20px;
          .comment-header {
            margin-top: 20px;
            margin-right: 10px;
            // width: 100%;
            p {
              margin-top: 10px;
              font-style: normal;
              font-weight: normal;
              font-size: 14px;
              line-height: 21px;
              text-decoration-line: underline;
            }
            img {
              position: absolute;
              padding-left: 10px;
              margin-top: 5px;
            }
          }
        }

        .action-btn {
          margin-right: 20px;
          display: flex;
          justify-content: flex-end;
          button {
            width: fit-content;
            background: #f3f6ff;
            border-radius: 4px;
            height: 40px;
            margin-left: 10px;
          }
          .resolve-btn {
            background: #52bd94;
            color: $theme-white;
          }
        }
      }
      .tagged-users {
        margin-right: 3px;
      }
      .comments-scroll-wrapper {
        margin-bottom: 10px;
        margin-left: 30px;
        padding-top: 10px;
        overflow-y: scroll;
        .no-comments-yet {
          color: $neutral-200;
          height: calc(100vh - 350px);
          .main-msg {
            font: normal normal 400 24px/36px 'Poppins';
          }
          .sub-msg {
            font: normal normal 300 14px/21px "Open Sans", sans-serif;
          }
        }
        .comments-scroll {
          overflow-y: scroll;
          height: calc(100vh - 350px);
        // display: flex;
        // flex-direction: column-reverse;
        .cmt-profile {
          img {
            width: 32px;
            height: 32px;
            border-radius: 5px;
          }
        }
        .cmt-profile-picture {
          width: 80%;
          font-style: normal;
          color: $theme-black;
          padding-left: 10px;
          .username {
            font-weight: 600;
            font-size: 12px;
            line-height: 18px;
            padding-bottom: -10px;
          }
          .comment-time {
            margin-top: -10px;
            font-weight: 300;
            font-size: 10px;
            line-height: 15px;
          }
          .comments {
            margin-bottom: 5px;
            width: 100%;
            cursor: pointer;
            padding: 6px 10px 6px 6px;
            font-family: normal normal normal 14px/21px "Open Sans", sans-serif !important;
            border-radius: 8px;
            // &:hover {

            // }
            img {
              width: 20px;
              height: 20px;
            }
          }
          .hover-color {
            background: #f4f6fa;
          }
          .edit-comment-box {
            border: 1px solid #e6e8f0;
            border-radius: 4px;
            padding-right: 10px;
            .edit-comment-text {
              width: 80%;
              padding: 10px;
              resize: none;
            }
            ul {
              width: 13rem;
              background-color: #fff;
              box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
                0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
              padding: 0;
              li {
                padding-left: 1rem;
                padding-right: 1rem;
                height: 2rem;
                min-width: 10rem;
                background-color: #fff;
                cursor: pointer;
                display: -webkit-box;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: justify;
                justify-content: space-between;
              }
            }
            img {
              cursor: pointer;
              width: 24px;
              width: 24px;
            }
          }
        }
      }
        .comment-box {
          bottom: 30px;
          border: 1px solid #e6e8f0;
          box-sizing: border-box;
          border-radius: 4px;
          width: 60%;
          padding: 20px;
          position: fixed;
          bottom: 25px;
          // padding: 1rem;
          ul {
            width: 13rem;
            background-color: #fff;
            box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%),
              0 3px 1px -2px rgb(0 0 0 / 20%), 0 1px 5px 0 rgb(0 0 0 / 12%);
            padding: 0;
            li {
              padding-left: 1rem;
              padding-right: 1rem;
              height: 2rem;
              min-width: 10rem;
              background-color: #fff;
              cursor: pointer;
              display: -webkit-box;
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              -webkit-box-pack: justify;
              justify-content: space-between;
            }
          }
          .cmt-textarea {
            width: 100%;
            img {
              cursor: pointer;
              margin-right: 6px;
            }
            textarea {
              resize: none;
            }
          }
          .add-file {
            margin-bottom: -12px;
            margin-left: 10px;
            cursor: pointer;
          }
        }
    }
    }
  }
}
// ::-webkit-scrollbar {
//   width: 0px !important;
// }
.center-placement {
  width: 100%;
  height: 500px !important;
}
.comments-scroll-wrapper,
.division-1 {
  // Scroll bar for comments page
  &::-webkit-scrollbar {
    width: 10px;
  }
  /* Track */
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 5px;
  }
  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: #b8b6b6;
    border-radius: 10px;
    height: 20px;
  }
  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #b1acac;
  }
}
