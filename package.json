{"name": "module-rs2a", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 5000  --disable-host-check --host rs2a.app-local.datax.ai --ssl true", "build": "ng build", "build-dev": "ng build --configuration='dev'", "build-staging": "ng build --configuration='stag'", "build-prod": "ng build --configuration='production'", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~13.1.0", "@angular/cdk": "^13.2.2", "@angular/common": "~13.1.0", "@angular/compiler": "~13.1.0", "@angular/core": "~13.1.0", "@angular/flex-layout": "^13.0.0-beta.38", "@angular/forms": "~13.1.0", "@angular/material": "^13.2.2", "@angular/platform-browser": "~13.1.0", "@angular/platform-browser-dynamic": "~13.1.0", "@angular/router": "~13.1.0", "@auth0/auth0-angular": "^1.9.0", "@flxng/mentions": "^1.1.4", "angular-mentions": "^1.4.0", "jquery": "^3.6.0", "marked": "^4.0.12", "moment": "^2.29.1", "ngx-dropzone": "^3.1.0", "ngx-infinite-scroll": "^10.0.1", "ngx-markdown": "^13.1.0", "ngx-quill": "^15.0.0", "quill": "^1.3.7", "quill-mention": "^6.1.1", "rxjs": "~7.4.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.1.4", "@angular/cli": "~13.1.4", "@angular/compiler-cli": "~13.1.0", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.5.2"}}