import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-image-viewer',
  templateUrl: './image-viewer.component.html',
  styleUrls: ['./image-viewer.component.scss']
})
export class ImageViewerComponent implements OnInit {
  imgsArr: any = [];
  currentIndex = 0;
  slides;
  tag: any;
  showTag: boolean = false;
  productImage;
  constructor(
    @Inject(MAT_DIALOG_DATA) private data: any,
    private dialogRef: MatDialogRef<ImageViewerComponent, any>
  ) {
    if (data) {
      this.imgsArr = data.productImg;
      this.productImage = data.productImg
      // console.log(data)
    }
    // this.preloadImages();
  }

  ngOnInit(): void {}

  preloadImages() {
    this.imgsArr.forEach((slide) => {
      new Image().src = slide;
    });
  }

  setCurrentSlideIndex(index) {
    this.currentIndex = index;
  }

  isCurrentSlideIndex(index) {
    return this.currentIndex === index;
  }

  prevSlide() {
    this.currentIndex =
      this.currentIndex < this.imgsArr.length - 1 ? ++this.currentIndex : 0;
  }

  nextSlide() {
    this.currentIndex =
      this.currentIndex > 0 ? --this.currentIndex : this.imgsArr.length - 1;
  }
}
