import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import moment from 'moment';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SettingService } from '../../service/setting.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpErrorResponse } from '@angular/common/http';
import { SnackbarService } from '../../service/snackbar.service';

export interface PeriodicElement {
  action: string;
  value: boolean;
}
@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit {
  selectedValForToggle = 'email';
  modulesToBeDisplayed;
  concernedModuleList: any[] = [];
  retailersModuleList: any[] = [];
  suppliersModuleList: any[] = [];
  emailNotification;
  inAppNotification;
  checked = false;
  isChecked = false;
  Allchecked: boolean;
  displayedColumns: string[] = ['Actions'];
  dataSource;
  dataLoading: boolean = false;

  @ViewChild(MatPaginator) paginator: MatPaginator;

  constructor(
    private settingService: SettingService,
    public matSnackbar: MatSnackBar,
    private snackbarService: SnackbarService
  ) {}

  subscriptionId;
  useDefault;
  check: boolean;
  action: boolean;
  errors_in_uploaded_file: boolean;
  errors_in_file_uploaded_by_supplier: boolean;
  toggleList;

  ngOnInit() {
    // add subscription id from local storage in url as param
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.getCurrentStatus();
    this.dataLoading = true;
  }

  /**
   * Get current settings
   */
  getCurrentStatus = () => {
    this.settingService.getEmailNotification(this.subscriptionId).subscribe({
      next: (resp) => {
        this.dataLoading = false;
        this.toggleList = resp.result;
        this.action = resp.result.actions;
        (this.errors_in_uploaded_file = resp.result.errors_in_uploaded_file),
          (this.errors_in_file_uploaded_by_supplier =
            resp.result.errors_in_file_uploaded_by_supplier);
        // data source for Actions
        this.dataSource = new MatTableDataSource<PeriodicElement>([
          {
            action: 'Errors in uploaded File',
            value: this.errors_in_uploaded_file,
          },
          // {
          //   action: 'Errors in file uploaded by the supplier',
          //   value: this.errors_in_file_uploaded_by_supplier,
          // },
        ]);
      },
      error: (HttpResponse: HttpErrorResponse) => {
       this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * Toggle All settings
   * @param e
   */
  setAllToggleSwitches = (e) => {
    if (e.checked) {
      this.modifyEmailNotificationsSetting(this.subscriptionId, true, true);
    } else {
      this.modifyEmailNotificationsSetting(this.subscriptionId, false, false);
    }
  };

  /**
   * Individual settings
   * @param event
   * @param action
   */
  setValue = (event, action) => {
    if (action == 'Errors in uploaded File') {
      this.errors_in_uploaded_file = event.checked;
    } else if (action == 'Errors in file uploaded by the supplier') {
      this.errors_in_file_uploaded_by_supplier = event.checked;
    }

    this.modifyEmailNotificationsSetting(
      this.subscriptionId,
      this.errors_in_uploaded_file,
      this.errors_in_file_uploaded_by_supplier
    );
  };

  /**
   * Service call to modify settings
   * @param subscriptionId
   * @param errors_in_uploaded_file
   * @param errors_in_file_uploaded_by_supplier
   */
  modifyEmailNotificationsSetting = (
    subscriptionId,
    errors_in_uploaded_file,
    errors_in_file_uploaded_by_supplier
  ) => {
    this.settingService
      .modifyEmailNotificationsSetting(
        subscriptionId,
        errors_in_uploaded_file,
        errors_in_file_uploaded_by_supplier
      )
      .subscribe({
        next: (resp) => {
          this.getCurrentStatus();
            this.snackbarService.openSnackBar(resp.detail, 'OK');
        },
        error: (HttpResponse: HttpErrorResponse) => {
              this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };
}
