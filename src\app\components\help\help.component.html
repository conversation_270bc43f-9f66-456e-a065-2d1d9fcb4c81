<div class="wrapper wrapper-help" fxLayout="column">
  <div class="filter-container" fxLayout="row" fxLayoutGap="20px" fxFlex="100">
    <mat-button-toggle-group #group="matButtonToggleGroup" class="toggle-btn" [value]="selectedValForToggle"
      class="toggle-btn" (change)="onButtonToggle(group.value)">
      <mat-button-toggle value="sales">Contact Sales</mat-button-toggle>
      <mat-button-toggle value="support">Contact Support</mat-button-toggle>
    </mat-button-toggle-group>
  </div>
  <!-- help section -->
  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px">

    <div *ngIf="contactSales">
      <mat-card class="card">
        <!-- progress spinner -->
        <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="loading">
          <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
        </div>
        <div *ngIf="!loading">
          <p class="card-header">Need some help?</p>
          <div fxLayout="column" class="form-container">
            <!-- description box -->
            <form class="upload-form" [formGroup]="contactSalesForm">
              <p>Email</p>
              <mat-form-field class="" appearance="outline">
                <input matInput placeholder="<EMAIL>" formControlName="email" readonly/>
                <mat-error *ngIf="contactSalesForm.controls['email'].hasError('required')">Email is required </mat-error>
                <mat-error *ngIf="contactSalesForm.controls['email'].hasError('email') || contactSalesForm.controls['email'].hasError('pattern')">Please enter a valid email</mat-error>
              </mat-form-field>
              <p>Please describe your problem</p>
              <mat-form-field class="" appearance="outline">
                <textarea formControlName="message" matInput rows="5" placeholder="Type your text here…"></textarea>
                <mat-error *ngIf="contactSalesForm.controls['message'].hasError('required')">Description is required </mat-error>
                <mat-error *ngIf="contactSalesForm.controls['message'].hasError('pattern') || contactSalesForm.controls['message'].hasError('whitespace')">Please enter a valid description </mat-error>
              </mat-form-field>

              <!-- side panel upload button -->
              <div fxLayout="row" fxLayoutGap="10px">
                <button mat-button class="cancel-btn" fxLayout fxLayoutAlign="center center" (click)="resetForm()">
                  Cancel
                </button>
                <button mat-button class="send-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
                  [disabled]="contactSalesForm.invalid" (click)="postContactSales()">
                  Send
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-card>
    </div>

    <div *ngIf="contactSupport">
      <mat-card class="card">
        <!-- progress spinner -->
        <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="loading">
          <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
        </div>

        <div *ngIf="!loading">
          <p class="card-header">Need some help?</p>
          <div fxLayout="column" class="form-container">
            <!-- description box -->
            <form class="upload-form" [formGroup]="contactSalesForm">
              <p>Email</p>
              <mat-form-field class="" appearance="outline">
                <input matInput placeholder="<EMAIL>" formControlName="email" type="email" readonly/>
                <mat-error *ngIf="contactSalesForm.controls['email'].hasError('required')">Email is required </mat-error>
                <mat-error *ngIf="contactSalesForm.controls['email'].hasError('email') || contactSalesForm.controls['email'].hasError('pattern')">Please enter a valid email</mat-error>
              </mat-form-field>
              <p>Please describe your problem</p>
              <mat-form-field class="" appearance="outline">
                <textarea formControlName="message" matInput rows="5" placeholder="Type your text here…"></textarea>
                <mat-error *ngIf="contactSalesForm.controls['message'].hasError('required')">Description is required </mat-error>
                <mat-error *ngIf="contactSalesForm.controls['message'].hasError('pattern') || contactSalesForm.controls['message'].hasError('whitespace')">Please enter a valid description </mat-error>
              </mat-form-field>

              <!-- side panel upload button -->
              <div fxLayout="row" fxLayoutGap="10px">
                <button mat-button class="cancel-btn" fxLayout fxLayoutAlign="center center" (click)="resetForm()">
                  Cancel
                </button>
                <button mat-button class="send-btn filled-btn-primary" fxLayout fxLayoutAlign="center center"
                  [disabled]="contactSalesForm.invalid" (click)="postContactSupport()">
                  Send
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
</div>
