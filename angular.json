{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "2ce04fbd-1ced-4c49-8adc-593aa121b19c"}, "version": 1, "newProjectRoot": "projects", "projects": {"module-rs2a": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/module-rs2a", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css", "src/styles/styles.scss", "src/styles/mat-theme.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "stag": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stag.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "module-rs2a:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "module-rs2a:build:production"}, "development": {"browserTarget": "module-rs2a:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "module-rs2a:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "defaultProject": "module-rs2a"}