import { HttpHeaders, HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, mergeMap, catchError, throwError, map, concatMap, of, concat, BehaviorSubject } from 'rxjs';
import { Globals } from '../_globals/endpoints.global';

@Injectable({
  providedIn: 'root'
})
export class FileUploadService {

  private httpOptions: HttpHeaders;
  private chunkSize: number = 1 * 1024 * 1024;
  private totalChunk;
  startSize;
  endSize;
  private totalSent = 0;
  file: File;
  signedURL;
  sessionUri;
  subscription_id: any;
  template_id : any;
  currentSupplier:any;
  currentSupplierName: string;
  file_path:any;
  main_file_path;
  additionalFileList = [];
  
  // File details for sidebar view
  private fileDetails$ = new BehaviorSubject<any>(null);

  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/octet-stream',
      'x-goog-resumable': 'start',
    });
  }

  // Get the selected file details
  getSelectedFileDetails(): Observable<any> {
    return this.fileDetails$.asObservable();
  }

  // Set the selected file details
  setSelectedFileDetails(fileDetails: any): void {
    this.fileDetails$.next(fileDetails);
  }

  getUUID = (subscription_id, supplier, supplier_name): Observable<any> => {
    const getSignedUrlEndpoint = this.globals.urlJoin(
      'file_upload',
      'uuid'
    );
    const options = {
      params: new HttpParams()
        .set('supplier_name', supplier_name)
    };
    return this.http.get(getSignedUrlEndpoint + subscription_id + '/uuid/' + supplier + '/', options)
  };

  validateMainFile(
    subscription_id, 
    supplier,
    channel,
    inputFormat,
    outputFormat,
    uuid,
    supplier_name
    ) {
    const getSignedUrlEndpoint = this.globals.urlJoin(
      'file_upload',
      'validate_batch_upload'
    );
    return this.http.post(getSignedUrlEndpoint + subscription_id + '/validate_batch_upload/' + supplier + '/', 
    {
      channel: channel,
      input_template: inputFormat,
      output_template: outputFormat,
      batch_file: this.main_file_path,
      uuid: uuid,
      supplier_name: supplier_name
    }
    );
  }

  /**
   *
   * @param file
   * @param moduleId
   * @returns signed url => session uri to upload file
   */
  getSignedUrl = (
    subscription_id,
    supplier,
    channel,
    name,
    inputFormat,
    outputFormat,
    file,
    isMainFile,
    uuid,
    supplier_name
  ): Observable<any> => {
    const options = {
      headers: this.httpOptions,
      observe: 'response' as 'body',
    };
    this.file = file;
    const getSignedUrlEndpoint = this.globals.urlJoin(
      'file_upload',
      'get_signed_url'
    );
    this.currentSupplier = supplier;
    this.currentSupplierName = supplier_name;
    this.subscription_id = subscription_id
    return this.http
      .post(getSignedUrlEndpoint + subscription_id + '/batch_file_upload/' + supplier + '/', {
        file_name : file.name,
        uuid: uuid,
        supplier_name: supplier_name
      })
      .pipe(
        mergeMap((obj) => {
          // console.log(obj)
          if(isMainFile) {
            this.main_file_path = obj['result']['file_path'];
          } else {
            this.additionalFileList.push(obj['result']['file_path']);
          }          
          return this.http.post(obj['result']['signed_url'], null, options);
        }),
        catchError((err) => throwError(err))
      );
  };
  allSignedURLs = {
    main:"",
    additional:[]
  };

 

  /**
   *
   * @param status
   * @returns
   */
  private updateServer = (status): Observable<void> => {
    const endpoint = this.globals.urlJoin('file_upload', 'update_server');
    return this.http.patch<void>(endpoint, status);
  };

  /**
   *
   * @param response
   * @returns the file upload progress
   */
  private updateProgress = (response: any): number => {
    if (response) {
      // this.updateServer({ status: 'succeeded' }).subscribe();
    }
    this.totalSent += 1;
    const uploadPercentage = (this.totalSent / this.totalChunk) * 100;
    return uploadPercentage;
  };

  /**
   *
   *upload file complete
   */
  uploadCompelte = (
    channel,
    name,
    inputFormat,
    outputFormat,
    file?,
    reference = "",
    description = "",
    uuid = ""
  ) => {
    const uploadCompleteEndpoint = this.globals.urlJoin(
      'file_upload',
      'upload_complete'
    );
    return this.http
      .post(
        uploadCompleteEndpoint +
          this.subscription_id   +
          '/batch_upload_complete/' +
          this.currentSupplier + '/',
        {
          channel : channel,
          input_template : inputFormat,
          output_template : outputFormat,
          batch_file : this.main_file_path,
          batch_name : name.trim(),
          reference:reference?.trim(),
          description:description?.trim(),
          additional_files:this.additionalFileList,
          uuid: uuid,
          supplier_name: this.currentSupplierName
        },
        {
          reportProgress: true,
          observe: 'events',
        }
      )
      .pipe(
        map((response: any) => {
          // console.log(response, "service")
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   *
   * @param file
   * @param moduleId
   * @returns the response after file uploaded/ file upload failed
   */
  uploadData = (
    subscription_id,
    supplier,
    channel,
    name,
    inputFormat,
    outputFormat,
    file,
    isMainFile = false,
    uuid, 
    supplier_name
  ) => {
    this.file = file;
    this.startSize = 0;
    this.endSize = 0;
    this.totalSent = 0;
    // this.subscription_id = subscription_id;
    return this.getSignedUrl(
      subscription_id,
      supplier,
      channel,
      name,
      inputFormat,
      outputFormat,
      file,
      isMainFile,
      uuid,
      supplier_name
    ).pipe(
      mergeMap((resp) => {
        // console.log(resp)
        this.sessionUri = resp.headers.get('location');
        if(isMainFile) {
          this.allSignedURLs.main = resp.headers.get('location');
        } else {
          this.allSignedURLs.additional.push(resp.headers.get('location'));
        }
        return this.createAndUploadChunk(file, this.sessionUri);
      }),
      concatMap((r) => r),
      map(this.updateProgress),
      catchError((err) => throwError(err))
    );
  };

  /**
   *
   * @param file
   * @param sessionUri
   * @returns chunks of file|sends chunks to server
   */
  createAndUploadChunk = (file: File, sessionUri: string): Observable<any> => {
    const chunks = [];
    let totalFileSize = file.size;
    let contentType = file.type;
    this.totalChunk = Math.ceil(totalFileSize / this.chunkSize);
    let headers;
    while (totalFileSize > this.endSize) {
      let slice: Blob;
      let contentRangeHeader: string;
      this.endSize = this.startSize + this.chunkSize;
      // for file size bigger than chunk size
      if (totalFileSize > this.chunkSize) {
        if (totalFileSize - this.endSize < 0) {
          this.endSize = totalFileSize;
          slice = file.slice(this.startSize, this.endSize, contentType);
          contentRangeHeader = `bytes ${this.startSize}-${this.endSize}/*`;
        } else {
          slice = file.slice(this.startSize, this.endSize + 1, contentType);
          
          contentRangeHeader = `bytes ${this.startSize}-${this.endSize}/${file.size}`;
        }
        headers = new HttpHeaders({
          'Content-Range': contentRangeHeader,
          'Chunk-Size': this.chunkSize.toString(),
        });
      } else {
        // for file size smaller than chunk size
        // no need to send content range
        slice = file.slice(this.startSize, totalFileSize, contentType);
        headers = new HttpHeaders({
          'Chunk-Size': this.chunkSize.toString(),
        });
      }
      chunks.push(
        this.http
          .put(sessionUri, slice, {
            headers: headers,
          })
          .pipe(
            map((resp: any) => {}),
            catchError((err: HttpErrorResponse) => {
              if (err.status === 308) {
                return of(undefined);
              }
              return throwError(err);
            })
          )
      );
      this.startSize = this.endSize;
    }
    
    return concat(chunks);
  };

  /**
   *
   * @returns resumes file uploaded in case upload was interrupted
   */
  resumeFileUpload = (): Observable<any> => {
    return this.http
      .put(this.sessionUri, '', {
        headers: new HttpHeaders({
          'Content-Range': `bytes */${this.file.size}`,
        }),
      })
      .pipe(
        catchError((err: HttpErrorResponse) => {
          if (err.status === 308) {
            return of(parseInt(err.headers.get('range').split('-')[1], 10));
          }
          this.updateServer({ status: 'failed' });
          return throwError(err);
        }),
        mergeMap((startFrom: number) => {
          this.startSize = startFrom + 1;
          this.endSize = this.startSize + this.chunkSize;
          return this.createAndUploadChunk(this.file, this.sessionUri);
        }),
        concatMap((r) => r),
        map(this.updateProgress),
        catchError((err) => throwError(err))
      );
  };

  resumeManualFileUpload = (isMainFile, index, file): Observable<any> => {
    return this.http
      .put( isMainFile ? this.allSignedURLs.main : this.allSignedURLs.additional[index], '', {
        headers: new HttpHeaders({
          'Content-Range': `bytes */${file.size}`,
        }),
      })
      .pipe(
        catchError((err: HttpErrorResponse) => {
          if (err.status === 308) {
            return of(parseInt(err.headers.get('range').split('-')[1], 10));
          }
          this.updateServer({ status: 'failed' });
          return throwError(err);
        }),
        mergeMap((startFrom: number) => {
          this.startSize = startFrom + 1;
          this.endSize = this.startSize + this.chunkSize;
          return this.createAndUploadChunk(file, isMainFile ? this.allSignedURLs.main : this.allSignedURLs.additional[index]);
        }),
        concatMap((r) => r),
        map(this.updateProgress),
        catchError((err) => throwError(err))
      );
  };
}
