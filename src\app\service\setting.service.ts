import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, map, catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SettingService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  getEmailNotification = (subscription_id): Observable<any> => {
    const EmailNotificationEndpoint = this.globals.urlJoin(
      'settings',
      'emialNotifications'
    );
    return this.http
      .get(
        EmailNotificationEndpoint +
          subscription_id +
          '/settings/email_notifications/'
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  modifyEmailNotificationsSetting = (
    subscription_id,
    errors_in_uploaded_file,
    errors_in_file_uploaded_by_supplier
  ): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin(
      'settings',
      'emialNotifications'
    );
    return this.http
      .post(
        createLabelEndpoint +
          subscription_id +
          '/settings/email_notifications/',
        {
          errors_in_uploaded_file: errors_in_uploaded_file,
          errors_in_file_uploaded_by_supplier:
            errors_in_file_uploaded_by_supplier,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
