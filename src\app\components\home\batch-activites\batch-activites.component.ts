import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
} from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import moment from 'moment';
import { MatSort } from '@angular/material/sort';
import {
  Router,
  ActivatedRoute,
  Params,
  NavigationStart,
} from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HomeService } from '../../../service/home.service';
import { UserService } from '../../../service/user.service';
import { HttpErrorResponse } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { ImageViewerComponent } from '../../../_dialog/image-viewer/image-viewer.component';
import { SnackbarService } from '../../../service/snackbar.service';
import { SidePanelService } from 'src/app/service/side-panel.service';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Subscription } from 'rxjs';

export interface IskuList {
  sku_id: any;
  Retailer: string;
  Manufacture_Name_Code: any;
  Supplier_Brand_Code: any;
  Manufacture_Part_No: number;
  Image_Name: string;
  SKU_Type: string;
  Price: string;
  Price_Update: string;
  Status: string;
}

@Component({
  selector: 'app-batch-activites',
  templateUrl: './batch-activites.component.html',
  styleUrls: ['./batch-activites.component.scss'],
})
export class BatchActivitesComponent implements OnInit, OnDestroy {
  displayedColumns: string[] = [
    'sku_id',
    'Retailer',
    'Supplier',
    'Brand ',
    'Manufacture_Part_No',
    'Image_Name',
    'SKU_Type',
    'Price',
    'Price_Update',
    'Status',
    'Comments',
  ];
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: string;
  customStartDate: string;
  maxDate: Date;
  minDate: Date;
  selected = 'recent';
  start_date;
  end_date;
  page: number;
  size: number;
  supplier_code: any;
  supplier_name: any;
  subscriptionId: any;
  search;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  tableLoading: boolean = false;
  dataLoading: boolean = false;
  total_pages: number;
  totalItems: number;
  skuList: any;
  skuDataSource;
  batch_id;
  sku_id;
  from;
  selectedSku = [];
  skuFilterList;

  selectedProductStatus = [];
  selectedBrands = [];
  productStatusList = [];
  brandsList = [];
  assetsList = [];
  selectedAsset = [];
  priceList = [];
  selectedPrice = '';
  globalAttribList = [];
  selectedAttribute = '';
  task: string = '';

  routeSubscription: Subscription;

  constructor(
    private router: Router,
    private activatedroute: ActivatedRoute,
    public matSnackbar: MatSnackBar,
    private homeService: HomeService,
    private userService: UserService,
    public dialog: MatDialog,
    private snackbarService: SnackbarService,
    private sidepanel: SidePanelService,
    private fb: FormBuilder
  ) {}

  ngOnInit() {
    this.routeSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.dialog.closeAll();
      }
    });
    this.dataLoading = true;
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.activatedroute.queryParams.subscribe((params: Params) => {
      // this.subscriptionId = params.sub;
      this.supplier_code = params.supplier_code;
      this.supplier_name = params.supplier_name;
      this.batch_id = params.batch_id;
      this.from = params.from;
      // console.log(params)
      localStorage.setItem('supplier_code', this.supplier_code);
      localStorage.setItem('supplier_name', this.supplier_name);
    });
    this.getSkuFilterList(this.subscriptionId);
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );

    this.batchTransferForm = this.fb.group({
      inputFormat: ['', Validators.required],
      task: new FormControl({ value: '', disabled: true }),
      tag: [''],
      batchName: [
        '',
        [
          Validators.required,
          this.noWhitespaceValidator,
          Validators.maxLength(100),
        ],
      ],
      reference: ['', [this.noWhitespaceValidator, Validators.maxLength(200)]],
      outputFormat: ['', Validators.required],
      description: [
        '',
        [this.noWhitespaceValidator, Validators.maxLength(2000)],
      ],
    });
  }

  ngOnDestroy(): void {
    this.routeSubscription.unsubscribe();
  }

  /**
   * Jump page
   * @param event
   */
  goToPage = (event) => {
    this.page = event;
    this.skuDataSource = [];
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
  };

  getSkuSelection = () => {
    // console.log(this.selectedSku);
    this.skuDataSource = [];
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
  };
  /**
   * search bar
   * @param event
   */
  getSearchValue = (event) => {
    this.search = event.trim();
    this.page = 1;
    this.size = 50;
    this.skuDataSource = [];
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
    this.paginator.firstPage();
  };

  /**
   * reset search
   */
  resetSearch = () => {
    this.search = '';
    this.skuDataSource = [];
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
    this.paginator.firstPage();
  };

  reset = () => {
    this.search = '';
    this.selectedSku = [];
    this.skuDataSource = [];
    this.page = 1;
    this.size = 50;
    this.selectedProductStatus = [];
    this.selectedBrands = [];
    this.selectedAsset = [];
    this.selectedPrice = '';
    this.selectedAttribute = '';
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
  };

  /**
   * pagination
   * @param identifier
   */
  onPaginationEvent = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.skuDataSource = [];
    this.tableLoading = true;
    this.getTable(
      this.page,
      this.size,
      this.search,
      this.selectedProductStatus,
      this.selectedBrands,
      this.selectedSku,
      this.selectedAsset,
      this.selectedPrice,
      this.selectedAttribute,
      this.subscriptionId,
      this.batch_id
    );
  };

  /**
   * Image  slider
   * @param productImg
   */
  openImageSlider(productImg) {
    this.dialog.open(ImageViewerComponent, {
      data: { productImg },
    });
  }

  getSkuFilterList = (subscription_id) => {
    this.homeService
      .getSkuFilterList(subscription_id, this.batch_id)
      .subscribe({
        next: (resp) => {
          // console.log(resp);
          this.skuFilterList = resp.result.sku_status;
          this.productStatusList = resp.result.product_status;
          this.brandsList = resp.result.brand;
          this.assetsList = resp.result.assets;
          this.priceList = resp.result.price;
          this.globalAttribList = resp.result.global_attribute;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', {
            duration: 3000,
          });
        },
      });
  };

  /**
   * table sku data
   * @param page
   * @param size
   * @param search
   * @param subscription_id
   */

  getTable = (
    page,
    size,
    search,
    product_status,
    brands,
    sku_status,
    assets,
    price,
    global_attribute,
    subscription_id,
    batch_id
  ) => {
    this.homeService
      .getSkuList(
        page,
        size,
        search,
        product_status,
        brands,
        sku_status,
        assets,
        price,
        global_attribute,
        subscription_id,
        batch_id
      )
      .subscribe({
        next: (resp) => {
          this.skuList = resp.result;
          this.totalItems = resp.total_items;
          this.size = resp.page_size;
          this.page = resp.page;
          this.total_pages = resp.total_pages;
          this.sku_id = resp.sku_id;
          this.isTransferAllow = resp['allow_transfer'];
          const HOME_DATA: IskuList[] = this.skuList;

          this.skuDataSource = new MatTableDataSource<IskuList>(HOME_DATA);
          this.dataLoading = false;
          this.tableLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.tableLoading = false;
          this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
        },
      });
  };

  // open sidepanel
  panelTitle;
  inputFormatList = [];
  outputFormatList = [];
  tagsList = [];
  batchTransferForm: FormGroup;
  isServiceBusy: boolean = false;
  activeModule;
  isTransferAllow: boolean = false;
  openPanel(module) {
    this.batchTransferForm.controls['task'].disable();
    module == 'r2e'
      ? (this.panelTitle = 'Send to Content Enrichment')
      : (this.panelTitle = 'Send to Validate Existing SKUs');
    this.sidepanel.setShowNav(true);
    this.getInputOutput(this.subscriptionId, module);
    this.activeModule = module;
  }

  getInputOutput(subscription_id, module) {
    this.homeService
      .getModuleDetails(
        subscription_id,
        module,
        this.selectedProductStatus,
        this.selectedBrands,
        this.selectedSku,
        this.selectedAsset,
        this.selectedPrice,
        this.selectedAttribute
      )
      .subscribe({
        next: (resp) => {
          // resp['result']['task'] = 'some task';
          this.task = resp.result.task;
          this.batchTransferForm.patchValue({ task: this.task });
          this.tagsList = resp.result.tags;
          this.inputFormatList = resp.result.input_formats;
          this.outputFormatList = resp.result.output_formats;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar('undefined', 'OK');
        },
      });
  }

  onTransfer() {
    this.isServiceBusy = true;
    this.batchTransferForm.disable();
    const formData = this.batchTransferForm.value;
    const payload = {
      module: this.activeModule,
      search: this.search,
      product_status: this.selectedProductStatus,
      brand: this.selectedBrands,
      sku_status: this.selectedSku,
      assets: this.selectedAsset,
      price: this.selectedPrice,
      global_attribute: this.selectedAttribute,
      batch_name: formData['batchName'].trim(),
      task: formData['task'],
      tag_id: formData['tag'],
      input_format_id: formData['inputFormat'],
      output_format_id: formData['outputFormat'],
      reference: formData['reference']?.trim(),
      description: formData['description']?.trim(),
    };
    this.homeService
      .transferBatch(payload, this.subscriptionId, this.batch_id)
      .subscribe({
        next: (resp) => {
          this.snackbarService.openSnackBar(`${resp['detail']}`, 'OK');
          this.closePanel();
          this.batchTransferForm.enable();
        },
        error: (error) => {
          this.isServiceBusy = false;
          this.batchTransferForm.enable();
          this.snackbarService.openSnackBar(
            `${error['error']['detail']}`,
            'OK'
          );
        },
      });
  }

  /**
  Custom Validator to restrict whitespace in Input fields 
  **/
  public noWhitespaceValidator(control: FormControl) {
    if (control.value) {
      const isWhitespace = (control.value || '').trim().length == 0;
      const isValid = !isWhitespace;
      return isValid ? null : { whitespace: true };
    }
    return null;
  }

  closePanel() {
    this.isServiceBusy = false;
    this.activeModule = null;
    this.batchTransferForm.reset();
    this.sidepanel.toggleNavState();
  }
}
