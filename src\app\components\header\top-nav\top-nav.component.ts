import { Component, OnInit } from '@angular/core';
import { Auth0Service } from '../../../service/auth0.service';
import { UserService } from 'src/app/service/user.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-top-nav',
  templateUrl: './top-nav.component.html',
  styleUrls: ['./top-nav.component.scss'],
})
export class TopNavComponent implements OnInit {
  user;
  subscriptionId;
  constructor(
    private auth0Service: Auth0Service,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    if (this.subscriptionId) {
      this.userService.me(this.subscriptionId).subscribe({
        next: (resp) => {
          this.user = resp.result;
          console.log(this.user)
          localStorage.setItem('user', JSON.stringify(resp.result));
        },
      });
    }
  }

  logUserOut = () => {
    localStorage.clear();
    this.auth0Service.logUserOut();
  };

  stopPropagation(event: any) {
    event.stopPropagation();
  }
}
