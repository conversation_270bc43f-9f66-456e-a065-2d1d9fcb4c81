@import "../../../styles/variables";

.add-supplier-btn {
  color: #fff;
}
.table-wrapper {
  // margin-left: 100px;
  // margin-right: 2%;
  // margin-top: 210px;
  margin-bottom: 40px;
  .no-sku-data {
    padding: 8% 0;
    color: #1475eb;
  }
  .action-btn {
    padding: 10px;
    button {
      padding: 5px;
      width: 100%;
      font-family: $site-font;
      border-radius: 4px;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 21px;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .view-batch {
      color: $theme-white;
    }
    .pause-btn {
      background-color: $theme-white;
      color: $red-100;
      border: 1px solid $red-100;
    }
  }
}
.data-loading-spinner {
  height: 500px;
}

.add-vendor-panel {
  margin-top: 50px;
  // padding: 0 10px;

  .panel-header {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }

  .vendor-form {
    margin-top: 20px;

    p {
      margin-top: 14px;
      font-weight: 600;
      font-size: 14px;
      color: #222329;
    }

    mat-form-field {
      width: 100%;
    }

    button {
      margin-top: 0px;
      height: 40px;
      border-radius: 4px;
      font-weight: 600;
    }
  }
}