.ticket-view-wrapper {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ticket-view-container {
  width: calc(100vw - 80px);
  margin-left: 75px;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
  background: #fff;
  padding: 32px 0 0 0;
  display: flex;
  flex-direction: column;
}

.ticket-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px 24px 32px;
  border-bottom: 1px solid #e5e7eb;
}

.ticket-header-title span {
  font-size: 2rem;
  font-weight: 600;
  color: #23272e;
}

.ticket-tabs-row {
  display: flex;
  border-bottom: 1.5px solid #e5e7eb;
  margin: 0 32px;
  gap: 32px;
  align-items: center;
  position: relative;
  padding: 0;
}

.ticket-tab {
  padding: 16px 0;
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2.5px solid transparent;
  transition: color 0.2s, border-color 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-tab.active {
  border-bottom: 2.5px solid #2e5bff;
  background-color: transparent !important;
  color: #2e5bff;
}

.tab-badge {
  background: #e5e7eb;
  color: #23272e;
  border-radius: 12px;
  font-size: 0.85em;
  padding: 2px 8px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
}

.ticket-list {
  margin: 0 32px;
  padding: 24px 0 32px 0;
  flex: 1;
  overflow-y: auto;
  position: relative;
}

/* Ticket list loader styling */
.ticket-list-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* No data message styling */
.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 40px 20px;
  margin: 20px;
  box-sizing: border-box;
}

.no-data-message::before {
  content: "📋";
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.ticket-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 20px 24px;
  box-shadow: 0 1px 4px rgba(16, 30, 54, 0.04);
  border: 1px solid #f0f1f3;
  transition: background 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.ticket-item:hover {
  background: #f0f4ff;
  box-shadow: 0 2px 8px rgba(46, 91, 255, 0.08);
}

.ticket-item.unread {
}

.ticket-item-main {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.ticket-item-type-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.ticket-item-type {
  font-weight: 600;
  color: #23272e;
  font-size: 1.05rem;
  margin: 0;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ticket-item-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #dde6fd;
  color: #0c4af3;
  border-radius: 8px;
  font-size: 0.85em;
  padding: 4px 8px;
  margin: 0;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Status badge styles for open/closed states */
.status-badge {
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
}

.status-badge.open {
  background: #f3f4f6 !important;
  color: #6b7280 !important;
}

.status-badge.closed {
  background: #fee2e2 !important;
  color: #991b1b !important;
}

.ticket-item-message {
  color: #6b7280;
  font-size: 0.98rem;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ticket-item-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 100px;
  flex-shrink: 0;
  margin-left: 16px;
  margin-right: 0;
  text-align: right;
}

.ticket-item-date {
  color: #6b7280;
  font-size: 0.95rem;
  text-align: right;
  white-space: nowrap;
}

.ticket-item-user {
  color: #000000;
  font-size: 0.95rem;
  text-align: right;
  white-space: nowrap;
}

.by {
  color: #888;
}

.ticket-item.selected {
  border: 2px solid #5e7ff7;
  background: #eaf1ff;
}

.cdk-overlay-container {
  z-index: 2000 !important;
}

/* --- Modern Sidebar Styles --- */

.sidebar-backdrop {
  position: fixed;
  top: 70px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 70px);
  background: rgba(30, 34, 45, 0.25);
  z-index: 2999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4,0,0.2,1);
  backdrop-filter: blur(2px);
}

.sidebar-backdrop.sidebar-open {
  opacity: 1;
  pointer-events: auto;
}

.ticket-sidebar {
  position: fixed;
  top: 70px;
  right: 0;
  width: 60vw;
  height: calc(100vh - 70px);
  background: #fff;
  box-shadow: -8px 0 32px rgba(0,0,0,0.15);
  border-left: 1px solid #e1e4e8;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  pointer-events: none;
  max-width: 800px;
  min-width: 500px;
  box-sizing: border-box;
}

.ticket-sidebar.sidebar-open {
  transform: translateX(0);
  pointer-events: auto;
}

/* --- Modern Header Section --- */
.sidebar-header {
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  padding: 0;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
}

.header-title {
  font-size: 20px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
  line-height: 1.2;
}

.header-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: #656d76;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.header-close-btn:hover {
  background: #f6f8fa;
  color: #24292f;
  transform: scale(1.05);
}

.header-close-btn mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* --- Ticket Info Vertical Layout --- */
.ticket-info-vertical {
  padding: 20px 24px 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #f6f8fa;
  border-bottom: 1px solid #e1e4e8;
  margin-bottom: 0;
}

.info-line {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.info-label {
  font-size: 13px;
  font-weight: 600;
  color: #24292f;
  min-width: 90px;
  flex-shrink: 0;
}

.info-value {
  font-size: 13px;
  color: #656d76;
  font-weight: 400;
  flex: 1;
}

/* --- Main Content Area --- */
.sidebar-content {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
  background: #fff;
  padding: 0;
}

/* --- Form Section --- */
.form-section {
  padding: 24px 24px 0px 24px;
  background: #fff;
  border-bottom: 1px solid #f6f8fa;
}

.form-field-group {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.form-field-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 8px;
  line-height: 1.4;
}

.required {
  color: #d1242f;
  font-weight: 700;
  margin-left: 2px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  color: #24292f;
  background: #fff;
  transition: all 0.2s ease;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.12);
}

.form-input:disabled,
.form-textarea:disabled {
  background: #f6f8fa;
  color: #656d76;
  cursor: not-allowed;
  border-color: #d0d7de;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #656d76;
  opacity: 1;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}

/* Form validation styles */
.form-input.error,
.form-textarea.error {
  border-color: #d1242f;
  box-shadow: 0 0 0 3px rgba(209, 36, 47, 0.12);
}

.error-message {
  color: #d1242f;
  font-size: 12px;
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message::before {
  content: "⚠";
  font-size: 14px;
}

/* Loading spinner */
.sidebar-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
}

.spinner {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* --- Timeline Section --- */
.timeline-section {
  padding: 24px;
  flex: 1 1 auto;
  min-height: 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
}

/* --- GitLab-style Threaded Comments --- */
.threaded-comments {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  padding-left: 0;
  margin-left: 48px;
}

.threaded-comments::before {
  content: none;
}

.comment-thread-card {
  position: relative;
  background: #fff;
  border: 1.5px solid #e1e4e8;
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 2px 12px rgba(16, 30, 54, 0.06);
  margin-bottom: 18px;
  margin-left: 0;
}

.comment-thread-card::before {
  content: none;
}

.thread-header-row {
  display: flex;
  align-items: center;
  padding: 20px 20px 0 20px;
  gap: 12px;
  min-height: 60px; /* Ensure consistent height */
}

.thread-avatar {
  flex-shrink: 0;
  z-index: 2;
  margin-left: 0;
  margin-right: 12px;
}

.thread-avatar .avatar-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: #fff;
  overflow: hidden;
  background: #bdbdbd;
  box-shadow: 0 0 0 2px #fff, 0 1px 3px rgba(0,0,0,0.1);
}

.thread-avatar .avatar-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.thread-avatar.small .avatar-circle {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.thread-header-main {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.thread-username {
  font-weight: 600;
  color: #24292f;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.thread-timestamp {
  color: #656d76;
  font-size: 12px;
  margin-left: 4px;
  font-weight: 400;
  white-space: nowrap;
}

.thread-header-actions {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.comment-thread-card:hover .thread-header-actions {
  opacity: 1;
}

.thread-header-actions button {
  color: #656d76;
  font-size: 14px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.thread-header-actions button:hover {
  color: #0969da;
  background: #f6f8fa;
  transform: scale(1.05);
}

.thread-body {
  padding: 12px 20px 16px 64px;
  color: #24292f;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
}

.collapse-replies-row {
  padding: 12px 20px 12px 64px;
  border-top: 1px solid #f6f8fa;
  margin-top: 8px;
  background: #fafbfc;
}

.collapse-replies-btn {
  color: #0969da;
  font-weight: 500;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.collapse-replies-btn:hover {
  color: #0858b9;
  background: #dbeafe;
  transform: translateY(-1px);
}

.collapse-replies-btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #0969da !important;
  transition: transform 0.2s ease;
}

.collapse-replies-btn:hover mat-icon {
  transform: scale(1.1);
}

.thread-reply-row {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px 0 64px;
  gap: 12px;
  background: transparent;
  border-radius: 0;
  border-left: none;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
  border-left: 3px solid #f6f8fa;
  margin-left: 20px;
  padding-left: 44px;
}

.thread-reply-main {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.thread-reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.thread-reply-actions {
  display: flex;
  gap: 2px;
  margin-left: auto;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.thread-reply-row:hover .thread-reply-actions {
  opacity: 1;
}

.thread-reply-actions button {
  color: #656d76;
  font-size: 14px;
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.thread-reply-actions button:hover {
  color: #0969da;
  background: #f6f8fa;
  transform: scale(1.05);
}

.thread-reply-body {
  color: #24292f;
  font-size: 14px;
  margin-top: 2px;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
}

.thread-footer-row {
  background: #f6f8fa;
  border-radius: 0 0 12px 12px;
  border-top: 1px solid #e1e4e8;
  padding: 16px 20px 16px 64px;
  margin-top: 8px;
  margin-bottom: 0;
  animation: editModeEnter 0.3s ease-out;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modern-quill-editor.reply-mode {
  border-radius: 8px;
  background: #fff;
  border: 1px solid #d0d7de;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 0;
  min-height: 80px;
  max-height: 180px;
}

.modern-quill-editor.reply-mode .ql-toolbar {
  border-bottom: 1px solid #e1e4e8;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
  min-height: 40px;
  padding: 6px 12px;
}

.modern-quill-editor.reply-mode .ql-editor {
  min-height: 60px;
  max-height: 120px;
  padding: 12px 16px;
  border-radius: 0 0 8px 8px;
  font-size: 14px;
  background: #fff;
}

.thread-reply-btn,
.resolve-thread-btn {
  width: auto !important;
  min-width: 0 !important;
  margin-bottom: 0 !important;
  justify-content: flex-start;
}

.edit-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 0;
}

.thread-footer-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px 0;
}

.thread-footer-text {
  color: #656d76;
  font-size: 14px;
  font-style: italic;
}


/* --- Comment Input Section --- */
.comment-input-section {
  background: #fff;
  border-top: 1px solid #e1e4e8;
  padding: 16px 24px 0px;
  margin-top: 0;
  flex-shrink: 0;
  overflow: visible !important;
  position: relative;
}

.comment-input-container {
  position: relative;
}

.comment-input-container.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.comment-input-container.disabled .modern-quill-editor {
  background: #f6f8fa;
}

.comment-input-container.disabled .ql-toolbar {
  background: #f6f8fa;
  opacity: 0.7;
}

.comment-input-container.disabled .ql-editor {
  background: #f6f8fa;
  color: #656d76;
  cursor: not-allowed;
}

.pending-file-list {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pending-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f6f8fa;
  border-radius: 6px;
  border: 1px solid #e1e4e8;
}

.pending-file-name {
  flex: 1;
  font-size: 14px;
  color: #24292f;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.comment-actions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  z-index: 10;
}

.comment-send-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #0969da;
  color: #fff;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.comment-send-btn:hover:not(.disabled) {
  background: #0858b9;
  transform: scale(1.05);
}

.comment-send-btn.disabled {
  background: #d0d7de;
  color: #656d76;
  cursor: not-allowed;
  box-shadow: none;
}

.comment-send-btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Reply to Ticket Button */
.reply-to-ticket-section {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.reply-to-ticket-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* --- Modern Quill Editor Styles --- */
.modern-quill-editor {
  background: transparent;
  border: none;
  box-shadow: none;
}

.modern-quill-editor .ql-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  min-height: 44px;
  padding: 8px 12px;
  border-bottom: 1px solid #e1e4e8;
  background: #fafbfc;
}

.modern-quill-editor .ql-toolbar button,
.modern-quill-editor .ql-toolbar .ql-picker {
  display: flex;
  align-items: center;
  height: 32px;
  min-width: 32px;
  justify-content: center;
  vertical-align: middle;
  margin-top: 0;
  margin-bottom: 0;
}

.modern-quill-editor .ql-toolbar .ql-picker-label,
.modern-quill-editor .ql-toolbar .ql-picker-options {
  display: flex;
  align-items: center;
}

.modern-quill-editor .ql-toolbar .ql-stroke,
.modern-quill-editor .ql-toolbar .ql-fill,
.modern-quill-editor .ql-toolbar .ql-picker {
  vertical-align: middle;
}

.modern-quill-editor .ql-toolbar .ql-formats {
  display: flex;
  align-items: center;
  gap: 2px;
}

.modern-quill-editor .ql-toolbar button:hover,
.modern-quill-editor .ql-toolbar button:focus {
  background: #e1e4e8;
  color: #24292f;
}

.modern-quill-editor .ql-toolbar .ql-active {
  background: #0969da;
  color: #fff;
}

.modern-quill-editor .ql-toolbar .ql-picker {
  margin-right: 4px;
  position: relative;
}

.modern-quill-editor .ql-toolbar .ql-picker-label {
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  color: #656d76;
}

.modern-quill-editor .ql-toolbar .ql-picker-label:hover {
  background: #e1e4e8;
  color: #24292f;
}

.modern-quill-editor .ql-toolbar .ql-picker-options {
  background: #fff;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 4px 0;
}

.modern-quill-editor .ql-toolbar .ql-picker-item {
  padding: 4px 12px;
  color: #24292f;
}

.modern-quill-editor .ql-toolbar .ql-picker-item:hover {
  background: #f6f8fa;
}

.modern-quill-editor .ql-toolbar .ql-picker-item.ql-selected {
  background: #0969da;
  color: #fff;
}

/* Quill dropdown positioning overrides */
:host ::ng-deep .ql-toolbar .ql-picker.ql-expanded .ql-picker-options {
  bottom: 100% !important;
  top: auto !important;
  margin-bottom: 4px !important;
  transform: none !important;
  position: absolute !important;
  z-index: 9999 !important;
  overflow: visible !important;
  display: block !important;
}

:host ::ng-deep .ql-toolbar .ql-picker {
  position: relative !important;
}

:host ::ng-deep .ql-toolbar .ql-picker.ql-expanded {
  z-index: 10000 !important;
}

/* --- Footer Section --- */
.sidebar-footer {
  background: #fff;
  border-top: 1px solid #e1e4e8;
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0;
}

.btn-secondary {
  padding: 8px 16px;
  border: 1px solid #d0d7de;
  background: #fff;
  color: #24292f;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary:hover:not(:disabled) {
  background: #f6f8fa;
  border-color: #656d76;
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  padding: 8px 16px;
  border: 1px solid #0969da;
  background: #0969da;
  color: #fff;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary:hover:not(:disabled) {
  background: #0858b9;
  border-color: #0858b9;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #d0d7de;
  border-color: #d0d7de;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
  .ticket-view-container {
    width: 100vw;
    margin-left: 0;
    padding: 16px 0 0 0;
  }

  .ticket-header-row {
    padding: 0 16px 16px 16px;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .ticket-header-title {
    text-align: center;
  }

  .ticket-tabs-row {
    margin: 0 16px;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 4px;
  }

  .ticket-list {
    margin: 0 16px;
    padding: 16px 0 16px 0;
  }

  .no-data-message {
    margin: 10px;
    padding: 30px 15px;
    font-size: 14px;
  }

  .ticket-item {
    flex-direction: column;
    align-items: stretch;
    padding: 16px;
  }

  .ticket-item-type-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .ticket-item-meta {
    flex-direction: row;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
    margin-left: 0;
    text-align: left;
  }

  .ticket-item-user, .ticket-item-date {
    text-align: left;
  }

  .ticket-sidebar {
    width: 100vw;
    max-width: 100vw;
    min-width: 100vw;
    top: 0;
    height: 100vh;
  }

  .form-section,
  .timeline-section,
  .comment-input-section {
    padding: 16px;
  }

  .sidebar-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .sidebar-footer button {
    width: 100%;
    justify-content: center;
  }

  .threaded-comments {
    margin-left: 24px;
    padding-left: 0;
  }

  .timeline-avatars-absolute {
    width: 24px;
  }

  .timeline-avatar-abs-wrapper {
    left: 0;
  }

  .timeline-avatar {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .timeline-full-line {
    left: 12px;
    width: 2px;
  }

  .thread-header-row {
    padding: 12px 12px 0 12px;
    gap: 8px;
    min-height: 48px;
  }

  .thread-body {
    padding: 8px 12px 12px 36px;
    font-size: 14px;
  }

  .thread-reply-row {
    padding: 8px 12px 0 36px;
    gap: 8px;
    margin-left: 12px;
    padding-left: 24px;
  }

  .thread-footer-row {
    padding: 12px 12px 12px 36px;
    border-radius: 0 0 8px 8px;
  }

  .collapse-replies-row {
    padding: 8px 12px 8px 36px;
  }

  .comment-thread-card {
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(16, 30, 54, 0.08);
    margin-bottom: 12px;
  }

  .thread-header-actions {
    opacity: 1; /* Always show on mobile */
  }

  .thread-reply-actions {
    opacity: 1; /* Always show on mobile */
  }
}

/* --- Comment Edit Mode Styles --- */
.comment-edit-container {
  position: relative;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid #23272e;
  animation: editModeEnter 0.3s ease-out;
}

@keyframes editModeEnter {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.comment-edit-container .modern-quill-editor.edit-mode {
  border-radius: 8px;
  background: #fff;
  margin-bottom: 12px;
  box-shadow: 0 0 0 3px rgba(46, 91, 255, 0.1);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.comment-edit-container .modern-quill-editor.edit-mode:focus-within {
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(46, 91, 255, 0.2);
}

.comment-edit-container .modern-quill-editor.edit-mode .ql-toolbar {
  border-bottom: 1px solid #e1e4e8;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
}

.comment-edit-container .modern-quill-editor.edit-mode .ql-editor {
  min-height: 80px;
  max-height: 200px;
  padding: 12px 16px;
  border-radius: 0 0 8px 8px;
}

.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.edit-actions button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-color: transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 32px;
}

.edit-actions button mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.edit-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.edit-actions button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Loading state for edit actions */
.edit-actions button.loading {
  position: relative;
  color: transparent;
}

.edit-actions button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* --- Additional utility classes --- */
.comment-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
  width: 100%;
}

/* Comment attachments styling */
.comment-attachments {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.comment-attachment-item {
  display: flex;
  align-items: center;
}

.comment-attachment-item button {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #d0d7de;
  background: #f6f8fa;
  color: #24292f;
  font-size: 13px;
  transition: all 0.2s ease;
}

.comment-attachment-item button:hover {
  background: #e1e4e8;
  border-color: #afb8c1;
}

.comment-attachment-item mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Override Angular Material styles */
:host ::ng-deep .ql-editor p {
  font-size: 14px;
  color: #24292f;
  line-height: 1.5;
  word-break: break-word;
  margin: 0 0 8px 0;
}

:host ::ng-deep .ql-editor p:last-child {
  margin-bottom: 0;
}

:host ::ng-deep .ql-formats {
  margin-right: 4px !important;
}

:host ::ng-deep .ql-editor {
  max-height: 120px;
  min-height: 80px;
  padding: 12px 16px;
}

:host ::ng-deep .ql-container,
:host ::ng-deep .ql-toolbar,
:host ::ng-deep .ql-editor {
  border: none !important;
  box-shadow: none !important;
}

::ng-deep .mat-form-field-wrapper {
  padding: 0 !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 8px !important;
}

/* Remove LemonadeJS Timeline */
#lemonade-timeline {
  display: none !important;
}
.timeline-avatars {
  width: 48px;
}

.timeline-avatar-wrapper {
  position: relative;
  align-items: center;
  display: flex;
  flex-direction: column;
  min-height: 80px;
}

.timeline-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  z-index: 2;
  margin: 0;
  box-shadow: 0 0 0 2px #fff;
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.timeline-line-above,
.timeline-line-below {
  width: 4px;
  background: #e0e0e0;
  min-height: 16px;
  flex: 1 1 auto;
  z-index: 1;
}

.timeline-line-above {
  margin-bottom: 0;
  border-radius: 2px 2px 0 0;
}

.timeline-line-below {
  margin-top: 0;
  border-radius: 0 0 2px 2px;
}

.timeline-avatars-absolute {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 48px;
  pointer-events: none;
  z-index: 10;
}

.timeline-avatar-abs-wrapper {
  position: absolute;
  left: 6px;
  z-index: 12;
  transform: translateY(20px); /* Align with comment card header */
}

.timeline-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  background: #fff;
  box-shadow: 0 0 0 3px #fff, 0 2px 8px rgba(0,0,0,0.1);
  color: #fff;

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  span {
    font-weight: 600;
    font-size: 14px;
  }
}

.timeline-full-line {
  position: absolute;
  left: 24px;
  top: 0;
  width: 3px;
  background: linear-gradient(to bottom, #e1e4e8 0%, #e1e4e8 100%);
  z-index: 11;
  border-radius: 2px;
}

.threaded-comments {
  margin-left: 48px;
}

.threaded-comments-with-timeline {
  position: relative;
  display: flex;
}

.timeline-avatars-absolute {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 48px;
  pointer-events: none;
}

.timeline-avatar-abs-wrapper {
  position: absolute;
  left: 6px;
  z-index: 2;
}

.timeline-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  background: #fff;
  box-shadow: 0 0 0 2px #fff;
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.timeline-full-line {
  position: absolute;
  left: 24px;
  top: 0;
  width: 4px;
  background: #e0e0e0;
  z-index: 1;
}

.threaded-comments {
  margin-left: 48px;
}



