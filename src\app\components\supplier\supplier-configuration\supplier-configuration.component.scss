@import "../../../../styles/variables";

.search-filter {
  flex-grow: 1;
  width: 491px;
  height: 40px;
  border: 1px solid #c1c4d6;
  box-sizing: border-box;
  border-radius: 4px;

  input {
    width: 100%;
    height: 19px;
    margin-left: 5px;
    margin-top: -12px;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .search-icon {
    font-size: 30px;
    color: #b6b6b6;
    padding: 5px;
    cursor: pointer;
  }

  .mat-form-field-suffix {
    bottom: 5px;
    .remove-icon {
      font-size: 24px;
      color: #b6b6b6;
      padding: 5px;
      cursor: pointer;
    }
  }
}

// Supplier Configuration Header Styles
.supplier-configuration-header {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  
  .supplier-name {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #111827;
  }
  
  .supplier-code {
    font-size: 20px;
    color: #6B7280;
    font-weight: 400;
  }
  
  .status-badge {
    background-color: #ECFDF5;
    color: #10B981;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .edit-button {
    border: 1px solid #D1D5DB;
    border-radius: 4px;
    color: #111827;
    
    mat-icon {
      font-size: 18px;
      height: 18px;
      width: 18px;
      margin-right: 4px;
    }
  }
  
  .supplier-details {
    margin-top: 8px;
    
    .detail-column {
      .detail-label {
        font-size: 14px;
        color: #6B7280;
        margin-bottom: 8px;
      }
      
      .detail-value {
        font-size: 16px;
        color: #111827;
        font-weight: 500;
      }
    }
  }
}

// Header buttons style
.right-action-container {
  button {
    height: 36px !important;
    line-height: 36px;
    border-radius: 4px;
    font-weight: 500;
    min-width: 120px;
    padding: 0 16px;
    
    img, mat-icon {
      vertical-align: middle;
    }
    
    .add-icon {
      color: white;
    }
    
    // Add even spacing for icon and text
    span {
      margin-left: 8px;
    }
  }
}

.search-filter-file {
  ::ng-deep .mat-form-field-suffix {
    position: relative;
    top: -8px;
    .remove-icon {
      position: relative;
      top: 5px;
      padding-left: 5px;
    }
  }
}

.upload-panel-content {
  // padding: 0 20px 20px 20px;
  margin-top: 50px;
  
  .panel-header {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
  
  .file-selection-panel {
    .file-list-header {
      margin-bottom: 16px;
      
      p {
        font-size: 14px;
        color: #6b7280;
      }
    }
    
    .file-selection-list {
      margin-bottom: 20px;
      max-height: 300px;
      overflow-y: auto;
      
      .file-selection-item {
        padding: 10px;
        border-radius: 8px;
        background-color: #f9fafb;
        margin-bottom: 10px;
        
        .file-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          
          .file-name {
            flex: 1;
            font-size: 14px;
            color: #111827;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    
    .action-buttons {
      margin-top: 16px;
      
      .add-btn {
        background-color: #3f7e45;
        color: white;
      }
    }
  }
  
  .upload-form {
    width: 100%;

    p {
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 14px;
    }

    .full-width {
      width: 100%;
    }

    .mt-20 {
      margin-top: 20px;
    }

    ngx-dropzone {
      height: auto;
      min-height: 150px;
      background: #f8f9fa;
      border: 2px dashed #c1c4d6;
      border-radius: 8px;
      
      &:hover {
        background: #f0f2f5;
      }

      ngx-dropzone-label {
        padding: 20px;
        text-align: center;
        color: #6c757d;

        mat-icon {
          font-size: 36px;
          width: 36px;
          height: 36px;
          margin-bottom: 10px;
        }

        span {
          margin: 5px 0;
        }

        button {
          margin-top: 10px;
        }
      }
    }

    .file-list {
      margin-top: 15px;
      
      .file-item {
        background: rgba(248, 249, 250, 0.8);
        border: 1px solid #e6e8eb;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 8px;
        
        .file-name {
          color: #434956;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 90%;
        }
        
        .spacer {
          flex: 1;
        }
        
        mat-icon {
          color: #6c757d;
          font-size: 18px;
          height: 18px;
          width: 18px;
        }
      }
    }

    .app-dropdown {
      border: 1px solid #c1c4d6;
      border-radius: 4px;
      height: 40px;
      padding: 0 8px;
      margin-bottom: 16px;
    }

    .manual-upload-input {
      border: 1px solid #c1c4d6;
      border-radius: 4px;
      height: 40px;
      padding: 0 8px;
      margin-bottom: 16px;
    }
    
    .description-textarea {
      height: auto;
      min-height: 80px;
      padding: 8px;
      resize: vertical;
    }

    .form-fields {
      margin-top: 20px;
    }
    
    .form-field-container {
      margin-bottom: 15px;
      
      p {
        margin-bottom: 8px;
        font-weight: 500;
        font-size: 14px;
        color: #434956;
      }
    }
    
    .validation-error {
      color: #f44336;
      font-size: 12px;
      margin-top: -12px;
      margin-bottom: 8px;
    }

    .selected-channels {
      margin-top: -10px;
      margin-bottom: 15px;
      
      mat-chip-list {
        ::ng-deep .mat-chip-list-wrapper {
          margin: 0;
        }
      }
      
      mat-chip {
        margin: 4px 4px 4px 0;
        background-color: #f2f2f2;
        color: #434956;
        border-radius: 20px;
        padding: 4px 12px;
        
        &::after {
          background-color: rgba(0, 0, 0, 0.05);
        }
        
        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          margin-left: 4px;
          color: #6c757d;
        }
        
        &.upload-chip {
          background-color: #f2f2f2;
        }
        
        &.ftp-chip {
          background-color: #f2f2f2;
        }
        
        &.email-chip {
          background-color: #f2f2f2;
        }
      }
    }

    .description {
      margin-bottom: 16px;
    }

    .upload-err-msg {
      color: #f44336;
      font-size: 12px;
      margin-bottom: 10px;
    }

    .action-buttons {
      margin-top: 20px;
      margin-bottom: 20px;

      width: 100%;
      
      button {
        min-width: 120px;
        height: 44px;
        font-weight: 500;
      }
      
      .sidePanel-upload-btn {
        font-weight: 500;
      }
    }
  }
}

.filled-btn-primary {
  background-color: #3366ff;
  color: white;
  &:disabled {
    background-color: rgba(51, 102, 255, 0.6);
  }
}

// Table view for files
.table-view-for-files {
  width: 100%;
  overflow-x: hidden;
  
  .table-section {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    overflow-x: auto;
    
    table {
      width: 100%;
      table-layout: fixed;
      
      th.mat-header-cell {
        font-size: 12px;
        font-weight: 500;
        font-family: "Poppins", sans-serif;
        background-color: #EDEFF5;
        padding: 16px 8px;
        white-space: nowrap;
      }
      
      td.mat-cell {
        padding: 16px 8px;
        color: #434956;
        font-size: 14px;
        border-bottom: 1px solid #edeff5;
        word-wrap: break-word;
        
        .file-name {
          font-weight: 500;
          color: #434956;
        }
        
        .file-type {
          font-weight: 400;
        }
        
        .status-indicator {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          border-radius: 16px;
          width: fit-content;
          
          mat-icon {
            font-size: 16px;
            height: 16px;
            width: 16px;
            margin-right: 4px;
          }
          
          &.in-review {
            background-color: #f3f6fa;
            color: #6b7280;
            
            mat-icon {
              color: #6b7280;
            }
          }
          
          &.active {
            background-color: #ecfdf5;
            color: #10b981;
            
            mat-icon {
              color: #10b981;
            }
          }
          
          &.setup {
            background-color: #fff7ed;
            color: #f59e0b;
            
            mat-icon {
              color: #f59e0b;
            }
          }
        }
        
        .user-avatar {
          .avatar {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background-color: #6366f1;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 500;
          }
        }
        
        .upload-date {
          color: #6b7280;
        }
        
        .channel-chips {
          .channel-chip {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 17px;
            font-size: 12px;
            margin-right: 4px;
            font-weight: 500;
            background-color: #f3f4f6;
            color: #4b5563;
            
            // &.upload {
            //   background-color: #E3E304;
            //   color: #4b5563;
            // }
            
            // &.ftp {
            //   background-color: #A1BA9D;
            //   color: #4b5563;
            // }
            
            // &.email {
            //   background-color: #A1BA9D;
            //   color: #4b5563;
            // }
            
            // &.api {
            //   background-color: #A1BA9D;
            //   color: #4b5563;
            // }
          }
        }
        
        .action-buttons {
          button {
            color: #6b7280;
            
            // &:hover {
            //   color: #111827;
            // }
          }
        }
      }
      
      // tr.mat-row {
      //   &:hover {
      //     background-color: #f9fafb;
      //   }
      // }
    }
    
    .no-data {
      padding: 24px;
      color: #6b7280;
      font-size: 14px;
      
      mat-icon {
        color: #9ca3af;
        margin-right: 8px;
      }
    }
    
    .custom-paginator {
      margin-top: 16px;
      
      .jump-to-page {
        color: #6b7280;
        font-size: 14px;
        
        input {
          width: 40px;
          height: 32px;
          text-align: center;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          color: #111827;
          font-weight: 500;
          
          &:focus {
            outline: none;
            border-color: #6366f1;
          }
        }
      }
    }
  }
}

// Users tab styles
.table-view-for-users {
  width: 100%;
  overflow-x: hidden;
  
  .table-section {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    overflow-x: hidden;
    
    table {
      width: 100%;
      table-layout: fixed;
      
      th.mat-header-cell {
        font-size: 12px;
        font-weight: 500;
        font-family: "Poppins", sans-serif;
        background-color: #EDEFF5;
        padding: 16px 8px;
        white-space: nowrap;
      }
      
      td.mat-cell {
        padding: 16px 8px;
        color: #434956;
        font-size: 14px;
        border-bottom: 1px solid #edeff5;
        word-wrap: break-word;
      }
      
      .status-badge {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 16px;
        width: fit-content;
        font-size: 12px;
        
        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
        }
        
        &.invite-sent {
          background-color: rgba(0, 0, 0, 0.08);
          color: #6B7280;
        }
        
        &.invite-expired {
          background-color: #FEF2F2;
          color: #DC2626;
        }
      }
      
      .status-indicator.user-active-status {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-weight: 500;
      }
      
      .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .resend-button {
          height: 32px;
          line-height: 32px;
          border: 1px solid #D1D5DB;
          color: #6B7280;
          
          mat-icon {
            font-size: 16px;
            height: 16px;
            width: 16px;
          }
        }
        
        .delete-button {
          color: #DC2626;
        }
      }
    }
  }
}

// Invite users panel styles
.invite-users-panel {
  margin-top: 50px;
  height: 100%;
  // padding: 0 16px;
  
  .panel-header {
    margin: 0;
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    color: #111827;
  }
  
  .invite-form {
    margin-top: 20px;
    
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #6B7280;
      margin-bottom: 12px;
    }
    
    .email-inputs {
      .email-input-row {
        width: 100%;
        
        mat-form-field {
          width: 100%;
        }
      }
    }
    
    .add-more-button {
      margin-top: 8px;
      
      button {
        color: #3366FF;
        
        mat-icon {
          font-size: 18px;
          height: 18px;
          width: 18px;
        }
      }
    }
  }
  
  .action-buttons {
    margin-top: auto;
    padding-bottom: 24px;
    
    button {
      padding: 10px 16px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
      height: 40px;
      line-height: 20px;
    }
    
    .back-button {
      border: 1px solid #D1D5DB;
      color: #6B7280;
    }
    
    .send-invite-button {
      background-color: #3366FF;
      color: white;
      
      &:disabled {
        background-color: rgba(51, 102, 255, 0.6);
      }
    }
  }
}

// Channel tab styles
.channel-content {
  background-color: #f3f6fa;
  height: 100%;
  
  .channel-sidebar {
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 16px 0;
    
    .channel-options {
      .channel-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        cursor: pointer;
        color: #4b5563;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
        
        &.active {
          background-color: #f0f2f5;
          border-left: 3px solid #3366ff;
          color: #111827;
          font-weight: 500;
        }
        
        .channel-name {
          font-size: 14px;
        }
      }
    }
  }
  
  .channel-content-area {
    background-color: white;
    border-radius: 8px;
    min-height: 400px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 24px;
    
    .channel-view-tabs {
      margin-bottom: 24px;
      
      .mat-button-toggle-group {
        border-radius: 4px;
        border: 1px solid #e5e7eb;
        
        .mat-button-toggle {
          background-color: #ffffff;
          color: #6b7280;
          font-size: 14px;
          
          &.mat-button-toggle-checked {
            background-color: #f0f2f5;
            color: #111827;
          }
        }
      }
    }
    
    .channel-details {
      .config-section {
        margin-bottom: 20px;
        
        .config-label {
          font-size: 14px;
          font-weight: 500;
          color: #6b7280;
          margin-bottom: 8px;
        }
        
        .full-width {
          width: 100%;
        }
        
        .documentation-file {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          
          span {
            font-size: 14px;
            color: #111827;
          }
        }
      }
    }
    
    .channel-empty-state {
      padding: 40px 0;
      
      img {
        width: 100px;
        height: 100px;
        margin-bottom: 16px;
      }
      
      .empty-state-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
      }
      
      .empty-state-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 16px;
        text-align: center;
      }
    }
    
    .channel-not-configured {
      padding: 40px 0;
      
      img {
        width: 100px;
        height: 100px;
        margin-bottom: 16px;
      }
      
      .not-configured-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
        text-align: center;
      }
      
      .not-configured-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 8px;
        text-align: center;
      }
    }
    
    .channel-configuring {
      padding: 40px 0;
      
      img {
        width: 100px;
        height: 100px;
        margin-bottom: 16px;
      }
      
      .configuring-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
        text-align: center;
      }
      
      .configuring-subtitle {
        font-size: 14px;
        color: #6b7280;
        text-align: center;
      }
    }
    
    .channel-access-denied {
      padding: 40px 0;
      
      img {
        width: 100px;
        height: 100px;
        margin-bottom: 16px;
      }
      
      .access-denied-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
        text-align: center;
      }
      
      .access-denied-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 16px;
        text-align: center;
      }
    }
    
    .channel-files {
      .file-list {
        .file-item {
          width: 100%;
          padding: 12px 16px;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          margin-bottom: 12px;
          
          .file-name {
            font-size: 14px;
            color: #111827;
          }
          
          .file-actions {
            button {
              mat-icon {
                font-size: 18px;
                height: 18px;
                width: 18px;
                line-height: 18px;
              }
            }
          }
        }
      }
    }
    
    .channel-files-list {
      padding: 24px;
      
      .section-header {
        margin-bottom: 20px;
        
        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #111827;
          margin: 0;
        }
        
        .add-more-btn {
          font-size: 14px;
          background-color: var(--primary-color, #3f51b5);
          color: white;
          height: 36px;
          
          mat-icon {
            margin-right: 4px;
          }
        }
      }
      
      .files-container {
        .file-item {
          background-color: #f9fafb;
          border-radius: 8px;
          padding: 12px 16px;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          
          .file-name {
            font-size: 14px;
            color: #111827;
            font-weight: 500;
          }
          
          .file-actions {
            display: flex;
            align-items: center;
            
            button {
              margin-left: 4px;
            }
          }
        }
      }
    }
    
    .api-configuration, .database-configuration {
      .config-section {
        margin-bottom: 24px;
      }
    }
    
    .xyz-tooltip {
      .tooltip-content {
        p {
          margin-bottom: 10px;
          font-size: 14px;
        }
      }
    }
  }
}

.content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .checkbox-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;

    .checkbox-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      
      ::ng-deep {
        .mat-checkbox-label {
          font-size: 16px;
          color: #434956;
        }
      }
      
      .download-button {
        width: 36px;
        height: 36px;
        line-height: 36px;
        
        .download-icon {
          color: var(--primary-color, #3f51b5);
          font-size: 24px;
        }
      }
    }
  }

  .actions {
    display: flex;
    justify-content: space-between;
    margin-top: auto;
    margin-bottom: 20px;

    button {
      padding: 10px 24px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 100px;
    }

    .back-button {
      background-color: #ffffff;
      color: #434956;
      border: 1px solid #d1d5db;
      
      &:hover {
        background-color: #f3f4f6;
      }
    }

    .save-button {
      background-color: #3366ff;
      color: #ffffff;
      border: none;
      
      &:hover {
        background-color: #2952cc;
      }
      
      &:disabled {
        background-color: rgba(51, 102, 255, 0.6);
        cursor: not-allowed;
      }
    }
  }
}

// Sample file selection panel styles
.sample-file-selection-panel {
  margin-top: 50px;
  height: 100%;
  
  .panel-header {
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    color: #111827;
    margin: 0;
  }
  
  .sample-files-list {
    margin-bottom: 24px;
    margin-top: 24px;
    
    .sample-file-item {
      background-color: #f9fafb;
      border-radius: 8px;
      padding: 12px 16px;
      margin-bottom: 12px;
      
      mat-checkbox {
        font-size: 14px;
        color: #111827;
        font-weight: 500;
      }
      
      button {
        margin-left: 4px;
      }
    }
  }
  
  .action-buttons {
    margin-top: auto;
    padding-bottom: 24px;
    
    button {
      padding: 10px 16px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
      height: 40px;
      line-height: 20px;
    }
    
    .back-button {
      border: 1px solid #D1D5DB;
      color: #6B7280;
    }
    
    .send-invite-button {
      background-color: #3366FF;
      color: white;
      
      &:disabled {
        background-color: rgba(51, 102, 255, 0.6);
      }
    }
  }
}

// Channel tab navigation styles

.edit-vendor-panel {
  margin-top: 50px;

  .panel-header {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }

  .vendor-form {
    margin-top: 20px;

    p {
      margin-top: 14px;
      font-weight: 600;
      font-size: 14px;
      color: #222329;
    }

    mat-form-field {
      width: 100%;
    }

    .action-buttons {
      button {
        height: 40px;
        border-radius: 4px;
        font-weight: 600;
      }

      .save-btn {
        background-color: #3366ff;
        color: #ffffff;
        
        &:hover {
          background-color: #2952cc;
        }
        
        &:disabled {
          background-color: rgba(51, 102, 255, 0.6);
          cursor: not-allowed;
        }
      }
    }
  }
}
