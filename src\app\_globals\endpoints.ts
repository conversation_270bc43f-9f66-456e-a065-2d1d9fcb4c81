const ENDPOINTS: any = {
  user: {
    me: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/me',
    },
    permissions: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/permissions/',
    },
  },
  home: {
    filter: '/api/subscriptions/rs2a/',
    regions: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/regions'
    },
    export_table: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/home/<USER>/export/'
    },
    supplierStats: '/api/subscriptions/rs2a/',
    count: '/api/subscriptions/rs2a/',
    batchList: '/api/subscriptions/rs2a/',
    tagList: '/api/subscriptions/rs2a/',
    createTag: '/api/subscriptions/rs2a/',
    addRemovetags: '/api/subscriptions/rs2a/',
    action: '/api/subscriptions/rs2a/',
    skuList: '/api/subscriptions/rs2a/',
    reject: '/api/subscriptions/rs2a/',
    channelList : '/api/subscriptions/rs2a/',
    supplierList: '/api/subscriptions/rs2a/',
    skuFilter : {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/batch_detail/',
      pathC: '/filters/'
    } ,
    moduleDetail : {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/batch_transfer/tags_templates/'
    },
    batchTransfer : {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/batch_transfer/',
      pathC: '/'
    },
    moveBatch: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/home/<USER>/update_status/'
    },
    deleteTag: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tags/',
      pathC: '/'
    }
  },
  file_upload : {
    validate_batch_upload: '/api/subscriptions/rs2a/',
    uuid: '/api/subscriptions/rs2a/',
    get_signed_url : '/api/subscriptions/rs2a/',
    upload_complete : '/api/subscriptions/rs2a/',
    },

  supplier: {
    supplierInfo: '/api/subscriptions/rs2a/',
    exportUsers: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/suppliers/users_export/'
    },
    addVendor: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/supplier/'
    }
  },
  dataQuality: {
    filter: '/api/subscriptions/rs2a/',
    data_quality_stats: '/api/subscriptions/rs2a/',
  },
  comments: {
    commentsList: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/comments',
    },
    postComment: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/comments/',
    },
    userNamesToTag: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/usernames',
    },
    resolve: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/comments/',
    },
    edit: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/comments/',
      pathC: '',
    },
    delete: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/comments/',
      pathC: '',
    },
  },
  help: {
    contactSales: '/api/subscriptions/rs2a/',
    contactSupport: '/api/subscriptions/rs2a/',
  },
  settings : {
    emialNotifications : '/api/subscriptions/rs2a/'
  },
  tickets: {
    // Issues
    createIssue: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues'
    },
    updateIssue: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '' // issue_iid will be appended
    },
    getIssueDetails: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '' // issue_iid will be appended
    },
    listIssuesByGroup: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/groups/',
      pathC: '/issues'
    },
    listIssuesByProject: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/list'
    },
    listProjectsByGroup: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/groups/',
      pathC: '/projects'
    },
    listGroups: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/groups'
    },
    // Top-level Comments
    createComment: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/notes'
    },
    editComment: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/notes/' // note_id will be appended
    },
    deleteComment: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/notes/' // note_id will be appended
    },
    // Discussions
    listDiscussionsForIssue: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/discussions'
    },
    createDiscussion: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/discussions/thread'
    },
    // Discussion Comments (Replies)
    addCommentToDiscussion: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/discussions/', // discussion_id will be appended
      pathE: '/notes'
    },
    updateCommentInDiscussion: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/discussions/', // discussion_id will be appended
      pathE: '/notes/' // note_id will be appended
    },
    deleteCommentInDiscussion: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/issues/',
      pathD: '/discussions/', // discussion_id will be appended
      pathE: '/notes/' // note_id will be appended
    },
    projectLabels: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/labels'
    },
    groupLabels: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/groups/',
      pathC: '/labels'
    },
    groupMembers: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/groups/',
      pathC: '/members/all'
    },
    projectMembers: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/projects/',
      pathC: '/members/all'
    },
    // Ticket Attachments
    attachmentUploadSignedUrl: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/upload/signed_url'
    },
    attachmentDownloadSignedUrl: {
      pathA: '/api/subscriptions/rs2a/',
      pathB: '/tickets/download/signed_url'
    },
  },
};
export { ENDPOINTS };
