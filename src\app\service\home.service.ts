import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, map, catchError, throwError } from 'rxjs';
import { Globals } from '../_globals/endpoints.global';

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get SupplierList filters
   * @param subscription_id
   * @returns
   */
  getSupplierList = (subscription_id): Observable<any> => {
    const FilterListEndpoint = this.globals.urlJoin('home', 'filter');
    return this.http
      .get(FilterListEndpoint + subscription_id + '/suppliers/')
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  // status filter
  getStatusList(subscription_id): Observable<any> {
    const endpoint = this.globals.urlJoin('home', 'filter');
    return this.http.get(endpoint + subscription_id + '/home/<USER>/');
  }

  /**
   * get sku filters
   * @param subscription_id
   * @returns
   */
  getSkuFilterList = (subscription_id, batch_id): Observable<any> => {
    const FilterListEndpoint = this.globals.urlJoinWithTwoParam(
      'home',
      'skuFilter',
      subscription_id,
      batch_id
    );
    return this.http.get(FilterListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get region filter options
   */
  getRegions = (subscription_id): Observable<any> => {
    const RegionListEndpoint = this.globals.urlJoinWithParam(
      'home',
      'regions',
      subscription_id,
    );
    return this.http.get(RegionListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param start_date
   * @param end_date
   * @param supplier_filter
   * @param subscription_id
   * @returns
   */
  getSupplierStats = (
    page,
    size,
    search,
    start_date,
    end_date,
    supplier_filter,
    subscription_id
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    if (supplier_filter !== undefined && supplier_filter.length > 0) {
      supplier_filter.forEach((item) => {
        options.params = options.params.append('supplier_filter', item);
      });
    }
    const SupplierListEndpoint = this.globals.urlJoin('home', 'supplierStats');
    return this.http
      .get(SupplierListEndpoint + subscription_id + '/supplier_stats/', options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Get status count
   * @param subscription_id
   * @param supplier_code
   * @returns
   */
  getStatusCount = (
    subscription_id,
    supplier_code,
    q,
    start_date,
    end_date,
    supplier_name
  ): Observable<any> => {
    const countEndpoint = this.globals.urlJoin('home', 'count');
    const options = {
      params: new HttpParams()
        .set('q', q)
        .set('start_date', start_date)
        .set('end_date', end_date)
        .set('supplier_name', supplier_name)
    };
    return this.http
      .get(
        countEndpoint + subscription_id + '/batches/status/' + supplier_code,
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  getHomeCardsCount = (
    subscription_id,
    supplier_code,
    q,
    start_date,
    end_date,
    status_filter,
    regions
  ): Observable<any> => {
    const countEndpoint = this.globals.urlJoin('home', 'count');
    return this.http
      .get(countEndpoint + subscription_id + '/home/<USER>/sku_stats/', {
        params: {
          q: q,
          start_date: start_date,
          end_date: end_date,
          supplier_filter: supplier_code,
          status: status_filter,
          region: regions
        },
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Batct tabel
   * @param page
   * @param size
   * @param status
   * @param search
   * @param start_date
   * @param end_date
   * @param batch_filter
   * @param subscription_id
   * @param supplier_code
   * @returns
   */
  getBatchList = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    batch_filter,
    subscription_id,
    supplier_code,
    supplier_name
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('status', status)
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date)
        .set('supplier_name', supplier_name)
    };
    // if (batch_filter !== undefined && batch_filter.length > 0) {
    //   batch_filter.forEach((item) => {
    //     options.params = options.params.append('batch_filter', item);
    //   });
    // }
    const BatchListEndpoint = this.globals.urlJoin('home', 'batchList');
    return this.http
      .get(
        BatchListEndpoint + subscription_id + '/batches/' + supplier_code + '/',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  getHomeBatchList = (
    page,
    size,
    status,
    search,
    start_date,
    end_date,
    batch_filter,
    subscription_id,
    supplier_code,
    regions
  ): Observable<any> => {
    // if (batch_filter !== undefined && batch_filter.length > 0) {
    //   batch_filter.forEach((item) => {
    //     options.params = options.params.append('batch_filter', item);
    //   });
    // }
    const BatchListEndpoint = this.globals.urlJoin('home', 'batchList');
    return this.http
      .get(BatchListEndpoint + subscription_id + '/home/<USER>/', {
        params: {
          page: page,
          page_size: size,
          status: status,
          q: search,
          start_date: start_date,
          end_date: end_date,
          supplier_filter: supplier_code,
          region: regions
        },
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  exportTable = (subscription_id, search, status, start_date, end_date, supplier_code, regions) => {
    const exportTableEndpoint = this.globals.urlJoinWithParam('home', 'export_table', subscription_id);
    return this.http
      .post(exportTableEndpoint, {
        search: search,
        status: status,
        start_date: start_date,
        end_date: end_date,
        supplier_filter: supplier_code,
        region: regions
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  }

  getSkuList = (
    page,
    size,
    search,
    product_status,
    brands,
    sku_status,
    assets,
    price,
    global_attribute,
    subscription_id,
    batch_id
  ): Observable<any> => {
    // const options = {
    //   params: new HttpParams()
    //     .set('page', page)
    //     .set('page_size', size)
    //     .set('q', search),
    // };
    // if (sku !== undefined && sku.length > 0) {
    //   sku.forEach((item) => {
    //     options.params = options.params.append('sku_type', item);
    //   });
    // }
    const SkuListEndpoint = this.globals.urlJoin('home', 'skuList');
    return this.http
      .get(SkuListEndpoint + subscription_id + '/batch_detail/' + batch_id, {
        params: {
          page: page,
          page_size: size,
          q: search,
          product_status: product_status,
          brand: brands,
          sku_status: sku_status,
          assets: assets,
          price: price,
          global_attribute: global_attribute,
        },
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get tag list
   * @param subscription_id
   * @returns
   */
  getTagList = (subscription_id): Observable<any> => {
    const countEndpoint = this.globals.urlJoin('home', 'tagList');
    return this.http.get(countEndpoint + subscription_id + '/tags/').pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  getChannelList = (subscription_id): Observable<any> => {
    const ChannelEndpoint = this.globals.urlJoin('home', 'channelList');
    return this.http.get(ChannelEndpoint + subscription_id + '/channels/').pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  getInputOutput = (
    subscription_id,
    supplier_code,
    supplier_name,
    channel
  ): Observable<any> => {
    const options = {
      params: new HttpParams().set('channel', channel).set('supplier_name', supplier_name),
    };
    const IOEndpoint = this.globals.urlJoin('home', 'supplierList');
    return this.http
      .get(
        IOEndpoint +
          subscription_id +
          '/input_output_formats/' +
          supplier_code +
          '/',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  //batch transfer I/O formats
  getModuleDetails = (
    subscription_id,
    module,
    product_status,
    brands,
    sku_status,
    asset,
    price,
    global_attribute
  ): Observable<any> => {
    const endpoint = this.globals.urlJoinWithParam(
      'home',
      'moduleDetail',
      subscription_id
    );
    // return this.http.get('https://api.npoint.io/433412cf5e3292fc9139');
    return this.http.get(endpoint, {
      params: {
        module: module,
        product_status: product_status,
        brand: brands,
        sku_status: sku_status,
        assets: asset,
        price: price,
        global_attribute: global_attribute,
      },
    });
  };

  transferBatch(payload, subscription_id, batch_id) {
    const endpoint = this.globals.urlJoinWithTwoParam(
      'home',
      'batchTransfer',
      subscription_id,
      batch_id
    );
    return this.http.post(endpoint, payload);
  }

  moveBatch(
    payload,
    subscription_id,
    status,
    search,
    start_date,
    end_date,
    supplier_code
  ) {
    const endpoint = this.globals.urlJoinWithParam(
      'home',
      'moveBatch',
      subscription_id
    );
    return this.http.patch(endpoint, payload, {
      params: {
        status: status,
        q: search,
        start_date: start_date,
        end_date: end_date,
        supplier_filter: supplier_code,
      },
    });
  }
  /**
   * Create label
   * @param subscription_id
   * @param tag
   * @param description
   * @param tag_colour
   * @param text_colour
   * @returns
   */
  createLabel = (
    subscription_id,
    tag,
    description,
    tag_colour,
    text_colour
  ): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin('home', 'createTag');
    return this.http
      .post(createLabelEndpoint + subscription_id + '/tags/', {
        tag: tag,
        description: description,
        tag_colour: tag_colour,
        text_colour: text_colour,
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Add label
   * @param subscription_id
   * @param batch_id
   * @param addlabel_id
   * @param removelabel_id
   * @returns
   */
  addLabel = (
    subscription_id,
    batch_id,
    addlabel_id,
    removelabel_id
  ): Observable<any> => {
    const LabelEndpoint = this.globals.urlJoin('home', 'addRemovetags');
    if (!addlabel_id) {
      return this.http
        .patch(
          LabelEndpoint +
            subscription_id +
            '/batches/' +
            batch_id +
            '/add_remove_tags/',
          {
            remove_tag: removelabel_id,
          }
        )
        .pipe(
          map((response: any) => {
            return response;
          }),
          catchError((error) => throwError(error))
        );
    } else {
      return this.http
        .patch(
          LabelEndpoint +
            subscription_id +
            '/batches/' +
            batch_id +
            '/add_remove_tags/',
          {
            add_tag: addlabel_id,
          }
        )
        .pipe(
          map((response: any) => {
            return response;
          }),
          catchError((error) => throwError(error))
        );
    }
  };

  /**
   * batch action api
   * @param subscription_id
   * @param batch_id
   * @param addlabel_id
   * @param removelabel_id
   * @returns
   */
  action = (subscription_id, batch_id, action): Observable<any> => {
    const LabelEndpoint = this.globals.urlJoin('home', 'action');
    return this.http
      .patch(
        LabelEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/update_status/',
        {
          action: action,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  rejectBatch = (subscription_id, batch_id, comment): Observable<any> => {
    const RejectEndpoint = this.globals.urlJoin('home', 'reject');
    return this.http
      .patch(
        RejectEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/reject_batch/',
        {
          comment: comment,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * delete tag
   * @param subscription_id 
   * @param tagId 
   * @returns 
   */
  
  deleteTag = (subscription_id, tagId) => {
    const deleteTagEndpoint = this.globals.urlJoinWithTwoParam('home', 'deleteTag', subscription_id, tagId)
    return this.http.delete(deleteTagEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  }
}
