import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { BatchesComponent } from './components/home/<USER>/batches.component';
import { SupplierComponent } from './components/supplier/supplier.component';
import { DataQualityComponent } from './components/data-quality/data-quality.component';
import { SettingsComponent } from './components/settings/settings.component';
import { HelpComponent } from './components/help/help.component';
import { BatchActivitesComponent } from './components/home/<USER>/batch-activites.component';
import { LoadingComponent } from './components/loading/loading.component';
import { AuthGuard } from '@auth0/auth0-angular';
import { CommentsComponent } from './components/comments/comments.component';
import { AppAuthGuard } from './_guards/auth.guard';
import { OfflineComponent } from './components/offline/offline.component';
import { SupplierBatchesComponent } from './components/supplier/supplier-batches/supplier-batches.component';
import { SupplierConfigurationComponent } from './components/supplier/supplier-configuration/supplier-configuration.component';
import { TicketViewComponent } from './components/ticket-view/ticket-view.component';

const routes: Routes = [
  {
    path: 'home',
    component: BatchesComponent,
    data: { title: 'Home' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'supplier',
    component: SupplierComponent,
    data: { title: 'Supplier' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'supplier/supplier-configuration',
    component: SupplierConfigurationComponent,
    data: { title: 'Supplier Configuration' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  // {
  //   path: 'dataquality',
  //   component: DataQualityComponent,
  //   data: { title: 'Data Quality' },
  //   canActivate: [AuthGuard, AppAuthGuard],
  // },
  {
    path: 'settings',
    component: SettingsComponent,
    data: { title: 'Settings' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'help',
    component: HelpComponent,
    data: { title: 'Help' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'Help-Center',
    component: TicketViewComponent,
    data: { title: 'Help-Center' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'loading',
    component: LoadingComponent,
    data: { title: 'Loading' },
    canActivate: [AuthGuard],
  },
  {
    path: 'home/batches',
    component: SupplierBatchesComponent,
    data: { title: 'Batches' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'home/batches/batch-activities',
    component: BatchActivitesComponent,
    data: { title: 'Batch-Activities' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'comments',
    component: CommentsComponent,
    data: { title: 'Comments' },
    canActivate: [AuthGuard, AppAuthGuard],
  },
  {
    path: 'offline',
    component: OfflineComponent,
    data: { title: `DataX` },
  },
  {
    path: '**',
    component: HomeComponent,
    canActivate: [AuthGuard, AppAuthGuard],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes,{ scrollPositionRestoration:'top' })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
