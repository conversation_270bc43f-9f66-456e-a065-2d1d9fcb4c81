import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { SidePanelService } from '../../../service/side-panel.service';
import { FileUploadService } from '../../../service/file-upload.service';
import { Subscription } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-file-details',
  templateUrl: './file-details.component.html',
  styleUrls: ['./file-details.component.scss']
})
export class FileDetailsComponent implements OnInit, OnDestroy {
  fileDetails: any = null;
  private subscription: Subscription = new Subscription();
  
  // Track selected channels
  selectedChannels: { [key: string]: boolean } = {
    'Upload': true,
    'Email': true,
    'FTP': true,
    'Push API': false,
    'Pull API': false,
    'Database': false,
    'Email (Alternative)': false
  };

  constructor(
    private sidePanelService: SidePanelService,
    private fileUploadService: FileUploadService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.subscription.add(
      this.fileUploadService.getSelectedFileDetails().subscribe(details => {
        this.fileDetails = details;
        
        // If the file has inputChannel data, update our selectedChannels
        if (details && details.inputChannel && Array.isArray(details.inputChannel)) {
          // Reset all channels to false first
          Object.keys(this.selectedChannels).forEach(channel => {
            this.selectedChannels[channel] = false;
          });
          
          // Set the channels from the file details to true
          details.inputChannel.forEach(channel => {
            if (this.selectedChannels.hasOwnProperty(channel)) {
              this.selectedChannels[channel] = true;
            }
          });
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  closePanel(): void {
    this.sidePanelService.setShowNav(false);
  }
  
  updateChannel(channel: string, checked: boolean): void {
    this.selectedChannels[channel] = checked;
  }
  
  saveChannels(): void {
    // Get the list of selected channels
    const selectedChannelsList = Object.keys(this.selectedChannels)
      .filter(channel => this.selectedChannels[channel]);
    
    // Update the file details
    if (this.fileDetails) {
      this.fileDetails.inputChannel = selectedChannelsList;
      
      // Update the file details in the service
      this.fileUploadService.setSelectedFileDetails(this.fileDetails);
      
      // Show success message
      this.snackBar.open('Channel preferences saved successfully', 'Close', {
        duration: 3000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });
      
      // Close the panel
      this.closePanel();
    }
  }
} 