import { Component, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import moment from 'moment';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { SupplierService, AddVendorPayload } from '../../service/supplier.service';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Params } from '@angular/router';
import { SnackbarService } from '../../service/snackbar.service';
import { SidePanelService } from '../../service/side-panel.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../_dialog/confirm-dialog/confirm-dialog.component';
import { Router } from '@angular/router';

interface EmailField {
  id: number;
  email: string;
  isValid: boolean;
  errorMessage?: string;
  touched: boolean;
}

export interface SupplierInfo {
  SupplierNameCode: string;
  Modules_Subscribed: any;
  Input: any;
  Status: string;
  No_of_Users: string;
  // Start_Date: string;
  // End_Date: string;
  Comments: string;
  Action: string;
}

@Component({
  selector: 'app-supplier',
  templateUrl: './supplier.component.html',
  styleUrls: ['./supplier.component.scss'],
})
export class SupplierComponent implements OnInit {
  displayedColumns: string[] = [
    'SupplierNameCode',
    'Modules_Subscribed',
    'Input',
    'Status',
    'No_of_Users',
    'Last Login',
    // 'Start_Date',
    // 'End_Date',
    'Comments',
    'Action',
  ];
  // dataSource = new MatTableDataSource<PeriodicElement>(ELEMENT_DATA);

  page: number;
  size: number;
  search: any;
  SubscriptionID;
  supplierInfo;
  totalItems;
  total_pages;
  supplierDataSource;
  tableLoading: boolean = false;
  dataLoading: boolean = false;
  userEmails: EmailField[] = [{ id: 0, email: '', isValid: true, touched: false }];
  lastId: number = 0;
  readonly MAX_EMAILS = 5;
  readonly emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  domain: string = '';
  isDomainValid: boolean = true;
  domainErrorMessage: string = '';
  isDomainTouched: boolean = false;

  vendorForm: FormGroup;

  constructor(
    private supplierService: SupplierService,
    public matSnackbar: MatSnackBar,
    private activatedRoute: ActivatedRoute,
    private snackbarService: SnackbarService,
    private sidepanel: SidePanelService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.initForm();
  }
  @ViewChild(MatPaginator) paginator: MatPaginator;

  ngOnInit() {
    this.dataLoading = true;
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });

    this.search = '';
    this.page = 1;
    this.size = 50;

    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
  }

  private initForm() {
    this.vendorForm = this.fb.group({
      registeredName: ['', Validators.required],
      displayName: ['', Validators.required],
      code: ['', Validators.required],
      nidPub: ['', Validators.required],
      additionalCodes: [''],
      domain: ['', [this.httpsValidator()]],
      invited_email: this.fb.array([this.createEmailFormGroup()])
    });
  }

  private createEmailFormGroup() {
    return this.fb.group({
      email: ['', [this.emailValidator()]]
    });
  }

  get emailsFormArray() {
    return this.vendorForm.get('invited_email') as FormArray;
  }

  /**
   * Search based on keyword
   * @param value
   */

  getSearchValue = (event) => {
    this.search = event.trim();
    this.page = 1;
    this.size = 50;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
    this.paginator.firstPage();
  };

  /**
   * reset btn
   */
  reset = () => {
    this.search = '';
    this.page = 1;
    this.size = 50;

    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
  };

  addVendor() {
    this.resetVendorAdditionForm();
    this.sidepanel.setShowNav(true, 'addVendorPanel');
  }

  closeSidepanel() {
    this.sidepanel.setShowNav(false);
    this.resetVendorAdditionForm();
  }

  resetSearch = () => {
    this.search = '';
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
  };

  /**
   * Get module table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginationEvent = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
  };

  /**
   * Jump pages
   * @param event
   */
  goToPage = (event) => {
    this.page = event;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.SubscriptionID
    );
  };

  /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param label
   * @param subscription_id
   */
  getSupplierTable = (page, size, search, subscription_id) => {
    this.supplierService
      .getSupplierInfo(page, size, search, subscription_id)
      .subscribe({
        next: (resp) => {
          this.supplierInfo = resp.result.result;
          this.totalItems = resp.result.total_items;
          this.size = resp.result.page_size;
          this.page = resp.result.page;
          this.total_pages = resp.result.total_pages;
          const HOME_DATA: SupplierInfo[] = this.supplierInfo;

          this.supplierDataSource = new MatTableDataSource<SupplierInfo>(
            HOME_DATA
          );
          this.tableLoading = false;
          this.dataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
        },
      });
  };

  /**
   * export users
   */
  exportUsers = () => {
    this.supplierService.exportUsers(this.SubscriptionID).subscribe({
      next: (resp) => {
        this.snackbarService.openSnackBar(resp.detail, 'OK');
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.statusText}`, 'OK');
      },
    })
  }

  addEmailField() {
    if (this.emailsFormArray.length < this.MAX_EMAILS) {
      this.emailsFormArray.push(this.createEmailFormGroup());
    } else {
      this.snackbarService.openSnackBar(`Maximum ${this.MAX_EMAILS} email fields allowed`, 'error');
    }
  }

  removeEmailField(index: number) {
    if (this.emailsFormArray.length > 1) {
      this.emailsFormArray.removeAt(index);
    }
  }

  private emailValidator() {
    return (control: any) => {
      const email = control.value;
      if (!email) {
        return null; // Optional field
      }
      
      if (this.emailRegex.test(email)) {
        // Check for duplicates
        const emails = this.emailsFormArray.controls
          .map(group => group.get('email')?.value)
          .filter(val => val);
        
        const isDuplicate = emails.filter(e => e === email).length > 1;
        if (isDuplicate) {
          return { duplicate: true };
        }
        return null;
      }
      return { invalidFormat: true };
    };
  }

  private httpsValidator() {
    return (control: any) => {
      const domain = control.value;
      if (!domain) {
        return null; // Optional field
      }

      const trimmedDomain = domain.trim().toLowerCase();
      if (!trimmedDomain.startsWith('https://')) {
        return { httpsRequired: true };
      }

      if (trimmedDomain === 'https://') {
        return { invalidDomain: true };
      }

      return null;
    };
  }

  getEmailErrorMessage(index: number): string {
    const emailControl = this.emailsFormArray.at(index).get('email');
    if (emailControl?.errors?.['invalidFormat']) {
      return 'Please enter a valid email address';
    }
    if (emailControl?.errors?.['duplicate']) {
      return 'This email is already added';
    }
    return '';
  }

  getDomainErrorMessage(): string {
    const domainControl = this.vendorForm.get('domain');
    if (domainControl?.errors?.['httpsRequired']) {
      return 'Domain must start with https://';
    }
    if (domainControl?.errors?.['invalidDomain']) {
      return 'Please enter a valid domain';
    }
    return '';
  }

  /**
   * Submit the vendor form
   */
  onSubmit() {
    if (this.vendorForm.valid) {
      const formData = this.vendorForm.value;
      const vendorName = formData.displayName || formData.registeredName;
      
      const dialogRef = this.dialog.open(ConfirmDialogComponent, {
        width: '520px',
        data: {
          title: 'Are you sure?',
          message: `Are you sure you want to add the vendor "${vendorName}"?`
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Get valid emails from the form array
          const validEmails = this.emailsFormArray.controls
            .map(group => group.get('email')?.value)
            .filter(email => email && email.trim().length > 0);

          // Prepare the payload
          const payload: AddVendorPayload = {
            registered_name: formData.registeredName,
            display_name: formData.displayName,
            code: Number(formData.code),
            nid_pub: [Number(formData.nidPub)],
            domain: formData.domain,
            additional_codes: formData.additionalCodes ? [Number(formData.additionalCodes)] : [],
            invited_email: validEmails
          };

          // Show loading state
          this.tableLoading = true;

          // Make the API call
          this.supplierService.addVendor(this.SubscriptionID, payload).subscribe({
            next: (response) => {
              this.snackbarService.openSnackBar('Vendor added successfully', 'success');
              this.resetVendorAdditionForm();
              this.sidepanel.setShowNav(false);
              // Refresh the table
              this.getSupplierTable(
                this.page,
                this.size,
                this.search,
                this.SubscriptionID
              );
              // Navigate to supplier configuration
              this.router.navigate(['/supplier/supplier-configuration'], {
                queryParams: {
                  sub: this.SubscriptionID,
                  vendor: vendorName,
                  code: formData.code
                }
              });
            },
            error: (error: HttpErrorResponse) => {
              this.tableLoading = false;
              this.snackbarService.openSnackBar(error.error?.detail || 'Failed to add vendor', 'error');
            }
          });
        }
      });
    } else {
      this.markFormGroupTouched(this.vendorForm);
    }
  }

  private resetVendorAdditionForm() {
    this.initForm();
    this.vendorForm.markAsUntouched();
    this.vendorForm.markAsPristine();
    
    // Reset each control individually
    Object.keys(this.vendorForm.controls).forEach(key => {
      const control = this.vendorForm.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
      
      // Handle the invited_email FormArray specially
      if (key === 'invited_email' && control instanceof FormArray) {
        control.controls.forEach(emailGroup => {
          emailGroup.markAsUntouched();
          emailGroup.markAsPristine();
          emailGroup.get('email')?.markAsUntouched();
          emailGroup.get('email')?.markAsPristine();
        });
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
