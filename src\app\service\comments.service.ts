import { Injectable } from '@angular/core';
import { throwError, map, catchError } from 'rxjs';
import { HttpParams, HttpHeaders, HttpClient } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { ENDPOINTS } from '../_globals/endpoints';

@Injectable({
  providedIn: 'root',
})
export class CommentsService {
  private endpoints: any = ENDPOINTS;
  private httpOptions: HttpHeaders;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get comments list
   * @param subs_id
   * @param category
   * @param id
   * @returns
   */
  getCommentsList = (
    subs_id,
    page,
    size,
    category,
    category_id,
    q,
    comment_thread,
    batch_id
  ) => {
    const commentsListEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'commentsList',
      subs_id
    );
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('category', category)
        .set('q', q)
        .set('comment_thread', comment_thread)
  
    };
    if (category_id) {
      options.params = options.params.append('category_id', category_id);
    }
    if (category_id) {
      options.params = options.params.append('batch_id', batch_id);
    }
    
    return this.http.get(commentsListEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * post comment
   * @param subs_id
   * @param obj
   * @returns
   */
  postComment = (subs_id, type, id, obj, batch_id) => {
    const options = {
      params: new HttpParams()
      .set('category_id', id)
      .set('category', type)
      .set('batch_id', batch_id),
    };

    const postCommentEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'postComment',
      subs_id
    );
    return this.http.post(postCommentEndpoint, obj, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get user names to tag
   * @param subs_id
   * @param q
   * @returns
   */
  getUserNamesToTag = (subs_id) => {
    const commentsListEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'userNamesToTag',
      subs_id
    );
    // const options = {
    //   params: new HttpParams().set('q', q),
    // };
    return this.http.get(commentsListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * Resolve comment
   * @param subs_id
   * @param type
   * @param id
   * @returns
   */
  resolveComment = (subs_id, type, id,batch_id) => {
    const options = {
      params: new HttpParams()
      .set('category_id', id)
      .set('category', type)
      .set('batch_id', batch_id)
    };
    const postCommentEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'resolve',
      subs_id
    );
    return this.http
      .patch(postCommentEndpoint, { resolve: true }, options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * edit comment
   * @param subs_id
   * @param comment_id
   * @param obj
   */
  updateComment = (subs_id, comment_id, obj) => {
    const editCommentEndpoint = this.globals.urlJoinWithTwoParam(
      'comments',
      'edit',
      subs_id,
      comment_id
    );
    return this.http.patch(editCommentEndpoint, obj).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * delete comment
   * @param subs_id
   * @param comment_id
   * @param obj
   * @returns
   */
  deleteComment = (subs_id, comment_id) => {
    const deleteCommentEndpoint = this.globals.urlJoinWithTwoParam(
      'comments',
      'delete',
      subs_id,
      comment_id
    );
    return this.http.delete(deleteCommentEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
}
