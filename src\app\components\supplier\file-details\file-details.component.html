<div class="file-details-container">
  <!-- <div class="header">
    <h2>File Details</h2> 
    <button class="close-button" (click)="closePanel()">
      <span class="material-icons">clear</span>
    </button>
  </div> -->
  <!--  -->
  <div class="header" fxLayout="row" fxLayoutAlign="space-between center">
    <p class="panel-header">File Details</p>
    <img
      style="cursor: pointer"
      class="batch-stop-icon"
      src="assets/images/home-icons/close.svg"
      (click)="closePanel()"
    />
  </div>
  <!--  -->

  <div class="content" *ngIf="fileDetails">
    <div class="checkbox-section">
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Upload']"
          (change)="updateChannel('Upload', $event.checked)"
          color="primary"
        >
          Upload
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Email']"
          (change)="updateChannel('Email', $event.checked)"
          color="primary"
        >
          Email
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['FTP']"
          (change)="updateChannel('FTP', $event.checked)"
          color="primary"
        >
          FTP
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Push API']"
          (change)="updateChannel('Push API', $event.checked)"
          color="primary"
        >
          Push API
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Pull API']"
          (change)="updateChannel('Pull API', $event.checked)"
          color="primary"
        >
          Pull API
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Database']"
          (change)="updateChannel('Database', $event.checked)"
          color="primary"
        >
          Database
        </mat-checkbox>
      </div>
      <div class="checkbox-item">
        <mat-checkbox
          [checked]="selectedChannels['Email (Alternative)']"
          (change)="updateChannel('Email (Alternative)', $event.checked)"
          color="primary"
        >
          Email
        </mat-checkbox>
      </div>
    </div>

    <div
      class="actions"
      fxLayout="row"
      fxLayoutAlign="space-between center"
      fxLayoutGap="10px"
    >
      <button class="back-button" fxFlex="1 1 auto" (click)="closePanel()">
        Back
      </button>
      <button class="save-button" fxFlex="1 1 auto" (click)="saveChannels()">
        Save
      </button>
    </div>
  </div>
</div>
