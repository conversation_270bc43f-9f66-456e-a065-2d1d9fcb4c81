import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, map, catchError, throwError, BehaviorSubject } from 'rxjs';
import { Auth0Service } from './auth0.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  appClass;
  permissionObject;
  user;
  private httpOptions: HttpHeaders;
  appPermissionsAvailable = new BehaviorSubject<boolean>(false);
  isAppPermissionsAvailable = this.appPermissionsAvailable.asObservable();
  // private endpoints: any = ENDPOINTS;
  constructor(
    private globals: Globals,
    private http: HttpClient,
    public auth0: Auth0Service,
    public snackBar: MatSnackBar
  ) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  // ME endpoint call
  me = (subs_id): Observable<any> => {
    const meEndPoint = this.globals.urlJoinWithParam('user', 'me', subs_id);
    return this.http.get(meEndPoint).pipe(
      map((response) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get app permissions
   * @param subs_id
   * @returns
   */
  getAppPermissions = (subs_id) => {
    const getPermissionEndpoint = this.globals.urlJoinWithParam(
      'user',
      'permissions',
      subs_id
    );
    return this.http
      .get(getPermissionEndpoint, {
        headers: this.httpOptions,
      })
      .pipe(
        map((response: any) => {
          this.permissionObject = response.permissions;
          this.checkAppPermissions(true);
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  get appPermissions(): any {
    return this.permissionObject;
  }

  set appPermissions(val) {
    this.permissionObject = val;
  }

  /**
   * set app theme
   * @param subs_id
   */
  setTheme = (subs_id) => {
    this.me(subs_id).subscribe({
      next: (resp) => {
        localStorage.setItem('user', JSON.stringify(resp.result));
        let app = resp.result.theme_class;
        if (app == 'default') {
          this.appClass = '';
          document.body.className = '';
        } else {
          this.appClass = app + '-app-theme';
          document.body.classList.add(this.appClass);
        }
      },
      error: (error) => {
        this.user = JSON.parse(localStorage.getItem('user'));
        this.auth0.logUserOut();
        this.snackBar.open(error, 'OK', {
          duration: 3000,
          panelClass: ['error-snackbar'],
        });
      },
    });
  };
  /**
   * sets the appPermissionsAvailable to true once permissions api has responded
   * @param val
   */
  checkAppPermissions = (val: boolean) => {
    this.appPermissionsAvailable.next(val);
  };
}
