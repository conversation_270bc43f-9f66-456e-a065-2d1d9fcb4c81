import { Component, Inject, OnInit, Optional } from '@angular/core';
import { FormGroup, Validators, FormBuilder, FormControl } from '@angular/forms';
import { HomeService } from '../../../../service/home.service';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BatchesComponent } from '../batches.component';

@Component({
  selector: 'app-reject-dialog',
  templateUrl: './reject-dialog.component.html',
  styleUrls: ['./reject-dialog.component.scss']
})
export class RejectDialogComponent implements OnInit {
  public reasonForm: FormGroup;
  subscriptionId;
  batch_id;
  formValues;
  fromDialog;
  values;
  constructor(
    private fb: FormBuilder,
    private homeService: HomeService,
    public matSnackbar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<BatchesComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public datas: any
    
  ) { }

  ngOnInit(): void {
    // console.log(this.data)
    this.subscriptionId = this.data.sub_id;
    this.batch_id = this.data.batch_id
    this.reasonForm = this.fb.group({
      comment: ['', [Validators.required, this.noWhitespaceValidator]],
    });
  }

   closeDialog() {
    this.values = this.reasonForm.value;
    // console.log(this.values);
    this.dialogRef.close({ event: 'close', data: this.values.comment });
  }

  public noWhitespaceValidator(control: FormControl) {
    if (control.value) {
      const isWhitespace = (control.value || '').trim().length == 0;
      const isValid = !isWhitespace;
      return isValid ? null : { whitespace: true };
    }
    return null;
  }
   
}
