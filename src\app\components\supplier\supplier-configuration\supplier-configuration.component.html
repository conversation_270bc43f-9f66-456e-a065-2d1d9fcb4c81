<div class="wrapper">
  <div
    class="supplier-configuration-container"
    fxLayout="column"
    fxLayoutGap="10px"
    fxFlex="100"
    style="padding: 20px 24px"
  >
    <div
      class="supplier-configuration-header"
      style="border-radius: 9px; padding: 24px; background-color: #fff"
    >
      <div class="supplier-header-content" fxLayout="column" fxLayoutGap="16px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <div
            class="supplier-title-section"
            fxLayout="row"
            fxLayoutAlign="start center"
            fxLayoutGap="12px"
          >
            <h1 class="supplier-name">Leviton</h1>
            <div class="supplier-code">(3414)</div>
            <div class="status-badge">Active</div>
          </div>
          <button
            mat-button
            class="edit-button"
            (click)="openVendorDetailsEditPanel()"
          >
            <mat-icon>edit</mat-icon>
            Edit
          </button>
        </div>

        <div
          class="supplier-details"
          fxLayout="row"
          fxLayoutAlign="start start"
          fxLayoutGap="100px"
        >
          <div class="detail-column">
            <div class="detail-label">Registered name</div>
            <div class="detail-value">Leviton Inc</div>
          </div>
          <div class="detail-column">
            <div class="detail-label">Type</div>
            <div class="detail-value">Supplier</div>
          </div>
          <div class="detail-column">
            <div class="detail-label">Domain</div>
            <div class="detail-value">www.leviton.com</div>
          </div>
          <div class="detail-column">
            <div class="detail-label">Added on</div>
            <div class="detail-value">Dec 28, 2024</div>
          </div>
          <div class="detail-column">
            <div class="detail-label">Last Upload</div>
            <div class="detail-value">AD24AR001 | Jan 01, 2025</div>
          </div>
          <div class="detail-column">
            <div class="detail-label">Batches</div>
            <div class="detail-value">90</div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="supplier-configuration-pane"
      style="border-radius: 9px; padding: 0px 0px; background-color: #f3f6fa"
    >
      <mat-tab-group
        [selectedIndex]="selectedTabIndex"
        (selectedIndexChange)="onTabChange($event)"
      >
        <mat-tab label="Channel">
          <div class="tab-content">
            <!-- Channel content structure -->
            <div
              class="channel-content"
              style="padding: 24px 0px"
              fxLayout="column"
              fxLayoutGap="10px"
            >
              <!-- Header with view tickets and raise request buttons -->
              <!-- <div
                class="channel-content-header"
                fxLayout="row"
                fxLayoutAlign="space-between center"
                fxFlex="100"
              >
                <div
                  class="left-action-container"
                  fxFlex="50"
                  style="width: 100%"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <button
                    class="view-ticket-btn filled-btn-primary"
                    mat-stroked-button
                  >
                    <span>View Tickets</span>
                  </button>
                </div>
                <div
                  class="right-action-container"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <button
                    class="raise-request-btn"
                    fxLayout="row"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                    mat-raised-button
                    color="primary"
                  >
                    <mat-icon class="add-icon">bolt</mat-icon>
                    <span>Raise a Request</span>
                  </button>
                </div>
              </div> -->

              <!-- Main content area with channel options and content -->
              <div fxLayout="row" fxLayoutGap="20px" style="height: 100%">
                <!-- Left sidebar with channel options -->
                <div
                  class="channel-sidebar"
                  fxFlex="20"
                  style="min-width: 200px"
                >
                  <div class="channel-options">
                    <!-- Upload Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'Upload' }"
                      (click)="selectChannel('Upload')"
                    >
                      <div class="channel-name">Upload</div>
                      <mat-slide-toggle
                        [checked]="channelStatus.Upload"
                        (change)="toggleChannelStatus('Upload', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- Email Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'Email' }"
                      (click)="selectChannel('Email')"
                    >
                      <div class="channel-name">Email</div>
                      <mat-slide-toggle
                        [checked]="channelStatus.Email"
                        (change)="toggleChannelStatus('Email', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- FTP Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'FTP' }"
                      (click)="selectChannel('FTP')"
                    >
                      <div class="channel-name">FTP</div>
                      <mat-slide-toggle
                        [checked]="channelStatus.FTP"
                        (change)="toggleChannelStatus('FTP', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- Pull API Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'Pull API' }"
                      (click)="selectChannel('Pull API')"
                    >
                      <div class="channel-name">Pull API</div>
                      <mat-slide-toggle
                        [checked]="channelStatus['Pull API']"
                        (change)="toggleChannelStatus('Pull API', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- Push API Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'Push API' }"
                      (click)="selectChannel('Push API')"
                    >
                      <div class="channel-name">Push API</div>
                      <mat-slide-toggle
                        [checked]="channelStatus['Push API']"
                        (change)="toggleChannelStatus('Push API', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- Database Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'Database' }"
                      (click)="selectChannel('Database')"
                    >
                      <div class="channel-name">Database</div>
                      <mat-slide-toggle
                        [checked]="channelStatus.Database"
                        (change)="toggleChannelStatus('Database', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>

                    <!-- XYZ Channel Option -->
                    <div
                      class="channel-option"
                      [ngClass]="{ active: selectedChannel === 'XYZ' }"
                      (click)="selectChannel('XYZ')"
                    >
                      <div class="channel-name">XYZ</div>
                      <mat-slide-toggle
                        [checked]="channelStatus.XYZ"
                        (change)="toggleChannelStatus('XYZ', $event)"
                        color="primary"
                      ></mat-slide-toggle>
                    </div>
                  </div>
                </div>

                <!-- Right content area -->
                <div class="channel-content-area" fxFlex="80">
                  <!-- Upload Channel Content -->
                  <div
                    *ngIf="selectedChannel === 'Upload' && channelStatus.Upload"
                    class="channel-details upload-channel"
                  >
                    <div
                      fxLayout="row"
                      fxLayoutAlign="end center"
                      fxLayoutGap="10px"
                    >
                      <!-- Add More button when files exist -->
                      <!-- Add 2 more buttons here -->
                      <button
                        class="view-ticket-btn"
                        mat-stroked-button
                        fxLayout="row"
                        fxLayoutAlign="center center"
                      >
                        <span>View Tickets</span>
                      </button>
                      <button
                        mat-raised-button
                        color="primary"
                        class="raise-request-btn"
                        fxLayout="row"
                        fxLayoutAlign="center center"
                      >
                        <mat-icon class="add-icon">bolt</mat-icon>
                        <span>Raise a Request</span>
                      </button>
                      <div
                        *ngIf="hasChannelSampleFiles('Upload')"
                        class="add-more-button"
                        fxLayout="row"
                        fxLayoutAlign="end center"
                        style="margin-top: 16px"
                      >
                        <button
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('Upload')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                      </div>
                    </div>
                    <!-- Upload Empty State -->
                    <div
                      *ngIf="!hasChannelSampleFiles('Upload')"
                      class="channel-empty-state"
                      fxLayout="column"
                      fxLayoutAlign="center center"
                      fxLayoutGap="10px"
                    >
                      <img
                        src="assets/images/supplier-configuration/upload-file.svg"
                        alt="Upload icon"
                      />
                      <div class="empty-state-title">
                        Please Upload Sample Input File
                      </div>
                      <div class="empty-state-subtitle">
                        Please Upload Sample Input Files by clicking the button
                        below.
                      </div>
                      <button
                        mat-raised-button
                        color="primary"
                        class="upload-file-btn"
                        (click)="openSampleFileSelectionPanel('Upload')"
                      >
                        <mat-icon>add</mat-icon>
                        Add Sample Input File
                      </button>
                    </div>

                    <!-- Files list when files exist -->
                    <div
                      *ngIf="hasChannelSampleFiles('Upload')"
                      class="channel-files-list"
                      fxLayout="row wrap"
                      fxLayoutGap="16px"
                    >
                      <div
                        class="files-container"
                        fxFlex
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          *ngFor="let file of channelSampleFiles['Upload']"
                          class="file-item"
                          fxLayout="row"
                          fxLayoutAlign="space-between center"
                          fxLayoutGap="100px"
                        >
                          <span class="file-name">{{ file }}</span>
                          <div class="file-actions">
                            <button
                              mat-icon-button
                              color="primary"
                              (click)="downloadTemplate('Upload')"
                              matTooltip="Download"
                            >
                              <mat-icon>get_app</mat-icon>
                            </button>
                            <button
                              mat-icon-button
                              color="warn"
                              (click)="toggleSampleFileSelection(file, false)"
                              matTooltip="Remove"
                            >
                              <mat-icon>close</mat-icon>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Email Channel Content -->
                  <div
                    *ngIf="selectedChannel === 'Email' && channelStatus.Email"
                    class="channel-details email-channel"
                  >
                    <div
                      class="channel-view-tabs"
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                    >
                      <mat-button-toggle-group
                        [value]="selectedChannelView"
                        (change)="selectedChannelView = $event.value"
                      >
                        <mat-button-toggle value="configuration"
                          >Configuration</mat-button-toggle
                        >
                        <mat-button-toggle value="input-files"
                          >Input Files</mat-button-toggle
                        >
                      </mat-button-toggle-group>
                      <div fxLayout="row" fxLayoutGap="10px">
                        <button
                          class="view-ticket-btn"
                          mat-stroked-button
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <span>View Tickets</span>
                        </button>
                        <button
                          mat-raised-button
                          color="primary"
                          class="raise-request-btn"
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <mat-icon class="add-icon">bolt</mat-icon>
                          <span>Raise a Request</span>
                        </button>
                        <!-- <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openFileUploadPanelForChannel()"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button> -->
                        <!--  -->
                        <button
                          *ngIf="hasChannelSampleFiles('Email')"
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('Email')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                        <!--  -->
                      </div>
                    </div>

                    <!-- Email Configuration -->
                    <div
                      *ngIf="selectedChannelView === 'configuration'"
                      class="email-configuration"
                      fxLayout="column"
                      fxLayoutGap="20px"
                    >
                      <div class="config-section">
                        <div class="config-label">Sender Mail</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput placeholder="Enter sender Mail" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Receiver Mail</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input
                            matInput
                            value="<EMAIL>"
                          />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>
                    </div>

                    <!-- Email Empty State or Files List -->
                    <div *ngIf="selectedChannelView === 'input-files'">
                      <!-- Empty state when no files -->
                      <div
                        *ngIf="!hasChannelSampleFiles('Email')"
                        class="channel-empty-state"
                        fxLayout="column"
                        fxLayoutAlign="center center"
                        fxLayoutGap="10px"
                      >
                        <img
                          src="assets/images/supplier-configuration/upload-file.svg"
                          alt="Upload icon"
                        />
                        <div class="empty-state-title">
                          Please Add Sample Input File
                        </div>
                        <div class="empty-state-subtitle">
                          Please Add Sample Input Files by clicking the button
                          below.
                        </div>
                        <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Email')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button>
                      </div>

                      <!-- Files list when files exist -->
                      <div
                        *ngIf="hasChannelSampleFiles('Email')"
                        class="channel-files-list"
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          class="files-container"
                          fxFlex
                          fxLayout="row wrap"
                          fxLayoutGap="16px"
                        >
                          <div
                            *ngFor="let file of channelSampleFiles['Email']"
                            class="file-item"
                            fxLayout="row"
                            fxLayoutAlign="space-between center"
                            fxLayoutGap="100px"
                          >
                            <span class="file-name">{{ file }}</span>
                            <div class="file-actions">
                              <button
                                mat-icon-button
                                color="primary"
                                (click)="downloadTemplate('Email')"
                                matTooltip="Download"
                              >
                                <mat-icon>get_app</mat-icon>
                              </button>
                              <button
                                mat-icon-button
                                color="warn"
                                (click)="toggleSampleFileSelection(file, false)"
                                matTooltip="Remove"
                              >
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- FTP Channel Content -->
                  <div
                    *ngIf="selectedChannel === 'FTP' && channelStatus.FTP"
                    class="channel-details ftp-channel"
                  >
                    <!-- Channel view tabs -->
                    <div
                      class="channel-view-tabs"
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                    >
                      <mat-button-toggle-group
                        [value]="selectedChannelView"
                        (change)="selectedChannelView = $event.value"
                      >
                        <mat-button-toggle value="configuration"
                          >Configuration</mat-button-toggle
                        >
                        <mat-button-toggle value="input-files"
                          >Input Files</mat-button-toggle
                        >
                      </mat-button-toggle-group>
                      <div fxLayout="row" fxLayoutGap="10px">
                        <button
                          class="view-ticket-btn"
                          mat-stroked-button
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <span>View Tickets</span>
                        </button>
                        <button
                          mat-raised-button
                          color="primary"
                          class="raise-request-btn"
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <mat-icon class="add-icon">bolt</mat-icon>
                          <span>Raise a Request</span>
                        </button>
                        <!-- Add file button -->
                        <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            hasChannelSampleFiles('FTP')
                          "
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('FTP')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                      </div>
                    </div>

                    <!-- FTP Configuration -->
                    <div
                      *ngIf="selectedChannelView === 'configuration'"
                      class="ftp-configuration"
                      fxLayout="column"
                      fxLayoutGap="20px"
                    >
                      <div class="config-section">
                        <div class="config-label">CAX FTP Server</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="ftp://ftp.example.com:21" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Folder Name</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="Amit's FTP-1" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>
                    </div>

                    <!-- FTP Input Files -->
                    <div *ngIf="selectedChannelView === 'input-files'">
                      <!-- Empty state when no files -->
                      <div
                        *ngIf="!hasChannelSampleFiles('FTP')"
                        class="channel-empty-state"
                        fxLayout="column"
                        fxLayoutAlign="center center"
                        fxLayoutGap="10px"
                      >
                        <img
                          src="assets/images/supplier-configuration/upload-file.svg"
                          alt="Upload icon"
                        />
                        <div class="empty-state-title">
                          Please Add Sample Input File
                        </div>
                        <div class="empty-state-subtitle">
                          Please Add Sample Input Files by clicking the button
                          below.
                        </div>
                        <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('FTP')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button>
                      </div>

                      <!-- Files list when files exist -->
                      <div
                        *ngIf="hasChannelSampleFiles('FTP')"
                        class="channel-files-list"
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          class="files-container"
                          fxFlex
                          fxLayout="row wrap"
                          fxLayoutGap="16px"
                        >
                          <div
                            *ngFor="let file of channelSampleFiles['FTP']"
                            class="file-item"
                            fxLayout="row"
                            fxLayoutAlign="space-between center"
                            fxLayoutGap="100px"
                          >
                            <span class="file-name">{{ file }}</span>
                            <div class="file-actions">
                              <button
                                mat-icon-button
                                color="primary"
                                (click)="downloadTemplate('FTP')"
                                matTooltip="Download"
                              >
                                <mat-icon>get_app</mat-icon>
                              </button>
                              <button
                                mat-icon-button
                                color="warn"
                                (click)="toggleSampleFileSelection(file, false)"
                                matTooltip="Remove"
                              >
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- FTP Not Configured State -->
                  <div
                    *ngIf="selectedChannel === 'FTP' && !channelStatus.FTP"
                    class="channel-not-configured"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-not-configured.svg"
                      alt="Upload icon"
                    />
                    <div class="not-configured-title">
                      Input channel File Transfer Protocol (FTP) is not
                      configured
                    </div>
                    <div class="not-configured-subtitle">
                      Please Raise a Request by clicking the button below, we
                      will configure it.
                    </div>
                    <div class="not-configured-subtitle">
                      Please include preferred username and password
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="raise-request-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">bolt</mat-icon>
                      Raise a Request
                    </button>
                  </div>

                  <!-- FTP Configuring State -->
                  <div
                    *ngIf="
                      selectedChannel === 'FTP' &&
                      channelStatus.FTP &&
                      ftpConfiguring
                    "
                    class="channel-configuring"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-configuring.svg"
                      alt="Configuring icon"
                    />
                    <div class="configuring-title">
                      We're currently configuring your FTP input channel.
                    </div>
                    <div class="configuring-subtitle">
                      You will be notified once it's ready to use.
                    </div>
                  </div>

                  <!-- Pull API Channel Content -->
                  <div
                    *ngIf="
                      selectedChannel === 'Pull API' &&
                      !channelStatus['Pull API']
                    "
                    class="channel-not-configured"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-not-configured.svg"
                      alt="Upload icon"
                    />
                    <div class="not-configured-title">
                      Input channel Pull API is not configured
                    </div>
                    <div class="not-configured-subtitle">
                      Please click the button below to Raise a request.
                    </div>
                    <div class="not-configured-subtitle">
                      Please include your API URL, access token or credentials,
                      and documentation
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="raise-request-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">bolt</mat-icon>
                      Raise a Request
                    </button>
                  </div>

                  <!-- Pull API Configuring State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Pull API' &&
                      channelStatus['Pull API'] &&
                      pullApiConfiguring
                    "
                    class="channel-configuring"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-configuring.svg"
                      alt="Configuring icon"
                    />
                    <div class="configuring-title">
                      We're currently configuring your Pull API input channel.
                    </div>
                    <div class="configuring-subtitle">
                      You will be notified once it's ready to use.
                    </div>
                  </div>

                  <!-- Pull API Configured State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Pull API' &&
                      channelStatus['Pull API'] &&
                      !pullApiConfiguring
                    "
                    class="channel-details pull-api-channel"
                  >
                    <div
                      class="channel-view-tabs"
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                    >
                      <mat-button-toggle-group
                        [value]="selectedChannelView"
                        (change)="selectedChannelView = $event.value"
                      >
                        <mat-button-toggle value="configuration"
                          >Configuration</mat-button-toggle
                        >
                        <mat-button-toggle value="input-files"
                          >Input Files</mat-button-toggle
                        >
                      </mat-button-toggle-group>
                      <div fxLayout="row" fxLayoutGap="10px">
                        <button
                          class="view-ticket-btn"
                          mat-stroked-button
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <span>View Tickets</span>
                        </button>
                        <button
                          mat-raised-button
                          color="primary"
                          class="raise-request-btn"
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <mat-icon class="add-icon">bolt</mat-icon>
                          <span>Raise a Request</span>
                        </button>
                        <!-- <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            !hasChannelSampleFiles('Pull API')
                          "
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Pull API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button> -->
                        <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            hasChannelSampleFiles('Pull API')
                          "
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('Pull API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                      </div>
                    </div>

                    <!-- Pull API Configuration -->
                    <div
                      *ngIf="selectedChannelView === 'configuration'"
                      class="api-configuration"
                      fxLayout="column"
                      fxLayoutGap="20px"
                    >
                      <div class="config-section">
                        <div class="config-label">API URL</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input
                            matInput
                            value="https://api.mockdata.io/v1/resources/9f3a7c1b-4c2e-4f2a-a3e7-bf62d12e0e89"
                          />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">API KEY</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input
                            matInput
                            value="9f3c7e42-bd4a-4d7d-8a13-58efb8cf2ea4"
                          />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Documentation</div>
                        <div class="documentation-file">
                          <span>Leviton_API_Doc.txt</span>
                          <button mat-icon-button>
                            <mat-icon>file_download</mat-icon>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Pull API Input Files -->
                    <div *ngIf="selectedChannelView === 'input-files'">
                      <!-- Empty state when no files -->
                      <div
                        *ngIf="!hasChannelSampleFiles('Pull API')"
                        class="channel-empty-state"
                        fxLayout="column"
                        fxLayoutAlign="center center"
                        fxLayoutGap="10px"
                      >
                        <img
                          src="assets/images/supplier-configuration/upload-file.svg"
                          alt="Upload icon"
                        />
                        <div class="empty-state-title">
                          Please Add Sample Input File
                        </div>
                        <div class="empty-state-subtitle">
                          Please Add Sample Input Files by clicking the button
                          below.
                        </div>
                        <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Pull API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button>
                      </div>

                      <!-- Files list when files exist -->
                      <div
                        *ngIf="hasChannelSampleFiles('Pull API')"
                        class="channel-files-list"
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          class="files-container"
                          fxFlex
                          fxLayout="row wrap"
                          fxLayoutGap="16px"
                        >
                          <div
                            *ngFor="let file of channelSampleFiles['Pull API']"
                            class="file-item"
                            fxLayout="row"
                            fxLayoutAlign="space-between center"
                            fxLayoutGap="100px"
                          >
                            <span class="file-name">{{ file }}</span>
                            <div class="file-actions">
                              <button
                                mat-icon-button
                                color="primary"
                                (click)="downloadTemplate('Pull API')"
                                matTooltip="Download"
                              >
                                <mat-icon>get_app</mat-icon>
                              </button>
                              <button
                                mat-icon-button
                                color="warn"
                                (click)="toggleSampleFileSelection(file, false)"
                                matTooltip="Remove"
                              >
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Push API Channel Content -->
                  <div
                    *ngIf="
                      selectedChannel === 'Push API' &&
                      !channelStatus['Push API']
                    "
                    class="channel-not-configured"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-not-configured.svg"
                      alt="Upload icon"
                    />
                    <div class="not-configured-title">
                      Input channel Push API is not configured
                    </div>
                    <div class="not-configured-subtitle">
                      Please Raise a Request by clicking the button below, we
                      will configure it.
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="raise-request-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">bolt</mat-icon>
                      Raise a Request
                    </button>
                  </div>

                  <!-- Push API Configuring State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Push API' &&
                      channelStatus['Push API'] &&
                      pushApiConfiguring
                    "
                    class="channel-configuring"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-configuring.svg"
                      alt="Configuring icon"
                    />
                    <div class="configuring-title">
                      We're currently configuring your Push API input channel.
                    </div>
                    <div class="configuring-subtitle">
                      You will be notified once it's ready to use.
                    </div>
                  </div>

                  <!-- Push API Configured State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Push API' &&
                      channelStatus['Push API'] &&
                      !pushApiConfiguring
                    "
                    class="channel-details push-api-channel"
                  >
                    <div
                      class="channel-view-tabs"
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                    >
                      <mat-button-toggle-group
                        [value]="selectedChannelView"
                        (change)="selectedChannelView = $event.value"
                      >
                        <mat-button-toggle value="configuration"
                          >Configuration</mat-button-toggle
                        >
                        <mat-button-toggle value="input-files"
                          >Input Files</mat-button-toggle
                        >
                      </mat-button-toggle-group>
                      <div fxLayout="row" fxLayoutGap="10px">
                        <button
                          class="view-ticket-btn"
                          mat-stroked-button
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <span>View Tickets</span>
                        </button>
                        <button
                          mat-raised-button
                          color="primary"
                          class="raise-request-btn"
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <mat-icon class="add-icon">bolt</mat-icon>
                          <span>Raise a Request</span>
                        </button>
                        <!-- <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            !hasChannelSampleFiles('Push API')
                          "
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Push API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button> -->
                        <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            hasChannelSampleFiles('Push API')
                          "
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('Push API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                      </div>
                    </div>

                    <!-- Push API Configuration -->
                    <div
                      *ngIf="selectedChannelView === 'configuration'"
                      class="api-configuration"
                      fxLayout="column"
                      fxLayoutGap="20px"
                    >
                      <div class="config-section">
                        <div class="config-label">API URL</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input
                            matInput
                            value="https://api.mockdata.io/v1/resources/9f3a7c1b-4c2e-4f2a-a3e7-bf62d12e0e89"
                          />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">API KEY</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input
                            matInput
                            value="9f3c7e42-bd4a-4d7d-8a13-58efb8cf2ea4"
                          />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Documentation</div>
                        <div class="documentation-file">
                          <span>CAX_API_Doc.txt</span>
                          <button mat-icon-button>
                            <mat-icon>file_download</mat-icon>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Push API Input Files -->
                    <div *ngIf="selectedChannelView === 'input-files'">
                      <!-- Empty state when no files -->
                      <div
                        *ngIf="!hasChannelSampleFiles('Push API')"
                        class="channel-empty-state"
                        fxLayout="column"
                        fxLayoutAlign="center center"
                        fxLayoutGap="10px"
                      >
                        <img
                          src="assets/images/supplier-configuration/upload-file.svg"
                          alt="Upload icon"
                        />
                        <div class="empty-state-title">
                          Please Add Sample Input File
                        </div>
                        <div class="empty-state-subtitle">
                          Please Add Sample Input Files by clicking the button
                          below.
                        </div>
                        <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Push API')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button>
                      </div>

                      <!-- Files list when files exist -->
                      <div
                        *ngIf="hasChannelSampleFiles('Push API')"
                        class="channel-files-list"
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          class="files-container"
                          fxFlex
                          fxLayout="row wrap"
                          fxLayoutGap="16px"
                        >
                          <div
                            *ngFor="let file of channelSampleFiles['Push API']"
                            class="file-item"
                            fxLayout="row"
                            fxLayoutAlign="space-between center"
                            fxLayoutGap="100px"
                          >
                            <span class="file-name">{{ file }}</span>
                            <div class="file-actions">
                              <button
                                mat-icon-button
                                color="primary"
                                (click)="downloadTemplate('Push API')"
                                matTooltip="Download"
                              >
                                <mat-icon>get_app</mat-icon>
                              </button>
                              <button
                                mat-icon-button
                                color="warn"
                                (click)="toggleSampleFileSelection(file, false)"
                                matTooltip="Remove"
                              >
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Database Channel Content -->
                  <div
                    *ngIf="
                      selectedChannel === 'Database' && !channelStatus.Database
                    "
                    class="channel-not-configured"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-not-configured.svg"
                      alt="Upload icon"
                    />
                    <div class="not-configured-title">
                      Input channel Database is not configured
                    </div>
                    <div class="not-configured-subtitle">
                      Please click the button below to Raise a request.
                    </div>
                    <div class="not-configured-subtitle">
                      Please include your Host, Database Name, Server Port URL,
                      access token or credentials.
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="raise-request-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">bolt</mat-icon>
                      Raise a Request
                    </button>
                  </div>

                  <!-- Database Configuring State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Database' &&
                      channelStatus.Database &&
                      databaseConfiguring
                    "
                    class="channel-configuring"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-configuring.svg"
                      alt="Configuring icon"
                    />
                    <div class="configuring-title">
                      We're currently configuring your Database input channel.
                    </div>
                    <div class="configuring-subtitle">
                      You will be notified once it's ready to use.
                    </div>
                  </div>

                  <!-- Database Configured State -->
                  <div
                    *ngIf="
                      selectedChannel === 'Database' &&
                      channelStatus.Database &&
                      !databaseConfiguring
                    "
                    class="channel-details database-channel"
                  >
                    <div
                      class="channel-view-tabs"
                      fxLayout="row"
                      fxLayoutAlign="space-between center"
                    >
                      <mat-button-toggle-group
                        [value]="selectedChannelView"
                        (change)="selectedChannelView = $event.value"
                      >
                        <mat-button-toggle value="configuration"
                          >Configuration</mat-button-toggle
                        >
                        <mat-button-toggle value="input-files"
                          >Input Files</mat-button-toggle
                        >
                      </mat-button-toggle-group>
                      <div fxLayout="row" fxLayoutGap="10px">
                        <button
                          class="view-ticket-btn"
                          mat-stroked-button
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <span>View Tickets</span>
                        </button>
                        <button
                          mat-raised-button
                          color="primary"
                          class="raise-request-btn"
                          fxLayout="row"
                          fxLayoutAlign="center center"
                        >
                          <mat-icon class="add-icon">bolt</mat-icon>
                          <span>Raise a Request</span>
                        </button>
                        <!-- <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            !hasChannelSampleFiles('Database')
                          "
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Database')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button> -->
                        <button
                          *ngIf="
                            selectedChannelView === 'input-files' &&
                            hasChannelSampleFiles('Database')
                          "
                          mat-raised-button
                          color="primary"
                          class="add-more-btn"
                          (click)="openSampleFileSelectionPanel('Database')"
                        >
                          <mat-icon>add</mat-icon>
                          Add More
                        </button>
                      </div>
                    </div>

                    <!-- Database Configuration -->
                    <div
                      *ngIf="selectedChannelView === 'configuration'"
                      class="database-configuration"
                      fxLayout="column"
                      fxLayoutGap="20px"
                    >
                      <div class="config-section">
                        <div class="config-label">Host</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="************" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Database Name</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="levition_data" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Server Port</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="5321" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Username</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput value="reader" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>

                      <div class="config-section">
                        <div class="config-label">Password</div>
                        <mat-form-field appearance="outline" class="full-width">
                          <input matInput type="password" value="123123123" />
                          <button mat-icon-button matSuffix>
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        </mat-form-field>
                      </div>
                    </div>

                    <!-- Database Input Files -->
                    <div *ngIf="selectedChannelView === 'input-files'">
                      <!-- Empty state when no files -->
                      <div
                        *ngIf="!hasChannelSampleFiles('Database')"
                        class="channel-empty-state"
                        fxLayout="column"
                        fxLayoutAlign="center center"
                        fxLayoutGap="10px"
                      >
                        <img
                          src="assets/images/supplier-configuration/upload-file.svg"
                          alt="File document icon"
                        />
                        <div class="empty-state-title">
                          Please Add Sample Input File
                        </div>
                        <div class="empty-state-subtitle">
                          Please Add Sample Input Files by clicking the button
                          below.
                        </div>
                        <button
                          mat-raised-button
                          color="primary"
                          class="upload-file-btn"
                          (click)="openSampleFileSelectionPanel('Database')"
                        >
                          <mat-icon>add</mat-icon>
                          Add Sample Input File
                        </button>
                      </div>

                      <!-- Files list when files exist -->
                      <div
                        *ngIf="hasChannelSampleFiles('Database')"
                        class="channel-files-list"
                        fxLayout="row wrap"
                        fxLayoutGap="16px"
                      >
                        <div
                          class="files-container"
                          fxFlex
                          fxLayout="row wrap"
                          fxLayoutGap="16px"
                        >
                          <div
                            *ngFor="let file of channelSampleFiles['Database']"
                            class="file-item"
                            fxLayout="row"
                            fxLayoutAlign="space-between center"
                            fxLayoutGap="100px"
                          >
                            <span class="file-name">{{ file }}</span>
                            <div class="file-actions">
                              <button
                                mat-icon-button
                                color="primary"
                                (click)="downloadTemplate('Database')"
                                matTooltip="Download"
                              >
                                <mat-icon>get_app</mat-icon>
                              </button>
                              <button
                                mat-icon-button
                                color="warn"
                                (click)="toggleSampleFileSelection(file, false)"
                                matTooltip="Remove"
                              >
                                <mat-icon>close</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- XYZ Channel Content -->
                  <div
                    *ngIf="selectedChannel === 'XYZ' && !channelStatus.XYZ"
                    class="channel-not-configured"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/channel-database-not-configured.svg"
                      alt="Upload icon"
                    />
                    <div class="not-configured-title">
                      XYZ is not configured
                    </div>
                    <div class="not-configured-subtitle">
                      Please Raise a Request by clicking the button below, we
                      will configure it.
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="raise-request-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">bolt</mat-icon>
                      Raise a Request
                    </button>
                  </div>

                  <!-- XYZ Access Denied State -->
                  <div
                    *ngIf="
                      selectedChannel === 'XYZ' &&
                      channelStatus.XYZ &&
                      xyzAccessDenied
                    "
                    class="channel-access-denied"
                    fxLayout="column"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                  >
                    <img
                      src="assets/images/supplier-configuration/locked.svg"
                      alt="Lock icon"
                    />
                    <div class="access-denied-title">
                      You don't have access to this input channel.
                    </div>
                    <div class="access-denied-subtitle">
                      Please click the button below to Request Access.
                    </div>
                    <button
                      mat-raised-button
                      color="primary"
                      class="request-access-btn"
                      fxLayout="row"
                      fxLayoutAlign="center center"
                    >
                      <mat-icon class="add-icon">key</mat-icon>
                      Request Access
                    </button>
                  </div>

                  <!-- Tooltip for XYZ -->
                  <div
                    *ngIf="
                      selectedChannel === 'XYZ' &&
                      channelStatus.XYZ &&
                      !xyzAccessDenied
                    "
                    class="xyz-tooltip"
                    style="
                      position: absolute;
                      top: 0;
                      left: 0;
                      background-color: #333;
                      color: white;
                      padding: 10px;
                      border-radius: 4px;
                      z-index: 1000;
                    "
                  >
                    <div class="tooltip-content">
                      <p>
                        You have no access to this subscription, please raise a
                        request to get accesss
                      </p>
                      <button mat-button color="primary">Request Access</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
        <mat-tab label="Files">
          <div class="tab-content">
            <!-- files content starts here -->
            <div
              class="files-content"
              style="padding: 24px 0px"
              fxLayout="column"
              fxLayoutGap="10px"
            >
              <div
                class="files-content-header"
                fxLayout="row"
                fxLayoutAlign="space-between center"
                fxFlex="100"
              >
                <div
                  class="left-action-container"
                  fxFlex="50"
                  style="width: 100%"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <mat-form-field
                    appearance="none"
                    class="search-filter search-filter-file"
                  >
                    <input
                      id="searchFile"
                      matInput
                      placeholder="Search supplier configuration file"
                      #searchVal
                      name="searchVal"
                      [(ngModel)]="searchVendorConfigurationFile"
                      (keydown.enter)="
                        getSearchVendorConfigurationFile(
                          searchVendorConfigurationFile
                        )
                      "
                    />
                    <mat-icon
                      matPrefix
                      class="search-icon"
                      (click)="
                        getSearchVendorConfigurationFile(
                          searchVendorConfigurationFile
                        )
                      "
                      >search</mat-icon
                    >
                    <mat-icon
                      matSuffix
                      class="remove-icon"
                      (click)="resetSearchVendorConfigurationFile()"
                      *ngIf="searchVendorConfigurationFile"
                      >close</mat-icon
                    >
                  </mat-form-field>
                  <!--  -->
                  <button
                    class="view-ticket-btn"
                    mat-stroked-button
                    fxLayout="row"
                    fxLayoutAlign="center center"
                  >
                    <span>View Tickets</span>
                  </button>
                  <!--  -->
                </div>
                <div
                  class="right-action-container"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <!--  -->
                  <button
                    mat-raised-button
                    color="primary"
                    class="raise-request-btn"
                    fxLayout="row"
                    fxLayoutAlign="center center"
                  >
                    <mat-icon class="add-icon">bolt</mat-icon>
                    <span>Raise a Request</span>
                  </button>
                  <!--  -->
                  <button
                    *ngIf="showTableViewForFileTab"
                    mat-button
                    class="filled-btn-primary"
                    fxLayout="row"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                    (click)="openUploadFilesPanel()"
                  >
                    <mat-icon class="add-icon">add</mat-icon>
                    <span>Upload Sample Input File</span>
                  </button>
                  <!-- <button
                    class="raise-a-request-btn-in-file filled-btn-primary"
                    mat-stroked-button
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                    fxLayoutGap="10px"
                  >
                    <img [src]="'assets/images/home-icons/drop.svg'" />
                    <span>Raise a Request</span>
                  </button> -->
                </div>
              </div>
              <!-- upload area for files -->
              <div
                *ngIf="!showTableViewForFileTab"
                class="upload-area-for-files"
                style="background-color: #edeff5; height: 300px"
                fxLayout="column"
                fxLayoutAlign="center center"
                fxLayoutGap="10px"
              >
                <!--  -->
                <img
                  [src]="'assets/images/supplier-configuration/upload-file.svg'"
                  alt=""
                  srcset=""
                />
                <span style="font-weight: 500"
                  >Please Upload Sample Input File</span
                >
                <span style="font-weight: 500; color: #434956; font-size: 12px"
                  >Please Upload Sample Input Files by clicking the button
                  below.</span
                >
                <button
                  mat-button
                  class="filled-btn-primary"
                  fxLayout
                  fxLayoutAlign="center center"
                  (click)="openUploadFilesPanel()"
                >
                  <mat-icon
                    class="add-icon"
                    style="margin-right: 8px; color: white"
                    >add</mat-icon
                  >
                  Upload Sample Input File
                </button>
                <!--  -->
              </div>
              <!--  -->
              <div *ngIf="showTableViewForFileTab" class="table-view-for-files">
                <div class="table-section">
                  <table mat-table [dataSource]="fileDataSource">
                    <!--  -->

                    <!-- Checkbox Column -->
                    <ng-container matColumnDef="select">
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        style="padding-left: 24px"
                      >
                        <mat-checkbox
                          [checked]="
                            fileDataSource?.length &&
                            fileSelectionModel.selected.length ===
                              fileDataSource?.length
                          "
                          [disabled]="
                            tableLoading || fileDataSource?.length === 0
                          "
                          (change)="toggleAllFiles()"
                        >
                        </mat-checkbox>
                      </th>
                      <td
                        mat-cell
                        *matCellDef="let element"
                        style="padding-left: 24px"
                      >
                        <div
                          class="select-file"
                          fxLayout="column"
                          fxLayoutAlign="center start"
                        >
                          <mat-checkbox
                            [checked]="fileSelectionModel.isSelected(element)"
                            [disabled]="tableLoading"
                            (change)="onFileToggle(element)"
                          >
                          </mat-checkbox>
                        </div>
                      </td>
                    </ng-container>

                    <!-- File Name Column -->
                    <ng-container matColumnDef="fileName">
                      <th mat-header-cell *matHeaderCellDef>File Name</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          fxLayout="row"
                          fxLayoutAlign="start center"
                          fxLayoutGap="8px"
                        >
                          <div fxLayout="column">
                            <span class="file-name">{{
                              element.fileName
                            }}</span>
                          </div>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Type Column -->
                    <ng-container matColumnDef="type">
                      <th mat-header-cell *matHeaderCellDef>Type</th>
                      <td mat-cell *matCellDef="let element">
                        <span class="file-type">{{ element.type }}</span>
                      </td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          class="status-container"
                          fxLayout="row"
                          fxLayoutAlign="start center"
                          fxLayoutGap="8px"
                        >
                          <div
                            *ngIf="element.status === 'In Review'"
                            class="status-indicator in-review"
                          >
                            <mat-icon>hourglass_empty</mat-icon>
                            <span>In Review</span>
                          </div>
                          <div
                            *ngIf="element.status === 'Active'"
                            class="status-indicator user-active-status"
                          >
                            <mat-icon>check_circle</mat-icon>
                            <span>Active</span>
                          </div>
                          <div
                            *ngIf="element.status === 'Setup in Progress'"
                            class="status-indicator setup"
                          >
                            <mat-icon>pending</mat-icon>
                            <span>Setup in Progress</span>
                          </div>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Uploaded By Column -->
                    <ng-container matColumnDef="uploadedBy">
                      <th mat-header-cell *matHeaderCellDef>Uploaded By</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          class="user-avatar"
                          fxLayout="row"
                          fxLayoutAlign="start center"
                        >
                          <div class="avatar">
                            <span class="initials">{{
                              element.uploadedBy
                            }}</span>
                          </div>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Uploaded On Column -->
                    <ng-container matColumnDef="uploadedOn">
                      <th mat-header-cell *matHeaderCellDef>Uploaded On</th>
                      <td mat-cell *matCellDef="let element">
                        <span class="upload-date">{{
                          element.uploadedOn
                        }}</span>
                      </td>
                    </ng-container>

                    <!-- Input Channel Column -->
                    <ng-container matColumnDef="inputChannel">
                      <th mat-header-cell *matHeaderCellDef>Input Channel</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          class="channel-chips"
                          fxLayout="row"
                          fxLayoutAlign="start center"
                          fxLayoutGap="4px"
                        >
                          <span
                            *ngIf="element.inputChannel.includes('Upload')"
                            class="channel-chip upload"
                            >Upload</span
                          >
                          <span
                            *ngIf="element.inputChannel.includes('FTP')"
                            class="channel-chip ftp"
                            >FTP</span
                          >
                          <span
                            *ngIf="element.inputChannel.includes('Email')"
                            class="channel-chip email"
                            >Email</span
                          >
                          <span
                            *ngIf="element.inputChannel.includes('API')"
                            class="channel-chip api"
                            >API</span
                          >
                        </div>
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          class="action-buttons"
                          fxLayout="row"
                          fxLayoutAlign="start center"
                          fxLayoutGap="8px"
                        >
                          <button
                            mat-icon-button
                            matTooltip="View Details"
                            (click)="viewFileDetails(element)"
                          >
                            <!-- <mat-icon>info_outline</mat-icon> -->
                            <img
                              src="assets/images/supplier-configuration/channel.svg"
                              alt="File icon"
                              style="width: 24px; height: 24px"
                            />
                          </button>
                          <button
                            mat-icon-button
                            matTooltip="Download"
                            class="filled-btn-primary"
                          >
                            <img
                              src="assets/images/supplier-configuration/file_download.svg"
                              alt="File icon"
                              style="width: 24px; height: 24px"
                            />
                            <!-- <mat-icon>cloud_download</mat-icon> -->
                          </button>
                        </div>
                      </td>
                    </ng-container>

                    <tr
                      mat-header-row
                      *matHeaderRowDef="displayedFileColumns"
                    ></tr>
                    <tr
                      mat-row
                      *matRowDef="let row; columns: displayedFileColumns"
                    ></tr>
                  </table>

                  <!-- No data message -->
                  <div
                    class="no-data"
                    *ngIf="fileDataSource?.length === 0"
                    fxLayout="row"
                    fxLayoutGap="10px"
                  >
                    <mat-icon fontSet="material-icons-outlined">info</mat-icon>
                    <span>No files available.</span>
                  </div>

                  <!-- Paginator -->
                  <div
                    class="custom-paginator"
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                  >
                    <div
                      class="jump-to-page"
                      fxLayout="row"
                      fxLayoutGap="10px"
                      fxLayoutAlign="center center"
                      *ngIf="fileDataSource?.length > 0"
                    >
                      <span>Page</span>
                      <input
                        min="1"
                        [max]="totalFilePages"
                        type="number"
                        (keydown.enter)="goToFilePage($event.target.value)"
                        [value]="currentFilePage"
                      />
                      <span>of</span>
                      <span>{{ totalFilePages }}</span>
                    </div>
                    <mat-paginator
                      #filePaginator
                      [pageSizeOptions]="[5, 10, 20, 50]"
                      showFirstLastButtons
                      [length]="totalFileItems"
                      [pageSize]="filePageSize"
                      [pageIndex]="currentFilePage - 1"
                      (page)="onFilePageChange($event)"
                      *ngIf="fileDataSource?.length > 0"
                    >
                    </mat-paginator>
                  </div>
                </div>
              </div>
            </div>

            <!-- files content ends here -->
          </div>
        </mat-tab>
        <mat-tab label="Users">
          <div class="tab-content">
            <!-- Users content starts here -->
            <div
              class="users-content"
              style="padding: 24px 0px"
              fxLayout="column"
              fxLayoutGap="10px"
            >
              <!-- Header with search and invite button -->
              <div
                class="users-content-header"
                fxLayout="row"
                fxLayoutAlign="space-between center"
                fxFlex="100"
              >
                <div
                  class="left-action-container"
                  fxFlex="50"
                  style="width: 100%"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <mat-form-field
                    appearance="none"
                    class="search-filter search-filter-user"
                  >
                    <input
                      id="searchUser"
                      matInput
                      placeholder="Search user"
                      #searchUserVal
                      name="searchUserVal"
                      [(ngModel)]="searchUser"
                    />
                    <mat-icon matPrefix class="search-icon">search</mat-icon>
                    <mat-icon
                      matSuffix
                      class="remove-icon"
                      (click)="searchUser = ''"
                      *ngIf="searchUser"
                      >close</mat-icon
                    >
                  </mat-form-field>
                </div>
                <div
                  class="right-action-container"
                  fxLayout="row"
                  fxLayoutGap="10px"
                >
                  <button
                    mat-button
                    class="filled-btn-primary invite-users-btn"
                    fxLayout="row"
                    fxLayoutAlign="center center"
                    fxLayoutGap="10px"
                    (click)="openInviteUserPanel()"
                  >
                    <mat-icon>person_add</mat-icon>
                    <span>Invite Users</span>
                  </button>
                </div>
              </div>

              <!-- Users table -->
              <div class="table-view-for-users">
                <div class="table-section">
                  <table mat-table [dataSource]="usersDataSource">
                    <!-- Checkbox Column -->
                    <ng-container matColumnDef="select">
                      <th
                        mat-header-cell
                        *matHeaderCellDef
                        style="padding-left: 24px"
                      >
                        <mat-checkbox
                          [checked]="
                            userSelectionModel.hasValue() &&
                            areAllUsersSelected()
                          "
                          [indeterminate]="
                            userSelectionModel.hasValue() &&
                            !areAllUsersSelected()
                          "
                          (change)="toggleAllUsers()"
                        >
                        </mat-checkbox>
                      </th>
                      <td
                        mat-cell
                        *matCellDef="let row"
                        style="padding-left: 24px"
                      >
                        <mat-checkbox
                          [checked]="userSelectionModel.isSelected(row)"
                          (change)="onUserToggle(row)"
                        >
                        </mat-checkbox>
                      </td>
                    </ng-container>

                    <!-- Name Column -->
                    <ng-container matColumnDef="name">
                      <th mat-header-cell *matHeaderCellDef>Name</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.name }}
                      </td>
                    </ng-container>

                    <!-- Mail ID Column -->
                    <ng-container matColumnDef="mailId">
                      <th mat-header-cell *matHeaderCellDef>Mail ID</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.mailId }}
                      </td>
                    </ng-container>

                    <!-- Joined On Column -->
                    <ng-container matColumnDef="joinedOn">
                      <th mat-header-cell *matHeaderCellDef>Joined On</th>
                      <td mat-cell *matCellDef="let element">
                        <div *ngIf="element.status === 'Active'">
                          {{ element.joinedOn }}
                        </div>
                        <div
                          *ngIf="element.status === 'Invite Sent'"
                          class="status-badge invite-sent"
                        >
                          <mat-icon>mail_outline</mat-icon> Invite Sent
                        </div>
                        <div
                          *ngIf="element.status === 'Invite Expired'"
                          class="status-badge invite-expired"
                        >
                          <mat-icon>error_outline</mat-icon> Invite Expired
                        </div>
                      </td>
                    </ng-container>

                    <!-- Batches Column -->
                    <ng-container matColumnDef="batches">
                      <th mat-header-cell *matHeaderCellDef>Batches</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.batches || "-" }}
                      </td>
                    </ng-container>

                    <!-- Last Login Column -->
                    <ng-container matColumnDef="lastLogin">
                      <th mat-header-cell *matHeaderCellDef>Last Login</th>
                      <td mat-cell *matCellDef="let element">
                        {{ element.lastLogin || "-" }}
                      </td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let element">
                        <div
                          *ngIf="element.status === 'Active'"
                          class="status-indicator user-active-status"
                        >
                          Active
                          <mat-slide-toggle
                            [checked]="true"
                            color="primary"
                            class="active-toggle"
                          ></mat-slide-toggle>
                        </div>
                        <div
                          *ngIf="element.status !== 'Active'"
                          class="action-buttons"
                        >
                          <button
                            mat-stroked-button
                            class="resend-button"
                            (click)="resendInvite(element)"
                          >
                            <mat-icon>refresh</mat-icon> Resend
                          </button>
                          <button
                            mat-icon-button
                            class="delete-button"
                            (click)="deleteInvite(element)"
                          >
                            <mat-icon
                              *ngIf="element.status === 'Invite Expired'"
                              >delete</mat-icon
                            >
                            <mat-icon *ngIf="element.status === 'Invite Sent'"
                              >close</mat-icon
                            >
                          </button>
                        </div>
                      </td>
                    </ng-container>

                    <tr
                      mat-header-row
                      *matHeaderRowDef="displayedUserColumns"
                    ></tr>
                    <tr
                      mat-row
                      *matRowDef="let row; columns: displayedUserColumns"
                    ></tr>
                  </table>

                  <!-- No data message -->
                  <div
                    class="no-data"
                    *ngIf="usersDataSource?.length === 0"
                    fxLayout="row"
                    fxLayoutGap="10px"
                  >
                    <mat-icon fontSet="material-icons-outlined">info</mat-icon>
                    <span>No users available.</span>
                  </div>

                  <!-- Paginator -->
                  <div
                    class="custom-paginator"
                    fxLayout="row"
                    fxLayoutAlign="space-between center"
                    *ngIf="usersDataSource?.length > 0"
                  >
                    <div
                      class="jump-to-page"
                      fxLayout="row"
                      fxLayoutGap="10px"
                      fxLayoutAlign="center center"
                    >
                      <span>Page</span>
                      <input
                        min="1"
                        [max]="totalUserPages"
                        type="number"
                        (keydown.enter)="goToUserPage($event.target.value)"
                        [value]="currentUserPage"
                      />
                      <span>of</span>
                      <span>{{ totalUserPages }}</span>
                    </div>
                    <mat-paginator
                      #userPaginator
                      [pageSizeOptions]="[5, 10, 20, 50]"
                      showFirstLastButtons
                      [length]="totalUserItems"
                      [pageSize]="userPageSize"
                      [pageIndex]="currentUserPage - 1"
                      (page)="onUserPageChange($event)"
                    >
                    </mat-paginator>
                  </div>
                </div>
              </div>
            </div>
            <!-- Users content ends here -->
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>

<!-- Side Panel for Upload File in supplier configuration -->
<app-side-panel
  [sidenavTemplateRef]="uploadPanelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'uploadPanel'"
>
</app-side-panel>

<!-- Upload Panel Content Template in supplier configuration for files tab -->
<ng-template #uploadPanelContent>
  <div class="upload-panel-content" fxLayout="column" fxLayoutGap="20px">
    <!-- Debug message removed -->

    <div
      fxLayout="row"
      fxLayoutAlign="space-between center"
      style="margin-top: 50px"
    >
      <p class="panel-header">Upload Sample Input</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeUploadFilePanel()"
        *ngIf="!isFileServiceBusy"
      />
    </div>

    <!-- Show file selection when in Database channel and files exist -->
    <div
      *ngIf="selectedChannel === 'Database' && files.length > 0"
      class="file-selection-panel"
    >
      <div class="file-list-header">
        <p>Please select the files you want to add</p>
      </div>

      <div class="file-selection-list">
        <div
          *ngFor="let file of files"
          class="file-selection-item"
          fxLayout="row"
          fxLayoutAlign="start center"
          fxLayoutGap="10px"
        >
          <mat-checkbox
            [checked]="file.selected"
            (change)="toggleFileSelection(file)"
          ></mat-checkbox>

          <div
            class="file-info"
            fxLayout="row"
            fxLayoutAlign="start center"
            fxLayoutGap="10px"
          >
            <span class="file-name">{{ file.name }}</span>
            <button mat-icon-button (click)="onRemove(file)">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <div
        class="action-buttons"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
      >
        <button
          mat-stroked-button
          fxFlex="1 1 auto"
          (click)="closeUploadFilePanel()"
        >
          Back
        </button>
        <button
          mat-raised-button
          class="add-btn filled-btn-primary"
          fxFlex="1 1 auto"
          (click)="addSelectedFiles()"
        >
          Add
        </button>
      </div>
    </div>

    <!-- Standard upload form when not in file selection mode -->
    <form
      *ngIf="!(selectedChannel === 'Database' && files.length > 0)"
      class="upload-form"
      [formGroup]="fileUploadFormForVendor"
      (ngSubmit)="upload()"
    >
      <!-- Main dropzone area -->
      <ngx-dropzone
        (change)="onSelect($event)"
        [multiple]="true"
        [accept]="acceptedFileTypes"
        style="text-align: center"
        [disabled]="isFileServiceBusy"
      >
        <ngx-dropzone-label fxLayout="column" fxLayoutAlign="center center">
          <mat-icon fxLayoutAlign="space-between center">folder_open</mat-icon>
          <span>Drag and Drop your files here</span>
          <span>or</span>
          <button type="button" mat-stroked-button color="primary">
            Browse Files
          </button>
        </ngx-dropzone-label>
      </ngx-dropzone>

      <!-- File list below dropzone -->
      <div class="file-list" *ngIf="files.length > 0">
        <div
          class="file-item"
          *ngFor="let f of files"
          fxLayout="row"
          fxLayoutAlign="start center"
        >
          <img
            src="assets/images/supplier-configuration/raise-request-in-file-tab.svg"
            alt="File icon"
            style="margin-right: 10px; width: 24px; height: 24px"
          />
          <span
            class="file-name"
            [matTooltip]="f.name"
            matTooltipPosition="above"
            >{{ f.name }}</span
          >
          <span class="spacer"></span>
          <button mat-icon-button (click)="onRemove(f)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>

      <!-- Form fields -->
      <div>
        <!-- Channel selection field -->
        <div fxLayout="column" class="form-field-container">
          <p>Select Channel</p>
          <mat-form-field appearance="outline" class="full-width">
            <mat-select
              placeholder="Select Channel"
              formControlName="channels"
              multiple
            >
              <mat-option
                *ngFor="let channel of channelOptions"
                [value]="channel"
              >
                {{ channel }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                fileUploadFormForVendor.controls['channels'].hasError(
                  'required'
                )
              "
            >
              Please select at least one channel
            </mat-error>
          </mat-form-field>
          <!-- Selected channel chips -->
          <div
            class="selected-channels"
            *ngIf="fileUploadFormForVendor.get('channels').value?.length > 0"
          >
            <mat-chip-list>
              <mat-chip
                *ngFor="
                  let channel of fileUploadFormForVendor.get('channels').value
                "
                [removable]="true"
                [ngClass]="channel.toLowerCase() + '-chip'"
                (removed)="removeChannel(channel)"
              >
                {{ channel }}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip>
            </mat-chip-list>
          </div>
        </div>

        <div fxLayout="row" fxLayoutAlign="space-between end">
          <p>Description (Optional)</p>
        </div>

        <mat-form-field
          class="description"
          appearance="outline"
          style="width: 100%"
        >
          <textarea
            autofocus
            matInput
            placeholder="Add description here"
            rows="5"
            formControlName="description"
          ></textarea>
        </mat-form-field>
        <mat-error
          *ngIf="
            fileUploadFormForVendor.controls['description'].hasError(
              'maxlength'
            )
          "
        >
          Description should not exceed 2000 characters*
        </mat-error>
        <mat-error
          *ngIf="
            fileUploadFormForVendor.controls['description'].hasError(
              'whitespace'
            ) ||
            fileUploadFormForVendor.controls['description'].hasError('pattern')
          "
        >
          Please enter a valid description
        </mat-error>
      </div>
      <!-- Upload button -->
      <div
        class="action-buttons"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
      >
        <button
          mat-stroked-button
          fxFlex="1 1 auto"
          (click)="closeUploadFilePanel()"
        >
          Back
        </button>
        <button
          mat-raised-button
          class="add-btn filled-btn-primary"
          fxFlex="1 1 auto"
          fxLayout
          fxLayoutAlign="center center"
          [disabled]="
            fileUploadFormForVendor.invalid ||
            files.length === 0 ||
            isFileServiceBusy
          "
        >
          <span *ngIf="!isFileServiceBusy">Upload</span>
          <span *ngIf="isFileServiceBusy">Uploading...</span>
        </button>
      </div>
    </form>
  </div>
</ng-template>
<!-- file upload panel for the channel view ends here -->

<!-- Side Panel for Invite Users in supplier configuration -->
<app-side-panel
  [sidenavTemplateRef]="inviteUserPanelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'invitePanel'"
>
</app-side-panel>

<!-- Invite Users Panel Content Template -->
<ng-template #inviteUserPanelContent>
  <div class="invite-users-panel" fxLayout="column" fxLayoutGap="20px">
    <div class="header" fxLayout="row" fxLayoutAlign="space-between center">
      <p class="panel-header">Invite Users</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeInviteUserPanel()"
      />
    </div>
    <div class="invite-form" [formGroup]="inviteUsersForm">
      <div class="form-section">
        <label class="form-label">Mail</label>
        <div
          class="email-inputs"
          formArrayName="emails"
          fxLayout="column"
          fxLayoutGap="10px"
        >
          <div
            *ngFor="let emailControl of emailControls; let i = index"
            class="email-input-row"
            fxLayout="row"
            fxLayoutAlign="start center"
            fxLayoutGap="10px"
          >
            <mat-form-field appearance="outline" fxFlex>
              <input
                matInput
                placeholder="<EMAIL>"
                [formControlName]="i"
              />
              <mat-error *ngIf="emailControls[i].touched">
                <span *ngIf="emailControls[i].hasError('required')"
                  >Email is required</span
                >
                <span *ngIf="emailControls[i].hasError('email')"
                  >Please enter a valid email address</span
                >
                <span *ngIf="emailControls[i].hasError('duplicate')"
                  >This email is already added</span
                >
              </mat-error>
            </mat-form-field>
            <button
              mat-icon-button
              color="warn"
              type="button"
              (click)="removeInviteEmail(i)"
              *ngIf="emailControls.length > 1"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        <div class="add-more-button" fxLayout="row" fxLayoutAlign="end center">
          <button
            mat-button
            color="primary"
            type="button"
            (click)="addMoreEmail()"
          >
            <mat-icon>add</mat-icon> Add
          </button>
        </div>
      </div>
    </div>

    <div
      class="action-buttons"
      fxLayout="row"
      fxLayoutAlign="space-between center"
      fxLayoutGap="10px"
    >
      <button
        mat-stroked-button
        class="back-button"
        fxFlex="1 1 auto"
        type="button"
        (click)="closeInviteUserPanel()"
      >
        Back
      </button>
      <button
        mat-raised-button
        class="send-invite-button filled-btn-primary"
        fxFlex="1 1 auto"
        type="button"
        (click)="sendInvites()"
        [disabled]="!canSendInvites()"
      >
        Send Invite
      </button>
    </div>
  </div>
</ng-template>

<!-- file upload panel for the channel view starts here -->
<app-side-panel
  [sidenavTemplateRef]="uploadPanelContentForChannelView"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'uploadPanelForChannelView'"
>
</app-side-panel>

<!-- Upload Panel Content Template in supplier configuration for files tab -->
<ng-template #uploadPanelContentForChannelView>
  <div class="upload-panel-content" fxLayout="column" fxLayoutGap="20px">
    <div
      fxLayout="row"
      fxLayoutAlign="space-between center"
      style="margin-top: 30px"
    >
      <p class="panel-header">Add Sample Input</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeUploadFilePanel()"
        *ngIf="!isFileServiceBusy"
      />
    </div>
    <!--  -->
    <div class="content">
      <div class="checkbox-section">
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Upload']"
            (change)="updateChannel('Upload', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Upload"] || "Upload" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Upload')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Email']"
            (change)="updateChannel('Email', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Email"] || "Email" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Email')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['FTP']"
            (change)="updateChannel('FTP', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["FTP"] || "FTP" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('FTP')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Push API']"
            (change)="updateChannel('Push API', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Push API"] || "Push API" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Push API')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Pull API']"
            (change)="updateChannel('Pull API', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Pull API"] || "Pull API" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Pull API')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Database']"
            (change)="updateChannel('Database', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Database"] || "Database" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Database')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
        <div class="checkbox-item">
          <mat-checkbox
            [checked]="selectedChannels['Email (Alternative)']"
            (change)="updateChannel('Email (Alternative)', $event.checked)"
            color="primary"
          >
            {{ randomFileLabels["Email (Alternative)"] || "Email" }}
          </mat-checkbox>
          <button
            mat-icon-button
            class="download-button"
            color="primary"
            (click)="downloadTemplate('Email (Alternative)')"
          >
            <mat-icon class="download-icon">get_app</mat-icon>
          </button>
        </div>
      </div>

      <div
        class="actions"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
      >
        <button class="back-button" fxFlex="1 1 auto">Back</button>
        <button class="save-button" fxFlex="1 1 auto">Save</button>
      </div>
    </div>
    <!--  -->
  </div>
</ng-template>

<!-- File Details Panel -->
<app-side-panel
  [sidenavTemplateRef]="fileDetailsPanelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'fileDetailsPanel'"
>
</app-side-panel>

<!-- File Details Panel Content Template -->
<ng-template #fileDetailsPanelContent>
  <app-file-details></app-file-details>
</ng-template>

<!-- Sample File Selection Panel -->
<app-side-panel
  [sidenavTemplateRef]="sampleFileSelectionPanelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'sampleFileSelectionPanel'"
>
</app-side-panel>
<!-- Sample File Selection Panel Content Template -->
<ng-template #sampleFileSelectionPanelContent>
  <div class="sample-file-selection-panel">
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <p class="panel-header">Add Sample Input Files</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeSampleFileSelectionPanel()"
      />
    </div>

    <div class="content">
      <div class="sample-files-list">
        <div
          *ngFor="let file of randomFileLabels | keyvalue"
          class="sample-file-item"
          fxLayout="row"
          fxLayoutAlign="space-between center"
        >
          <mat-checkbox
            [checked]="isSampleFileSelected(file.value)"
            (change)="toggleSampleFileSelection(file.value, $event.checked)"
            color="primary"
          >
            {{ file.value }}
          </mat-checkbox>
          <button
            mat-icon-button
            color="primary"
            (click)="downloadTemplate(file.key)"
            matTooltip="Download"
          >
            <mat-icon>get_app</mat-icon>
          </button>
        </div>
      </div>

      <div
        class="action-buttons"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
        style="margin-top: 20px"
      >
        <button
          mat-stroked-button
          class="back-button"
          fxFlex="1 1 auto"
          (click)="closeSampleFileSelectionPanel()"
        >
          Back
        </button>
        <button
          mat-raised-button
          class="send-invite-button filled-btn-primary"
          fxFlex="1 1 auto"
          (click)="saveSampleFileSelection()"
        >
          Save
        </button>
      </div>
    </div>
  </div>
</ng-template>

<!-- Edit Vendor Panel Template -->
<ng-template #editVendorPanelContent>
  <div class="edit-vendor-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Edit Vendor Details</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeEditPanel()"
      />
    </div>

    <form [formGroup]="vendorEditForm" class="vendor-form" fxLayout="column">
      <p>Registered Name</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter vendor name"
          formControlName="registeredName"
        />
        <mat-error
          *ngIf="vendorEditForm.get('registeredName')?.errors?.['required'] && vendorEditForm.get('registeredName')?.touched"
        >
          Registered name is required
        </mat-error>
      </mat-form-field>

      <p>Display Name</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter display name"
          formControlName="displayName"
        />
        <mat-error
          *ngIf="vendorEditForm.get('displayName')?.errors?.['required'] && vendorEditForm.get('displayName')?.touched"
        >
          Display name is required
        </mat-error>
      </mat-form-field>

      <p>Vendor Type</p>
      <mat-form-field appearance="outline">
        <mat-select
          placeholder="Select vendor type"
          formControlName="vendorType"
        >
          <mat-option value="type1">Type 1</mat-option>
          <mat-option value="type2">Type 2</mat-option>
          <mat-option value="type3">Type 3</mat-option>
        </mat-select>
        <mat-error
          *ngIf="vendorEditForm.get('vendorType')?.errors?.['required'] && vendorEditForm.get('vendorType')?.touched"
        >
          Vendor type is required
        </mat-error>
      </mat-form-field>

      <p>Code</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter vendor code"
          formControlName="code"
        />
        <mat-error
          *ngIf="vendorEditForm.get('code')?.errors?.['required'] && vendorEditForm.get('code')?.touched"
        >
          Code is required
        </mat-error>
      </mat-form-field>

      <p>NID Pub</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter NID Public ID"
          formControlName="nidPub"
        />
        <mat-error
          *ngIf="vendorEditForm.get('nidPub')?.errors?.['required'] && vendorEditForm.get('nidPub')?.touched"
        >
          NID Public ID is required
        </mat-error>
      </mat-form-field>

      <p>Additional Codes</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter additional code"
          formControlName="additionalCodes"
        />
      </mat-form-field>

      <p>Domain</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="https://example.com"
          formControlName="domain"
        />
        <mat-error
          *ngIf="
            vendorEditForm.get('domain')?.errors &&
            vendorEditForm.get('domain')?.touched
          "
        >
          {{ getDomainErrorMessage() }}
        </mat-error>
        <mat-hint>Enter domain starting with https://</mat-hint>
      </mat-form-field>

      <div
        class="action-buttons"
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
        style="margin-top: 24px"
      >
        <button mat-stroked-button fxFlex="1 1 auto" (click)="closeEditPanel()">
          Back
        </button>
        <button
          mat-raised-button
          class="save-btn filled-btn-primary"
          fxFlex="1 1 auto"
          (click)="saveVendorDetails()"
          [disabled]="!vendorEditForm.valid || vendorEditForm.pristine"
        >
          Save Changes
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- Side Panel for Edit Vendor -->
<app-side-panel
  [sidenavTemplateRef]="editVendorPanelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'editVendorPanel'"
>
</app-side-panel>
