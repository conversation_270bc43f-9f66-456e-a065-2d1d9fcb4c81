// panel bar container
.side-nav-bar {
  position: fixed;
  z-index: 2;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  .side-nav-bar-overlay {
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
  }

  .side-nav-bar-menu-container {
    position: absolute;
    top: 0;
    bottom: 0;
    background: #fff;
    display: flex;
    flex-direction: column;

    .side-nav-bar-close {
      float: right;
      display: block;
      font-size: 1.5em;
      margin-right: 5%;
      margin-top: 5%;
      cursor: pointer;
    }
  }

  .side-nav-bar-content-container {
    padding: 32px;
    overflow-y: scroll;
  }
}

.side-nav-bar-overlay-collapsed {
  background: transparent !important;
}

.side-nav-bar-collapsed {
  visibility: collapse !important;
}

.aside-section {
  // margin-top: 10%;
  margin-left: 10%;
  .__head {
    width: 100%;
    height: 19px;
    font: var(--unnamed-font-style-normal) normal var(--unnamed-font-weight-800)
      var(--unnamed-font-size-16) / var(--unnamed-line-spacing-24)
      var(--unnamed-font-family-montserrat);
    letter-spacing: var(--unnamed-character-spacing-0);
    color: var(--light-on-primary-high-emphasis);
    text-align: left;
    font: normal normal 800 16px/24px Montserrat;
    letter-spacing: 0px;
    color: #343434;
    opacity: 1;
  }
  .__sub-head {
    width: 100%;
    height: 15px;
    font: var(--unnamed-font-style-normal) normal
      var(--unnamed-font-weight-normal) var(--unnamed-font-size-12) /
      var(--unnamed-line-spacing-20) var(--unnamed-font-family-montserrat);
    letter-spacing: var(--unnamed-character-spacing-0);
    color: var(--light-on-primary-mid-emphasis);
    text-align: left;
    font: normal normal normal 12px/20px Montserrat;
    letter-spacing: 0px;
    color: #646464;
  }
}

::-webkit-scrollbar {
  width: 0px;
}
