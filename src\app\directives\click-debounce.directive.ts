import { Directive, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { MatSelect } from '@angular/material/select';
import { Subject, Subscription, debounceTime, distinctUntilChanged } from 'rxjs';

@Directive({
  selector: '[appClickDebounce]'
})
export class ClickDebounceDirective {
  @Input() debounceTime = 500;
  @Output() debounceClick = new EventEmitter();
  private clicks = new Subject();
  private subscription: Subscription;

  constructor(private select:MatSelect) { }

  ngOnInit() {
    // console.log(this.select);
    this.subscription = this.select.selectionChange.pipe(
      debounceTime(this.debounceTime),
      distinctUntilChanged()
    ).subscribe(
      e => this.debounceClick.emit(e)
    )
    
    // this.subscription = this.clicks.pipe(debounceTime(this.debounceTime)).subscribe(e => this.debounceClick.emit(e));
  }

  ngOnDestroy() {
    if(this.subscription)
    this.subscription.unsubscribe();
  }

}
