<h2 mat-dialog-title>Are you sure ?</h2>
<mat-dialog-content class="mat-typography">
    <p *ngIf="batch_id">On cancellation, this batch will be moved to the Cancelled Tab and a notification will be sent.</p>
    <p class="header">Please state the reason for rejection below:</p>
    <form class="upload-form" [formGroup]="reasonForm">
    <mat-form-field class="text-field" appearance="outline">
      <textarea matInput placeholder="Type your text here…" formControlName="comment" [(ngModel)]="fromDialog"></textarea>
    </mat-form-field>
    </form>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>Cancel</button>
  <button mat-flat-button color="primary" cdkFocusInitial [mat-dialog-close]="true" [disabled]="reasonForm.invalid || fromDialog == ''" (click)="closeDialog()" >Submit</button>
</mat-dialog-actions>
