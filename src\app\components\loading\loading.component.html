<div fxFlex="100" class="loading-wrapper" fxLayoutAlign="center center" >
    <mat-spinner *ngIf="!queryParam.error"
        fxLayoutAlign="center center"
        diameter="80" strokeWidth="5"
    ></mat-spinner>
    <div *ngIf="queryParam.error" fxLayout="column" fxLayoutAlign="center center">
        <div fxLayout="column" fxLayoutAlign="center center" fxLayoutGap="10px">
            <mat-icon>construction</mat-icon>
            <span>Access Denied!!</span>
            <p> The page you are trying to reach cannot be accessed at the moment.</p>
        </div>      
    </div>

  </div>