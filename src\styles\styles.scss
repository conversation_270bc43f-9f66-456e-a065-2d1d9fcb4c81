/* You can add global styles to this file, and also import other style files */
@import "./variables";
@import "quill/dist/quill.core.css";
html,
body {
  height: 100%;
  background-color: #f3f6fa;
  margin: 0;
  font-family: $site-font;
}
a {
  cursor: pointer;
  text-decoration: none;
  font-weight: 700;
}

// Scroll bar

/* scroll width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */

::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
// material overriden styles
mat-tab-group {
  width: 100%;
  // remove border bottom from all mat tabs
  .mat-tab-header {
    border-bottom: none !important;
  }
  mat-tab {
    position: fixed;
  }
}
// home date range filter
.date-range-filter {
  width: 340px;
  height: 40px;
  border: 1px solid #c1c4d6;
  box-sizing: border-box;
  border-radius: 4px;
  mat-select {
    padding: 2px 0;
    // width is 40px (margin-left) less than total width
    width: 300px;
    margin-left: 40px;
    margin-top: -1px;
    font: normal normal normal 14px/21px $site-font;
    .mat-select-placeholder {
      color: #3b3e48;
      padding-left: 35px;
    }
    .mat-select-arrow-wrapper {
      padding-right: 5px;
      .mat-select-arrow {
        color: #c1c4d6;
      }
    }
  }
  .mat-form-field-flex {
    border-radius: 4px;
    .mat-form-field-infix {
      border: 0px solid #e6e8f0;
      border-radius: 5px;
      width: 100%;
    }
  }
  .date-range-icon {
    margin-top: -30px;
    padding: 5px;
  }
}
// datepicker range and other mat options
mat-option {
  color: $tabel-data;
  text-align: left;
  padding: 25px 10px;
  border-bottom: 1px solid #e6e8f0;
  .range-category {
    font: normal normal normal 14px/21px $site-font;
    .date-range {
      font: normal normal 300 12px/18px $site-font;
    }
  }
}
.mat-option.mat-active {
  background: $neutral-0 !important;
}
.mat-option:hover {
  background: $neutral-0 !important;
}
// select dropdown overlay
// .mat-select-panel {
//   margin-top: 50px;
// }

// app dropdown
.app-dropdown {
  .mat-form-field-wrapper {
    padding-bottom: 0;
    .mat-form-field-flex {
      border-radius: 4px;
      .mat-form-field-infix {
        border: 1px solid #c1c4d6;
        border-radius: 5px;
      }
    }
    .mat-select {
      padding: 2px 0;
      .mat-select-placeholder {
        color: #78787b;
        font-size: 14px;
        padding-left: 6px;
      }
      .mat-select-value {
        padding-left: 6px;
      }
      .mat-select-arrow-wrapper {
        padding-right: 5px;
        .mat-select-arrow {
          color: #c1c4d6;
        }
      }
    }
  }
}

// Home and all common classes - Filter section
.mat-menu-panel {
  min-height: 0 !important;
}
// module 0 sku chip list
.chip-list {
  .mat-chip-list-wrapper {
    margin: 0 !important;
  }
}
// notification - logout menu
mat-menu {
  .mat-menu:hover {
    background: $theme-hover !important;
    color: $theme-blue !important;
  }
}
.table-section {
  background: $theme-white;
  border-radius: 4px;
}
mat-tab-group {
  width: 100%;
}
.table-scroll::-webkit-scrollbar {
  display: none;
}

table {
  border-radius: 4px;
  width: 100%;
  height: 100%;
  font-size: 14px;
  line-height: 21px;
  .sub_text {
    padding-top: 10px;
    padding-bottom: 10px;
    font-family: $site-font;
    font-style: normal;
    font-weight: 300;
    font-size: 12px;
    line-height: 18px;
    color: #3b3e48;
  }
  .main-data {
    padding-top: 5px;
    font-family: $site-font;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    color: $tabel-data;
    ul {
      list-style: none;
      // width: 120px;
    }
  }
  .err-data{
    padding-top: 5px;
    font-family: $site-font;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    color: $theme-red;
    .err-icon{
      font-size: 15px;
      position: absolute;
      margin-top: 3px;
      padding-left: 5px;
    }
  }
  .img-name {
    text-decoration: underline;
    a{
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
    }
  }
}
.mat-cell {
  color: #646464;
  font-weight: 400;
}
th {
  background-color: $tabel-header;
  font-family: $site-font;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: $theme-black;
  opacity: 1;
  max-width: 100px;
}
td {
  color: $tabel-data;
  font-family: $site-font;
  font-weight: normal;
  max-width: 100px;
  .table-data {
    padding: 10px 0;
  }
}
// toolbar section wrapper
.wrapper {
  // margin-right: 40px;
  margin-left: 76px;
  margin-top: 70px;
  .filter-container {
    width: 100%;
    z-index: 1;
    position: sticky;
    top: 70px;
    padding: 16px 40px 16px 16px;
    border-bottom: 2px solid #edeff5;
    background-color: $theme-background;
    opacity: 1;
    text-align: left;
    font: normal normal 600 16px/40px $site-font;
    letter-spacing: 0.32px;
    color: #3b3e48;
    opacity: 1;
    .search-filter {
      flex-grow: 1;
      width: 491px;
      height: 40px;
      border: 1px solid #c1c4d6;
      box-sizing: border-box;
      border-radius: 4px;
      input {
        width: 100%;
        height: 19px;
        margin-left: 5px;
        margin-top: -12px;
        margin-bottom: 10px;
        font-size: 14px;
      }
      .search-icon {
        font-size: 30px;
        color: #b6b6b6;
        padding: 5px;
        cursor: pointer;
      }
      .mat-form-field-suffix {
        bottom: 5px;
        .remove-icon{
          font-size: 24px;
          color: #b6b6b6;
          padding: 5px;
          cursor: pointer;
        }
      }

    }
    .upload-btn {
      width: auto;
      height: 40px;
      // background: $theme-button;
      border-radius: 4px;
      color: $theme-white;
      font-weight: 600;
    }
    .back-icon {
      padding-top: 5px;
      cursor: pointer;
    }
    .selected-batch {
      // width: 150px;
      // max-width: 200px;
      padding-left: 5px;
    }
    .reset-btn {
      height: 38px;
    }
  }
  //table
  .table-wrapper {
    z-index: 0;
    margin: 20px 40px 40px 20px;
    mat-chip-list:focus {
      outline: 0;
    }
  }
}

::ng-deep .mat-tab-labels .mat-tab-label-active {
  // width: 112px;
  opacity: 1;
  font-family: $site-font;
  color: $theme-button;
}

::ng-deep .mat-tab-label {
  font-family: $site-font;
}

::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
  // background-color: $theme-button !important;
  height: 4px;
  // width: 100%;
  border-radius: 4px 4px 0px 0px;
}

// mat toggel (dashboard)
mat-button-toggle-group {
  border-radius: 4px !important;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  height: 44px;
  .mat-button-toggle-label-content {
    line-height: 42px !important;
  }
}

mat-button-toggle {
  width: 164px;
  box-sizing: border-box;
  background-color: $theme-white;
}

.mat-button-toggle-input {
  background-color: none !important;
  padding-left: 32px !important;
  width: max-content;
}

// mat toggle Favourite
.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
  background-color: $blue-100;
}
.mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
  background-color: #fff;
  // border: 1px solid $blue-100;
}
.mat-slide-toggle.mat-checked .mat-ripple-element {
  background-color: $blue-100;
}

// custom panginator
.custom-paginator {
  background-color: $theme-white;
  .jump-to-page {
    padding-left: 20px;
    color: #0000008a;
    input {
      width: 32px;
      height: 32px;
      border: 1px solid #e6e8f0;
      box-sizing: border-box;
      border-radius: 4px;
    }
    button {
      width: 40px;
      height: 20px;
      min-width: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #989898;
      background: #fff;
      box-shadow: none;
      border: 1px solid #949494;
    }
    button:disabled {
      color: #dbdbdb !important;
      border: 1px solid #dbdbdb !important;
    }
  }
}

// progress spinner for all tables
.loading-spinner {
  height: 200px;
}
// no data message for all tables
.no-data {
  height: 200px;
  color: gray;
  display: flex;
  justify-content: center;
  align-items: center;
}
//filter section comments icon
.messages-icon {
  cursor: pointer;
  .dot {
    position: relative;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: $theme-red;
    top: 6px;
    left: 18px;
  }
}
.batch-panel {
  margin-top: 40px;
  .panel-header {
    font-family: $site-font;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }
}
.brand-container {
  text-align: left;
  margin-top: 20px;
  // background-color: rgb(214, 213, 212);
  .brand-list {
    font-family: $site-font;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #222329;
  }
}
button.approve-btn {
  margin-top: 5px;
}
button.reject-btn {
  margin-bottom: 5px;
}

.mat-tab-label-active{
  opacity: 1 !important;
}

.github-comment-textarea {
  border: 1px solid #d0d7de !important;
  border-radius: 6px !important;
  min-height: 80px;
  background: #fff;
  font-size: 1rem;
  margin-bottom: 8px;
  .ql-toolbar {
    border-radius: 6px 6px 0 0 !important;
    background: #f6f8fa;
    border: none !important;
  }
  .ql-container {
    border-radius: 0 0 6px 6px !important;
    border: none !important;
  }
}