import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HelpService } from '../../service/help.service';
import { ActivatedRoute, Params } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { SnackbarService } from '../../service/snackbar.service';

@Component({
  selector: 'app-help',
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss'],
})
export class HelpComponent implements OnInit {
  selectedValForToggle = 'sales';
  modulesToBeDisplayed;
  concernedModuleList: any[] = [];
  retailersModuleList: any[] = [];
  suppliersModuleList: any[] = [];
  sales;
  support;
  subscriptionId;
  contactSalesFormValues;
  contactSales: boolean = true;
  contactSupport: boolean = false;
  loading: boolean = false;
  userInfo;
  emailRegEx = '^[a-z0-9._-]+@[a-z0-9.-]+\\.[a-z]{2,4}$';
  tagCheckRegex = '^(?!.*<[^>]+>).*';
  public contactSalesForm: FormGroup;
  constructor(
    public matSnackbar: MatSnackBar,
    private helpService: HelpService,
    private activatedroute: ActivatedRoute,
    private fb: FormBuilder,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit(): void {
    this.activatedroute.queryParams.subscribe((params: Params) => {
      this.subscriptionId = params.sub;
    });
    this.contactSalesForm = this.fb.group({
      email: ['', [Validators.required, Validators.email, Validators.pattern(this.emailRegEx)]],
      message: ['', [Validators.required, Validators.pattern(this.tagCheckRegex), this.noWhitespaceValidator]],
    });

    // me api response from local storage
    this.userInfo =
      (localStorage.getItem('user') &&
        JSON.parse(localStorage.getItem('user'))) ||
      undefined;
    this.contactSalesForm.controls['email'].setValue(this.userInfo.username);
  }

  // custom white space validator
  public noWhitespaceValidator(control: FormControl) {
    if(control.value)
    return (control.value || '').trim().length? null : { 'whitespace': true };       
  }

  /**
   * On button toggle change
   * @param val
   */
  onButtonToggle = (val) => {
    this.selectedValForToggle = val;
    if (val == 'sales') {
      this.contactSupport = false;
      this.contactSales = true;
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    } else {
      this.contactSales = false;
      this.contactSupport = true;
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    }
    this.contactSalesForm.controls['message'].setValue(
      ""
    );
  };

  /**
   * post mail to sales
   */
  postContactSales = () => {
    this.contactSalesFormValues = this.contactSalesForm.value;
    this.contactSalesFormValues;
    this.helpService
      .contactSales(
        this.subscriptionId,
        this.contactSalesFormValues.email,
        this.contactSalesFormValues.message
      )
      .subscribe({
        next: (resp) => {
            this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.contactSalesForm.reset();
          this.contactSalesForm.controls['email'].setValue(
            this.userInfo.username
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
              this.snackbarService.openSnackBar(`${JSON.stringify(HttpResponse.error.detail)}`, 'OK');
        },
      });
  };

  /**
   * post mail to support
   */
  postContactSupport = () => {
    this.contactSalesFormValues = this.contactSalesForm.value;
    this.helpService
      .contactSupport(
        this.subscriptionId,
        this.contactSalesFormValues.email,
        this.contactSalesFormValues.message
      )
      .subscribe({
        next: (resp) => {
            this.snackbarService.openSnackBar(resp.detail, 'OK');
          this.contactSalesForm.reset();
          this.contactSalesForm.controls['email'].setValue(
            this.userInfo.username
          );
        },
        error: (HttpResponse: HttpErrorResponse) => {
              this.snackbarService.openSnackBar(`${JSON.stringify(HttpResponse.error.detail)}`, 'OK');
        },
      });
  };

  /**
   * reset form
   */
  resetForm = () => {
    this.contactSalesForm.reset();
    this.contactSalesForm.controls['email'].setValue(this.userInfo.username);
  };
}
