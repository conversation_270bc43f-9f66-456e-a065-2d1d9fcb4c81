<div class="wrapper" fxLayout="column">
  <div class="filter-container" fxLayout="row" fxLayoutGap="10px" fxFlex="100">
    <!-- home page header -->
    <!-- search box -->
    <mat-form-field appearance="none" class="search-filter">
      <input
        id="search"
        matInput
        placeholder="Search Batch Id or Name here..."
        #searchVal
        name="searchVal"
        [(ngModel)]="search"
        (keydown.enter)="getSearchValue(search)"
      />
      <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)"
        >search</mat-icon
      >
      <mat-icon
        matSuffix
        class="remove-icon"
        (click)="resetSearch()"
        *ngIf="search"
        >close</mat-icon
      >
    </mat-form-field>

    <button
      class="filled-btn-primary"
      mat-raised-button
      fxLayout="row"
      fxLayoutAlign="space-between center"
      (click)="exportUsers()"
    >
      Export users
    </button>
    <button
      mat-button
      class="filled-btn-primary"
      fxLayout
      fxLayoutAlign="center center"
      (click)="reset()"
    >
      Reset
    </button>
    <!-- Add vendor -->
    <button
      mat-button
      class="filled-btn-primary"
      fxLayout
      fxLayoutAlign="center center"
      (click)="addVendor()"
    >
      <mat-icon class="add-icon" style="margin-right: 8px; color: white"
        >add</mat-icon
      >
      Add Vendor
    </button>
    <!-- end vendor -->
  </div>
  <div
    class="data-loading-spinner"
    fxLayoutAlign="center center"
    *ngIf="dataLoading"
  >
    <mat-spinner
      fxLayoutAlign="center center"
      diameter="90"
      strokeWidth="3"
    ></mat-spinner>
  </div>
  <!-- data table -->
  <div
    class="table-wrapper"
    fxFlex="100"
    fxLayout="column"
    fxLayoutGap="20px"
    *ngIf="!dataLoading"
  >
    <!-- table -->
    <div class="table-section">
      <!-- table -->
      <table mat-table [dataSource]="supplierDataSource">
        <!-- Position Column -->
        <ng-container matColumnDef="SupplierNameCode">
          <th mat-header-cell *matHeaderCellDef>Supplier Name & Code</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span
                class="main-data"
                *ngIf="element.supplier_name?.length !== 0; else noData"
              >
                {{ element.supplier_name }}</span
              >
              <span
                class="sub_text"
                *ngIf="element.supplier_code?.length !== 0; else noData"
                >{{ element.supplier_code }}</span
              >
            </div>
          </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="Modules_Subscribed">
          <th mat-header-cell *matHeaderCellDef>Modules Subscribed</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span
                class="main-data"
                *ngIf="element.modules_subscribed?.length !== 0; else noData"
              >
                {{ element.modules_subscribed.join(", ") }}</span
              >
            </div>
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="Input">
          <th mat-header-cell *matHeaderCellDef>Input</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span
                class="main-data"
                *ngIf="element.input?.length !== 0; else noData"
              >
                {{ element.input.join(", ") }}</span
              >
            </div>
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="Status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let element">
            <span
              class="main-data"
              *ngIf="element.status?.length !== 0; else noData"
            >
              {{ element.status }}
            </span>
          </td>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="No_of_Users">
          <th mat-header-cell *matHeaderCellDef>No of Users</th>
          <td mat-cell *matCellDef="let element">
            <span
              class="main-data"
              *ngIf="element.no_of_users?.length !== 0; else noData"
            >
              {{ element.no_of_users }}</span
            >
          </td>
        </ng-container>

        <ng-container matColumnDef="Last Login">
          <th mat-header-cell *matHeaderCellDef>Last Login</th>
          <td mat-cell *matCellDef="let element">
            <span
              class="main-data"
              *ngIf="element.last_login?.length !== 0; else noData"
            >
              {{ element.last_login | date }}</span
            >
          </td>
        </ng-container>

        <!-- ng-container matColumnDef="End_Date">
          <th mat-header-cell *matHeaderCellDef>Subscription End Date</th>
          <td mat-cell *matCellDef="let element">
            <span
              class="main-data"
              *ngIf="element.end_date?.length !== 0; else noData"
            >
              {{ element.end_date | date }}</span
            >
          </td>
        </ng-container> -->

        <!-- Symbol Column -->
        <ng-container matColumnDef="Comments">
          <th mat-header-cell *matHeaderCellDef>Comments</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data"> N.A </span>
          </td>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="Action">
          <th mat-header-cell *matHeaderCellDef>Action</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column" style="padding: 10px">
              <div fxLayout="column" class="action-btn">
                <button
                  class="view-batch filled-btn-primary"
                  fxLayoutAlign="center center"
                  mat-button
                  color="primary"
                  fxFlexAlign="center"
                  [routerLink]="['/home/<USER>']"
                  [queryParams]="{
                    sub: SubscriptionID,
                    supplier_code: element.supplier_code,
                    supplier_name: element.supplier_name,
                    from: 'supplier'
                  }"
                >
                  View Batches
                </button>

                <!-- <button class="pause-btn" fxLayoutAlign="center center" mat-button fxFlexAlign="center">
                  Pause
                </button> -->
              </div>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <!-- progress spinner -->
      <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="false">
        <mat-spinner
          fxLayoutAlign="center center"
          diameter="60"
          strokeWidth="3"
        ></mat-spinner>
      </div>
      <!-- progress spinner -->
      <div
        class="loading-spinner"
        fxLayoutAlign="center center"
        *ngIf="tableLoading"
      >
        <mat-spinner
          fxLayoutAlign="center center"
          diameter="60"
          strokeWidth="3"
        ></mat-spinner>
      </div>
      <ng-template #noData>N.A</ng-template>
      <!-- no data section -->
      <div
        class="no-data"
        *ngIf="supplierInfo?.length == 0 && !tableLoading"
        fxLayout="row"
        fxLayoutGap="10px"
      >
        <mat-icon fontSet="material-icons-outlined">info</mat-icon>
        <span>Nothing to display.</span>
      </div>
      <!-- paginator -->
      <div
        class="custom-paginator"
        fxLayout="row"
        fxLayoutAlign="space-between center"
      >
        <div
          class="jump-to-page"
          fxLayout="row"
          fxLayoutGap="10px"
          fxLayoutAlign="center center"
          *ngIf="supplierInfo?.length > 0 && !tableLoading"
        >
          <span> Page</span>
          <input
            min="1"
            [max]="total_pages"
            type="number"
            (keydown.enter)="goToPage($event.target.value)"
            [value]="page"
          />
          <span>of</span>
          <span>{{ total_pages }}</span>
        </div>
        <mat-paginator
          #paginator
          [pageSizeOptions]="[10, 20, 50, 100]"
          showFirstLastButtons
          [length]="totalItems"
          [pageSize]="size"
          [pageIndex]="page - 1"
          showFirstLastButtons
          (page)="onPaginationEvent($event)"
          *ngIf="supplierInfo?.length > 0 && !tableLoading"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>

<!-- Add vendor panel template -->
<ng-template #panelContent>
  <div class="add-vendor-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <p class="panel-header">Add Vendor</p>
      <img
        style="cursor: pointer"
        class="batch-stop-icon"
        src="assets/images/home-icons/close.svg"
        (click)="closeSidepanel()"
      />
    </div>

    <form [formGroup]="vendorForm" class="vendor-form" fxLayout="column">
      <p>Registered Name</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter vendor name"
          formControlName="registeredName"
        />
        <mat-error
          *ngIf="vendorForm.get('registeredName')?.errors?.['required'] && vendorForm.get('registeredName')?.touched"
        >
          Registered name is required
        </mat-error>
      </mat-form-field>

      <p>Display Name</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter display name"
          formControlName="displayName"
        />
        <mat-error
          *ngIf="vendorForm.get('displayName')?.errors?.['required'] && vendorForm.get('displayName')?.touched"
        >
          Display name is required
        </mat-error>
      </mat-form-field>

      <p>Code</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter vendor code"
          formControlName="code"
        />
        <mat-error
          *ngIf="vendorForm.get('code')?.errors?.['required'] && vendorForm.get('code')?.touched"
        >
          Code is required
        </mat-error>
      </mat-form-field>

      <p>NID Pub</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter NID Public ID"
          formControlName="nidPub"
        />
        <mat-error
          *ngIf="vendorForm.get('nidPub')?.errors?.['required'] && vendorForm.get('nidPub')?.touched"
        >
          NID Public ID is required
        </mat-error>
      </mat-form-field>

      <p>Additional Codes</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="Enter additional code"
          formControlName="additionalCodes"
        />
      </mat-form-field>

      <p>Domain (Optional)</p>
      <mat-form-field appearance="outline">
        <input
          matInput
          placeholder="https://example.com"
          formControlName="domain"
        />
        <mat-error
          *ngIf="
            vendorForm.get('domain')?.errors &&
            vendorForm.get('domain')?.touched
          "
        >
          {{ getDomainErrorMessage() }}
        </mat-error>
        <mat-hint>Enter domain starting with https://</mat-hint>
      </mat-form-field>

      <p>Invite Users (Optional)</p>
      <div
        formArrayName="invited_email"
        class="email-fields"
        fxLayout="column"
        fxLayoutGap="10px"
      >
        <div
          *ngFor="let emailGroup of emailsFormArray.controls; let i = index"
          [formGroupName]="i"
          fxLayout="row"
          fxLayoutGap="10px"
          fxLayoutAlign="space-around center"
        >
          <mat-form-field appearance="outline" fxFlex>
            <input
              matInput
              placeholder="Enter email address"
              formControlName="email"
            />
            <mat-error
              *ngIf="
                emailGroup.get('email')?.errors &&
                emailGroup.get('email')?.touched
              "
            >
              {{ getEmailErrorMessage(i) }}
            </mat-error>
          </mat-form-field>
          <button
            mat-icon-button
            color="warn"
            (click)="removeEmailField(i)"
            *ngIf="emailsFormArray.length > 1"
            type="button"
            matTooltip="Remove email field"
          >
            <mat-icon>remove_circle</mat-icon>
          </button>
        </div>
        <div fxLayout="row" fxLayoutAlign="end center" fxFlex="100">
          <button
            mat-button
            color="primary"
            (click)="addEmailField()"
            [disabled]="emailsFormArray.length >= MAX_EMAILS"
            type="button"
            fxLayoutAlign="start center"
          >
            <mat-icon>add</mat-icon>
            <span style="margin-left: 8px">Add Email</span>
          </button>
        </div>
      </div>

      <button
        mat-raised-button
        class="filled-btn-primary"
        style="margin-top: 50px"
        fxLayout
        fxLayoutAlign="center center"
        (click)="onSubmit()"
        type="submit"
        [disabled]="!vendorForm.valid"
        [matTooltip]="
          !vendorForm.valid
            ? 'Please fill all required fields correctly'
            : 'Add new vendor'
        "
      >
        Add Vendor
      </button>
    </form>
  </div>
</ng-template>

<app-side-panel
  [sidenavTemplateRef]="panelContent"
  [direction]="'right'"
  [navWidth]="380"
  [duration]="0.5"
  [panelId]="'addVendorPanel'"
>
</app-side-panel>
