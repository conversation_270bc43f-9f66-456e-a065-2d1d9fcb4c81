import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Auth0Service } from 'src/app/service/auth0.service';
import { AuthService } from '@auth0/auth0-angular';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token;
  queryParam;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.queryParam = params;
      // check if authenticated else redirect to '/' for authentication
      // this.auth.isAuthenticated$.subscribe({
      //   next: (res) => {
      //     if (res == false) {
      //       this.router.navigate(['/']);
      //     }
      //   },
      // });
    });
  }
}
