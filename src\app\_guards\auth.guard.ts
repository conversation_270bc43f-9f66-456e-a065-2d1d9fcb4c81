import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '@auth0/auth0-angular';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Auth0Service } from '../service/auth0.service';
import { UserService } from '../service/user.service';
import { environment } from 'src/environments/environment';
import { SnackbarService } from '../service/snackbar.service';

@Injectable({
  providedIn: 'root',
})
export class AppAuthGuard {
  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    public auth: AuthService,
    public auth0: Auth0Service,
    // public permissionService: PermissionsService,
    public userService: UserService,
    public snackbarService: SnackbarService
  ) {}
  subscriptionId;
  user;
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    this.user = JSON.parse(localStorage.getItem('user'));
    this.subscriptionId = route.queryParams.sub;
    // proceed only if subs id present
    if (this.subscriptionId) {
      localStorage.setItem('SubscriptionID', this.subscriptionId);
      return new Promise((resolve, reject) => {
        // check if user permissions are already available
        if (!this.userService.appPermissions) {
          // get app permissions
          this.userService
            .getAppPermissions(this.subscriptionId)
            .toPromise()
            .then((resp) => {
              // get url tree and append subs id
              const urlTree = this.router.parseUrl(state.url);
              urlTree.queryParams['sub'] = this.subscriptionId;
              resolve(true);
              return urlTree;
            })
            .catch((err) => {
              // console.log('User logged out');
              this.rejectAuthentication('User logged out');
              resolve(err);
            });
        } else {
          resolve(true);
        }
      });
    } else {
      this.snackbarService.openSnackBar(
        `Missing Subscription, Redirecting ...`,
        'OK'
      );
      this.redirectUser();
    }
  }

  /***
   * Reject User Auth
   */
  rejectAuthentication = (errResp) => {
    this.auth0.logUserOut();
    this.snackbarService.openSnackBar(errResp, 'OK');
  };

  /**
   * Redirect user when no module subscription found
   */
  redirectUser = () => {
    this.user = JSON.parse(localStorage.getItem('user'));
    if (this.user.return_to) {
      // console.log(this.user.return_to);
      window.location.href = 'https://' + this.user.return_to;
    } else {
      window.location.href = environment.default_return_url;
    }
  };
}
