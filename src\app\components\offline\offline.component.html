<div fxFlex="100" class="loading-wrapper" fxLayoutAlign="center center">
  <div fxLayout="column" fxLayoutAlign="center center" >
    <!-- <div fxLayout="row" fxLayoutAlign="center center" class="product-icon">
        <img src="assets/images/dataX-logo.svg" class="svg_icon" />
    </div> -->
    <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="10px">
      <mat-icon>wifi_off</mat-icon>
      <span>You are currently offline</span>
      <!-- <p> Please, connect to the network</p> -->
    </div>
  </div>
</div>