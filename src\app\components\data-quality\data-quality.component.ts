import { Component, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import moment from 'moment';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { HomeService } from '../../service/home.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Params } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { DataQualityService } from '../../service/data-quality.service';
import { SnackbarService } from '../../service/snackbar.service';
export interface dataQuality {
  Global_Attributes: string;
  Fill_Rate: string;
}

@Component({
  selector: 'app-data-quality',
  templateUrl: './data-quality.component.html',
  styleUrls: ['./data-quality.component.scss'],
})
export class DataQualityComponent implements OnInit {
  displayedColumns: string[] = ['Global_Attributes', 'Fill_Rate'];
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: Date;
  customStartDate: Date;
  maxDate: Date;
  minDate: Date;
  selected = 'recent';
  start_date;
  end_date;
  page: number;
  size: number;
  dataQualityStats;
  dataQualityDataSource;
  tableLoading: boolean = false;
  dataLoading: boolean = false;
  average_fill_rate;
  average_resolution;
  average_title_length;
  SubscriptionID;
  search;
  searchedMultipleVals: string[] = [];
  searchedMultipleBatch: string[] = [];
  selectedSupplier;
  selectedBatch;
  supplier;
  batch;
  filterList: any;
  batchfilterList: any;
  cardResp;
  cardList = [
    {
      name: 'Average Fill Rate',
      value: 'average_fill_rate',
      description:
        "Weighted average fill rate of global attributes(Weight has been calculated according to the number of SKU's in each batch)",
    },
    {
      name: '% of SKUs with Image Size >= 500*500',
      value: 'average_resolution',
      description: "Percentage of SKU's with image resolution >= 500px x 500px",
    },
    {
      name: ' % of SKUs with Title Length >= 80 Characters',
      value: 'average_title_length',
      description: "Percentage of SKU's with title length >= 80 characters",
    },
  ];

  @ViewChild(MatPaginator) paginator: MatPaginator;

  constructor(
    private activatedRoute: ActivatedRoute,
    private homeService: HomeService,
    public matSnackbar: MatSnackBar,
    private dataqualityService: DataQualityService,
    private snackbarService: SnackbarService
  ) {}

  ngOnInit() {
    this.dataLoading = true;
    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });
    const d = new Date();
    this.search = '';
    this.start_date = '';
    this.end_date = '';
    this.supplier = '';
    this.batch = '';
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentyear = new Date().getFullYear();
    this.minDate = new Date(currentyear - 20, 0, 1);
    this.maxDate = new Date();
    this.selected = 'recent';
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];
    this.getSupplierList(this.SubscriptionID);
    this.getBatchlist(this.SubscriptionID, this.supplier);
    this.getDataQualityTable(
      this.search,
      this.start_date,
      this.end_date,
      this.supplier,
      this.batch,
      this.SubscriptionID
    );
  }

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = null), (this.customEndDate = null);
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }

    this.page = 1;
    this.size = 20;
    this.dataQualityDataSource = [];
    this.tableLoading = true;
    this.getDataQualityTable(
      this.search,
      this.start_date,
      this.end_date,
      this.supplier,
      this.batch,
      this.SubscriptionID
    );
  };

  sd;
  ed;

  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (dateRangeStart, dateRangeEnd) => {
    if (moment(dateRangeStart).isValid() && moment(dateRangeEnd).isValid()) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart).format('YYYY-MM-DD');
      this.ed = moment(dateRangeEnd).format('YYYY-MM-DD');
      this.page = 1;
      this.size = 20;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
      this.dataQualityDataSource = [];
      this.tableLoading = true;
      this.getDataQualityTable(
        this.search,
        this.start_date,
        this.end_date,
        this.supplier,
        this.batch,
        this.SubscriptionID
      );
    }
  };

  /**
   * supplier filter data
   */
  getProductSelection = () => {
    this.dataLoading = true;
    this.searchedMultipleVals = [];
    this.selectedSupplier.forEach((element) => {
      this.searchedMultipleVals.push(element.supplier_code);
    });
    this.supplier = this.searchedMultipleVals;
    this.getBatchlist(this.SubscriptionID, this.supplier);
    this.getDataQualityTable(
      this.search,
      this.start_date,
      this.end_date,
      this.supplier,
      this.batch,
      this.SubscriptionID
    );
  };

  /**
   * batch filter data
   */
  getBatchSelection = () => {
    this.dataLoading = true;
    this.searchedMultipleBatch = [];
    this.selectedBatch.forEach((element) => {
      this.searchedMultipleBatch.push(element);
    });
    this.batch = this.searchedMultipleBatch;
    this.getDataQualityTable(
      this.search,
      this.start_date,
      this.end_date,
      this.supplier,
      this.batch,
      this.SubscriptionID
    );
  };

  /**
   * reset btn
   */
  reset = () => {
    this.search = '';
    this.supplier = '';
    this.batch = '';
    this.start_date = '';
    this.end_date = '';
    this.customStartDate = null;
    this.customEndDate = null;
    this.selected = 'recent';
    this.selectedSupplier = '';
    this.selectedBatch = '';
    this.dataQualityDataSource = [];
    this.tableLoading = true;
    this.getDataQualityTable(
      this.search,
      this.start_date,
      this.end_date,
      this.supplier,
      this.batch,
      this.SubscriptionID
    );
  };

  /**
   * Supplier filter list
   * @param subscription_id
   */
  getSupplierList = (subscription_id) => {
    this.homeService.getSupplierList(subscription_id).subscribe({
      next: (resp) => {
        this.filterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
       this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * Supplier filter list
   * @param subscription_id
   */
  getBatchlist = (subscription_id, supplier) => {
    this.dataqualityService
      .getBatchListFilter(subscription_id, this.supplier)
      .subscribe({
        next: (resp) => {
          this.batchfilterList = resp.result;
        },
        error: (HttpResponse: HttpErrorResponse) => {
              this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
        },
      });
  };

  /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param start_date
   * @param end_date
   * @param supplier
   * @param subscription_id
   */
  getDataQualityTable = (
    search,
    start_date,
    end_date,
    supplier,
    batch,
    subscription_id
  ) => {
    // this.dataload = true;
    this.dataqualityService
      .getdataQualitystats(
        search,
        start_date,
        end_date,
        supplier,
        batch,
        subscription_id
      )
      .subscribe({
        next: (resp) => {
          this.dataQualityStats = resp.result.global_attr_list;
          const HOME_DATA: dataQuality[] = this.dataQualityStats;
          this.cardResp = resp.result;
          this.dataQualityDataSource = new MatTableDataSource<dataQuality>(
            HOME_DATA
          );
          this.tableLoading = false;
          this.dataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', {
            duration: 3000,
          });
        },
      });
  };
}
