<div class="wrapper" fxLayout="column">
  <div class="filter-container" fxLayout="row" fxLayoutGap="20px" fxFlex="100">
    <!-- home page header -->
    <!-- search box -->
    <mat-form-field appearance="none" class="search-filter">
      <input id="search" matInput placeholder="Search by Supplier name or Code.." #searchVal name="searchVal"
        [(ngModel)]="search" (keydown.enter)="getSearchValue(search)" />
      <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)">search</mat-icon>
      <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
    </mat-form-field>

    <!-- supplier filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select placeholder="All Suppliers" [(ngModel)]="selectedSupplier" (ngModelChange)="getProductSelection()"
        name="selectedProducts" multiple>
        <mat-option #mulVal *ngFor="let mulVals of filterList" [value]="mulVals">{{ mulVals.supplier_name }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- date range dropdown-->
    <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
      <mat-form-field appearance="none" class="date-range-filter">
        <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
          <div *ngFor="let option of datePickerOptions">
            <!-- Recent -->
            <mat-option *ngIf="option.value === 'recent'" value="recent">
              <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                <span> Recent</span> &nbsp;
                <span class="date-range"> </span>
              </div>
            </mat-option>
            <!-- Last Week / Month / Quarter -->
            <mat-option [value]="option.value" *ngIf="
                option.value !== 'recent' && option.value !== 'custom_range'
              ">
              <div fxLayout="column" class="range-category">
                {{ option.display }}
                <span class="date-range">
                  {{ option.start_date | date: "mediumDate" }} -
                  {{ currentDate | date: "mediumDate" }}</span>
              </div>
            </mat-option>
            <!-- Custom range  -->
            <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()">
              <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                <span>Custom Range</span>
                <span class="date-range" *ngIf="customStartDate && customEndDate">
                  {{ customStartDate | date: "mediumDate" }} -
                  {{ customEndDate | date: "mediumDate" }}</span>
                <span fxLayout style="margin: 0 0 0 8px">
                  <mat-date-range-input [rangePicker]="picker" [max]="maxDate" style="display: none">
                    <input matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                    &nbsp;
                    <input matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                        dateRangeChange(customStartDate, customEndDate)
                      " />
                  </mat-date-range-input>
                  <!-- date picker -->
                  <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                  <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                </span>
              </div>
            </mat-option>
          </div>
        </mat-select>
        <div class="date-range-icon">
          <img src="assets/images/home-icons/calendar.svg" />
        </div>
      </mat-form-field>
    </div>
    <button mat-button class="reset-btn filled-btn-primary" fxLayout fxLayoutAlign="center center" (click)="reset()">
      Reset
    </button>
    <div class="messages-icon" fxFlex fxLayoutAlign="start center">
      <!-- <div class="dot"></div> -->
      <img [routerLink]="['/comments']" [queryParams]="{ sub: SubscriptionID, origin: '/home' }"
        src="assets/images/home-icons/message.svg" matTooltip="Comments" matTooltipPosition="above" />
    </div>
    <!-- searched queries chip list -->
    <!-- <div class="search-chip"> 
        <mat-chip
          *ngFor="let item of searchedItems"
          [selectable]="selectable"
          [removable]="removable"
          (removed)="removeQuery(item)"
          [matTooltip]="item.is_batch ? item.progress +'%': null"
          [matTooltipPosition]="'above'"
        >
          {{ item.value }}
          <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
        </mat-chip>
      </div> -->
  </div>
  <!-- data table -->
  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <!-- table -->

    <div class="stats-container" fxLayout="row" fxLayoutAlign=" center" fxLayoutGap="20px">
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_suppliers }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip=" Number of suppliers with at least one completed batch" matTooltipPosition="above" />
          </div>
          <span class="card-description"> Total Suppliers </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_batches }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg" matTooltip="Number of batches processed"
              matTooltipPosition="above" />
          </div>
          <span class="card-description"> Total Completed Batches </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of SKUs processed across all batches" matTooltipPosition="above" />
          </div>
          <span class="card-description"> Total SKU's processed </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_new_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of new SKUs which doesn't exist in the retailer PIM" matTooltipPosition="above" />
          </div>
          <span class="card-description"> Total New SKUs </span>
        </div>
      </mat-card>
      <mat-card class="flex-grow">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{ total_error_sku }}
            </span>
            <img src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="Number of processed SKUs which have errors" matTooltipPosition="above" />
          </div>
          <span class="card-description"> Total Error SKUs </span>
        </div>
      </mat-card>
    </div>

    <div class="table-section">
      <table mat-table [dataSource]="supplierDataSource">
        <!-- Supplier Name Column -->
        <ng-container matColumnDef="supplier">
          <th mat-header-cell *matHeaderCellDef>Supplier</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span *ngIf="
                  element.supplier_code?.length !== 0 ||
                    element.supplier_name?.length !== 0;
                  else noData
                " class="supplier-name text-theme-primary" [routerLink]="['/home/<USER>']" [queryParams]="{
                  sub: SubscriptionID,
                  supplier_code: element.supplier_code,
                  supplier_name: element.supplier_name,
                  from: 'home'
                }">
                {{ element.supplier_name }}</span>
              <span class="sub_text">{{ element.supplier_code }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Brand Column -->
        <ng-container matColumnDef="brand">
          <th mat-header-cell *matHeaderCellDef>Brand</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngFor="
                  let brand_list of element.brands | slice: 0:1;
                  let i = index
                " [matTooltip]="brand_list.length > 12 ? brand_list : null">
                {{ brand_list | truncate: 12 }}</span>
              <span *ngIf="element.brands?.length > 0 " class="more sub-text-id text-theme-primary" (click)="
                  viewAllBrandsPanelToggle(
                    element.brands,
                    element.supplier_name
                  )
                ">
                + {{ element.brands?.length - 1 }} more</span>
            </div>
          </td>
        </ng-container>

        <!-- Batches Column -->
        <ng-container matColumnDef="batches">
          <th mat-header-cell *matHeaderCellDef>Batches</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main_data"> {{ element.batch_count }}</span>
              <span class="sub-text-id text-theme-primary" *ngIf="element.latest_batches?.length !== 0; else noData"
                [routerLink]="['/home/<USER>/batch-activities']" [queryParams]="{
                  sub: SubscriptionID,
                  supplier_code: element.supplier_code,
                  supplier_name: element.supplier_name,
                  batch_id: element.latest_batches,
                  from: '/home'
                }">({{ element.latest_batches.join(", ") }})</span>
            </div>
          </td>
        </ng-container>

        <!-- Total Rows column -->
        <ng-container matColumnDef="total_rows">
          <th mat-header-cell *matHeaderCellDef class="right-align">Total Rows</th>
          <td mat-cell *matCellDef="let element" class="right-align">
            <span class="main-data" *ngIf="element.total_rows?.length !== 0; else noData">
              {{ element.total_rows }}</span>
          </td>
        </ng-container>

        <!-- New ROWS -->
        <ng-container matColumnDef="new_rows">
          <th mat-header-cell *matHeaderCellDef class="right-align">New Rows</th>
          <td mat-cell *matCellDef="let element" class="right-align">
            <span class="main-data" *ngIf="element.new_rows?.length !== 0; else noData">
              {{ element.new_rows }}</span>
          </td>
        </ng-container>
        <!-- Maintenance rows -->
        <ng-container matColumnDef="maintenance_rows">
          <th mat-header-cell *matHeaderCellDef class="right-align">Maintenance Rows</th>
          <td mat-cell *matCellDef="let element" class="right-align">
            <span class="main-data" *ngIf="element.maintenance_rows?.length !== 0; else noData">
              {{ element.maintenance_rows }}</span>
          </td>
        </ng-container>

        <!-- Price Changes -->
        <ng-container matColumnDef="price_changes">
          <th mat-header-cell *matHeaderCellDef class="right-align">Price Changes</th>
          <td mat-cell *matCellDef="let element" class="right-align">
            <span class="main-data" *ngIf="element.price_changes?.length !== 0; else noData">
              {{ element.price_changes }}
            </span>
          </td>
        </ng-container>

        <!-- Status -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let element" class="status">
            <span class="main-data" *ngIf="
                element.in_queue?.length ||
                  element.in_progress?.length ||
                  element.approved?.length ||
                  element.cancelled?.length !== 0;
                else noData
              ">
              <ul>
                <li fxLayout="row" fxLayoutAlign="space-between center">
                  <span class="bucket">In Queue</span>
                  <span class="count">{{ element.in_queue }} </span>
                </li>
                <li fxLayout="row" fxLayoutAlign="space-between center">
                  <span class="bucket">Completed</span>
                  <span class="count">{{ element.completed }}</span>
                </li>
                <li fxLayout="row" fxLayoutAlign="space-between center">
                  <span class="bucket">Approved</span>
                  <span class="count">
                    {{ element.approved }}
                  </span>
                </li>
                <li fxLayout="row" fxLayoutAlign="space-between center">
                  <span class="bucket">Cancelled</span><span class="count" fxFlexAlign="end">{{
                    element.cancelled
                  }}</span>
                </li>
                <li fxLayout="row" fxLayoutAlign="space-between center">
                  <span class="bucket">Published</span>
                  <span class="count">
                    {{ element.published }}
                  </span>
                </li>
              </ul>
            </span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <!-- progress spinner -->
      <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableLoading">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>
      <ng-template #noData>N.A</ng-template>
      <!-- no data section -->
      <div class="no-data" *ngIf="supplierStats?.length == 0 && !tableLoading" fxLayout="row" fxLayoutGap="10px">
        <mat-icon fontSet="material-icons-outlined">info</mat-icon>
        <span>Nothing to display.</span>
      </div>
      <!-- paginator -->
      <div class="custom-paginator" fxLayout="row" fxLayoutAlign="space-between center">
        <div class="jump-to-page" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="center center"
          *ngIf="supplierStats?.length > 0 && !tableLoading">
          <span> Page</span>
          <input min="1" [max]="total_pages" type="number" (keydown.enter)="goToPage($event.target.value)"
            [value]="page" />
          <span>of</span>
          <span>{{ total_pages }}</span>
        </div>
        <mat-paginator #paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons [length]="totalItems"
          [pageSize]="size" [pageIndex]="page - 1" showFirstLastButtons (page)="onPaginationEvent($event)"
          *ngIf="supplierStats?.length > 0 && !tableLoading">
        </mat-paginator>
      </div>
    </div>
  </div>
  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
</div>

<!-- upload file side panel -->
<ng-template #panelContent>
  <div *ngIf="viewAllBrands" class="batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between">
      <p class="panel-header">{{ supplier_brand }} Supplier Brands</p>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="closeSidepanel()" />
    </div>

    <div class="brand-container">
      <ul>
        <li *ngFor="let item of brandList" class="brand-list">
          {{ item }}
        </li>
      </ul>
    </div>
  </div>
</ng-template>

<app-side-panel [sidenavTemplateRef]="panelContent" [direction]="'right'" [navWidth]="380" [duration]="0.5">
</app-side-panel>
