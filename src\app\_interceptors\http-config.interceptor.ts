import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpHeaders,
  HttpXsrfTokenExtractor,
  HttpResponse,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { Auth0Service } from '../service/auth0.service';
import { environment } from 'src/environments/environment';

@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {
  constructor(
    private tokenExtractor: HttpXsrfTokenExtractor,
    private router: Router,
    private auth0: Auth0Service
  ) {}
  user;
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Set Credentials (<PERSON>ie)
    request.clone({
      withCredentials: true,
    });

    return next.handle(request).pipe(
      map((response: HttpResponse<any>) => response),
      catchError((error: HttpResponse<HttpErrorResponse>) => {
        if (error.status === 403 || error.status === 401) {
          this.user = JSON.parse(localStorage.getItem('user'));
          this.auth0.logUserOut();
          localStorage.clear();
        }
        return throwError(error);
      })
    );
  }
}
