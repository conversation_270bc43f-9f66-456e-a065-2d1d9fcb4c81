import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, map, catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HelpService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * sales mail
   * @param subscription_id
   * @param email
   * @param message
   * @returns
   */
  contactSales = (subscription_id, email, message): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin('help', 'contactSales');
    return this.http
      .post(createLabelEndpoint + subscription_id + '/contact_sales/', {
        email: email,
        message: message,
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * support mail
   * @param subscription_id
   * @param email
   * @param message
   * @returns
   */
  contactSupport = (subscription_id, email, message): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin('help', 'contactSupport');
    return this.http
      .post(createLabelEndpoint + subscription_id + '/contact_support/', {
        email: email,
        message: message,
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
