@import "../../../styles/variables";

.supplier-name {
  padding-top: 5px;
  font-family: $site-font;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  text-decoration-line: underline;
  cursor: pointer;
}

.sub-text-id {
  cursor: pointer;
  padding-top: 5px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 18px;
  text-decoration: underline;
  width: 120px;
  word-break: break-word;
}

.right-align {
  text-align: right;
  padding-right: 40px;
}
.align-right{
  text-align: center;
}

.more {
  cursor: pointer;
  margin-bottom: 5px;
}

.data-loading-spinner {
  height: 500px;
}

.stats-container {
  mat-card {
    background: $theme-white;
    box-shadow: 3px 3px 6px 1px rgba(59, 62, 72, 0.02);
    border-radius: 4px;
    height: 97px;
    width: 100%;

    .card-head {
      font-family: $site-font;
      font-style: normal;
      font-weight: bold;
      font-size: 27px;
      line-height: 40px;
      color: $theme-black;
 
    }
    img{
      cursor: pointer;
    }

    .card-sub-head {
      margin-top: 2px;
      padding: 10px;
    }

    .card-actions {
      img {
        height: 40%;
        cursor: pointer;
      }
    }
  }
}

ul {
  padding-left: 0;
  width: 140px;

  li {
    // text-align: right !important;
    padding-right: 35px;

    .count {
      padding-left: 10px;
    }
  }
}
