<div class="wrapper wrapper-data-quality" fxLayout="column">
  <div class="filter-container data-quality-filter" fxLayout="row" fxLayoutGap="20px" fxFlex="100">
    <!-- home page header -->

    <!-- date range dropdown-->
    <div class="btn-container" fxLayout="row" fxLayoutGap="10px">
      <mat-form-field appearance="none" class="date-range-filter">
        <mat-select [(value)]="selected" (selectionChange)="getDataForDate($event.value)">
          <div *ngFor="let option of datePickerOptions">
            <!-- Recent -->
            <mat-option *ngIf="option.value === 'recent'" value="recent">
              <div fxLayout="column" class="range-category" *ngIf="option.value === 'recent'" fxLayout="row">
                <span> Recent</span> &nbsp;
                <span class="date-range"> </span>
              </div>
            </mat-option>
            <!-- Last Week / Month / Quarter -->
            <mat-option [value]="option.value" *ngIf="
                option.value !== 'recent' && option.value !== 'custom_range'
              ">
              <div fxLayout="column" class="range-category">
                {{ option.display }}
                <span class="date-range">
                  {{ option.start_date | date: "mediumDate" }} -
                  {{ currentDate | date: "mediumDate" }}</span>
              </div>
            </mat-option>
            <!-- Custom range  -->
            <mat-option *ngIf="option.value === 'custom_range'" value="custom_range" (click)="picker.open()">
              <div fxLayout="row" class="range-category" fxLayoutAlign="start center">
                <span>Custom Range</span>
                <span class="date-range" *ngIf="customStartDate && customEndDate">
                  {{ customStartDate | date: "mediumDate" }} -
                  {{ customEndDate | date: "mediumDate" }}</span>
                <span fxLayout style="margin: 0 0 0 8px">
                  <mat-date-range-input [rangePicker]="picker" [min]="minDate" [max]="maxDate" style="display: none">
                    <input matStartDate #dateRangeStart [(ngModel)]="customStartDate" />
                    &nbsp;
                    <input matEndDate #dateRangeEnd [(ngModel)]="customEndDate" (dateChange)="
                        dateRangeChange(customStartDate, customEndDate)
                      " />
                  </mat-date-range-input>
                  <!-- date picker -->
                  <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                  <mat-date-range-picker class="custom-date-icon" #picker></mat-date-range-picker>
                </span>
              </div>
            </mat-option>
          </div>
        </mat-select>
        <div class="date-range-icon">
          <img src="assets/images/home-icons/calendar.svg" />
        </div>
      </mat-form-field>
    </div>

    <!-- All batch dropdown -->
    <mat-form-field appearance="none" class="app-dropdown flex-grow">
      <mat-select placeholder="All Suppliers" [(ngModel)]="selectedSupplier" (ngModelChange)="getProductSelection()"
        name="selectedProducts" multiple>
        <mat-option #mulVal *ngFor="let mulVals of filterList" [value]="mulVals">{{ mulVals.supplier_name }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- supplier filter -->
    <mat-form-field appearance="none" class="app-dropdown flex-grow">
      <mat-select placeholder="All batches" [(ngModel)]="selectedBatch" (ngModelChange)="getBatchSelection()"
        name="selectedBatch" multiple>
        <mat-option #mulVal *ngFor="let batch of batchfilterList" [value]="batch">{{ batch }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <button mat-button class="reset-btn filled-btn-primary" fxLayout fxLayoutAlign="center center" (click)="reset()">
      Reset
    </button>
  </div>
  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
  <!-- data quality page content -->
  <div class="table-wrapper" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <!-- progress spinner -->
    <!-- table -->
    <div class="stats-container" fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="20px">
      <mat-card class="flex-grow" *ngFor="let card of cardList" fxFlex="33">
        <div class="stat-card-content" fxFlex="100" fxLayout="column" fxLayoutAlign="space-between start">
          <div fxLayout="row" fxLayoutAlign="space-between center" style="width: 100%">
            <span class="card-head">
              {{cardResp [card.value] | roundOff}}
            </span>
            <!-- <mat-icon fontSet="material-icons-outlined">info</mat-icon> -->
            <img
              src="../../../assets/images/data-quality/Info-circle.svg"
              matTooltip="{{ card.description }}"
              matTooltipPosition="above"
            />
          </div>
          <span class="card-description">
           {{card.name}}
          </span>
        </div>
      </mat-card>
    </div>


    <div>
      <table mat-table [dataSource]="dataQualityDataSource">
        <!-- Position Column -->
        <ng-container matColumnDef="Global_Attributes">
          <th mat-header-cell *matHeaderCellDef>Global Attributes</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngIf="element.Global_Attributes?.length !== 0; else noData">
                {{ element.Global_Attributes }}</span>
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="Fill_Rate">
          <th mat-header-cell *matHeaderCellDef>Fill Rate</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngIf="element.Fill_Rate?.length !== 0; else noData">
                {{ element.Fill_Rate | roundOff}}</span>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <ng-template #noData>N.A</ng-template>
      <!-- no data section -->
      <div class="no-data" *ngIf="dataQualityStats?.length == 0 && !tableLoading" fxLayout="row" fxLayoutGap="10px">
        <mat-icon fontSet="material-icons-outlined">info</mat-icon>
        <span>Nothing to display.</span>
      </div>
    </div>
  </div>
  <!-- progress spinner -->
  <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
  </div>
</div>
