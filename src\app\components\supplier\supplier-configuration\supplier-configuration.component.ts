import { <PERSON>mponent, OnInit, ViewChild, <PERSON>ement<PERSON>ef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { SidePanelService } from '../../../service/side-panel.service';
import { FormBuilder, FormGroup, Validators, FormControl, AbstractControl, ValidationErrors, FormArray, ValidatorFn } from '@angular/forms';
import { FileUploadService } from '../../../service/file-upload.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subscription } from 'rxjs';
import { SelectionModel } from '@angular/cdk/collections';
import { SnackbarService } from '../../../service/snackbar.service';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '../../../_dialog/confirm-dialog/confirm-dialog.component';

interface ConfirmDialogData {
  title: string;
  message: string;
}

@Component({
  selector: 'app-supplier-configuration',
  templateUrl: './supplier-configuration.component.html',
  styleUrls: ['./supplier-configuration.component.scss']
})
export class SupplierConfigurationComponent implements OnInit, OnDestroy {
  selectedTabIndex = 0;
  searchVendorConfigurationFile: string = '';
  showUploadPanel = true;
  
  // File upload related properties
  fileUploadFormForVendor = this.fb.group({
    channels: [[] as string[], [Validators.required, SupplierConfigurationComponent.validateChannels]],
    description: ['', Validators.maxLength(2000)]
  });
  files: any[] = [];
  isFileServiceBusy = false;
  acceptedFileTypes = "text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/wps-office.xlsx, application/JSON, application/zip, image/*, .xls, .xlsx, .csv, .pdf, .zip";
  
  // Table view for files tab
  fileDataSource: any[] = [];
  displayedFileColumns: string[] = ['select', 'fileName', 'type', 'status', 'uploadedBy', 'uploadedOn', 'inputChannel', 'actions'];
  currentFilePage: number = 1;
  totalFilePages: number = 1;
  totalFileItems: number = 0;
  filePageSize: number = 10;
  @ViewChild('filePaginator') filePaginator: any;
  tableLoading: boolean = false;
  
  // User tab related properties
  searchUser: string = '';
  usersDataSource: any[] = [];
  displayedUserColumns: string[] = ['select', 'name', 'mailId', 'joinedOn', 'batches', 'lastLogin', 'status'];
  userSelectionModel = new SelectionModel<any>(true, []);
  currentUserPage: number = 1;
  totalUserPages: number = 1;
  totalUserItems: number = 0;
  userPageSize: number = 10;
  @ViewChild('userPaginator') userPaginator: any;
  
  // Invite users panel properties
  inviteEmails: string[] = [''];
  
  // Invite users form
  inviteUsersForm = this.fb.group({
    emails: this.fb.array([this.createEmailControl()])
  });
  
  // Form data
  supplier_code: string;
  supplier_name: string;
  subscriptionId: string;
  
  // Input and output formats
  inputFormat: string[] = ['CSV', 'XLSX', 'XML', 'JSON'];
  selectedInputFormat: string = '';
  
  // Channel options
  channelOptions: string[] = ['Upload', 'FTP', 'Email', 'API', 'S3', 'Database', 'Pull API', 'Push API'];
  selectedChannels: string[] = [];
  
  // Random filenames for labels
  randomFileLabels: { [key: string]: string } = {};
  
  // Track selected sample files for each channel type
  channelSampleFiles: { [key: string]: string[] } = {
    'Upload': [],
    'Email': [],
    'FTP': [],
    'Database': [],
    'Pull API': [],
    'Push API': []
  };
  
  // Currently active channel for file selection
  activeChannelForFileSelection: string = '';
  
  // Refs for file inputs
  @ViewChild('mainFileInput') mainFileInput: ElementRef;
  
  // Subscriptions for cleanup
  private subscriptions: Subscription[] = [];

  // Add properties for tracking multiple file uploads
  currentFileIndex = 0;
  uploadedFilePaths: string[] = [];
  showTableViewForFileTab: boolean = false;

  // Templates mapping for download functionality
  private templateMap: { [key: string]: string } = {
    'Upload': '.xlsx',
    'Email': '.xlsx',
    'FTP': '.xlsx',
    'Push API': '.xlsx',
    'Pull API': '.zip',
    'Database': '.xlsx',
    'Email (Alternative)': '.xlsx'
  };

  // Getter methods for form controls
  get channelsControl() { return this.fileUploadFormForVendor.get('channels'); }
  get descriptionControl() { return this.fileUploadFormForVendor.get('description'); }

  // Add new properties for file selection
  fileSelectionModel = new SelectionModel<any>(true, []);

  // Getter method for email form controls
  get emailControls() {
    return (this.inviteUsersForm.get('emails') as FormArray).controls;
  }

  // Channel tab related properties
  selectedChannel: string = 'Upload';
  channelStatus: { [key: string]: boolean } = {
    'Upload': true,
    'Email': true,
    'FTP': false,
    'Pull API': false,
    'Push API': false,
    'Database': false,
    'XYZ': false
  };
  selectedChannelView: string = 'input-files';
  ftpConfiguring: boolean = false;
  pullApiConfiguring: boolean = false;
  pushApiConfiguring: boolean = false;
  databaseConfiguring: boolean = false;
  xyzAccessDenied: boolean = true;
  
  // Database channel files
  hasDatabaseFiles: boolean = false;
  databaseFiles: {name: string, type: string}[] = [];

  // Push API files
  pushApiFiles: string[] = [];

  // Vendor edit form
  vendorEditForm: FormGroup;

  constructor(
    private sidePanelService: SidePanelService,
    private fb: FormBuilder,
    private fileUploadService: FileUploadService,
    private snackBar: MatSnackBar,
    private snackbarService: SnackbarService,
    private dialog: MatDialog
  ) {
    this.initVendorEditForm();
  }

  ngOnInit(): void {
    // Set default values - in a real app, these would come from a service or route params
    this.supplier_code = 'SAMPLE_SUPPLIER_CODE';
    this.supplier_name = 'Sample Supplier';
    this.subscriptionId = 'SAMPLE_SUBSCRIPTION_ID';
    
    // Initialize mock data for the file table
    this.initMockFileData();
    
    // Initialize mock data for the users table
    this.initMockUserData();
    
    // Generate random filenames for each channel
    this.generateRandomFileLabels();
    
    // Initialize Push API files from the existing pushApiFiles array
    if (this.pushApiFiles && this.pushApiFiles.length > 0) {
      this.channelSampleFiles['Push API'] = [...this.pushApiFiles];
    }
    
    // Initialize Database files from the existing databaseFiles array
    this.syncDatabaseFilesWithChannelSampleFiles();
  }
  
  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onTabChange(event: any): void {
    this.selectedTabIndex = event.index;
  }

  // User tab methods
  openInviteUserPanel(): void {
    // Close any upload panel first
    this.sidePanelService.setShowNav(false);
    
    // Set state for this panel
    this.showUploadPanel = false;
    
    // Give a small delay to ensure panels don't conflict
    setTimeout(() => {
      // Reset the invite users form
      this.inviteUsersForm = this.fb.group({
        emails: this.fb.array([this.createEmailControl()])
      });
      
      // Open the panel
      this.sidePanelService.setShowNav(true, 'invitePanel');
    }, 100);
  }
  
  closeInviteUserPanel(): void {
    this.sidePanelService.setShowNav(false);
  }
  
  addMoreEmail(): void {
    const emails = this.inviteUsersForm.get('emails') as FormArray;
    emails.push(this.createEmailControl());
  }
  
  removeInviteEmail(index: number): void {
    const emails = this.inviteUsersForm.get('emails') as FormArray;
    if (emails.length > 1) {
      emails.removeAt(index);
    }
  }
  
  canSendInvites(): boolean {
    return this.inviteUsersForm.valid && this.emailControls.length > 0;
  }
  
  sendInvites(): void {
    if (!this.inviteUsersForm.valid) {
      // Mark all fields as touched to display validation errors
      this.emailControls.forEach(control => control.markAsTouched());
      return;
    }
    
    // Get all valid email values from the form
    const validEmails = this.emailControls
      .map(control => control.value)
      .filter(email => email && email.trim().length > 0);
    
    if (validEmails.length === 0) {
      this.showMessage('Please enter at least one valid email address');
      return;
    }
    
    // In a real app,we would call a service to send the invites
    // For now, we'll just simulate this by adding to the mock data
    const newUsers = validEmails.map(email => {
      const userName = `User ${this.usersDataSource.length + 1}`;
      return {
        name: userName,
        mailId: email,
        joinedOn: 'Invite Sent',
        batches: '-',
        lastLogin: '-',
        status: 'Invite Sent'
      };
    });
    
    // Add new users to the top of the list
    this.usersDataSource = [...newUsers, ...this.usersDataSource];
    this.totalUserItems = this.usersDataSource.length;
    this.totalUserPages = Math.ceil(this.totalUserItems / this.userPageSize);
    
    // Close the panel and show success message
    this.closeInviteUserPanel();
    this.showMessage(`Invitations sent to ${validEmails.length} email${validEmails.length > 1 ? 's' : ''}`);
  }
  
  resendInvite(user: any): void {
    // In a real app,wewould call a service to resend the invite
    // For now, just show a message
    this.showMessage(`Invite resent to ${user.mailId}`);
  }

  openVendorDetailsEditPanel(): void {
    // Close any other panels first
    this.sidePanelService.setShowNav(false);
    
    // Populate form with current vendor details
    this.vendorEditForm.patchValue({
      registeredName: 'Leviton', // Replace with actual vendor details
      displayName: 'Leviton',
      vendorType: 'type1',
      code: '3414',
      nidPub: '', // Add actual value
      additionalCodes: '', // Add actual value
      domain: '' // Add actual value
    });
    
    // Reset form state
    this.vendorEditForm.markAsPristine();
    
    // Open the edit panel
    setTimeout(() => {
      this.sidePanelService.setShowNav(true, 'editVendorPanel');
    }, 100);
  }
  
  deleteInvite(user: any): void {
    // In a real app,we would call a service to delete the invite
    // For now, we'll just remove from the mock data
    const index = this.usersDataSource.findIndex(u => u.mailId === user.mailId);
    if (index !== -1) {
      this.usersDataSource.splice(index, 1);
      this.usersDataSource = [...this.usersDataSource]; // Create new array to trigger change detection
      this.totalUserItems = this.usersDataSource.length;
      this.totalUserPages = Math.ceil(this.totalUserItems / this.userPageSize);
      this.showMessage(`Invite for ${user.mailId} deleted`);
    }
  }
  
  formatUserIndex(index: number): string {
    const adjustedIndex = index + 1 + (this.currentUserPage - 1) * this.userPageSize;
    // Pad with leading zero if needed
    return adjustedIndex < 10 ? `0${adjustedIndex}` : `${adjustedIndex}`;
  }
  
  onUserToggle(row: any): void {
    this.userSelectionModel.toggle(row);
  }
  
  toggleAllUsers(): void {
    if (this.areAllUsersSelected()) {
      this.userSelectionModel.clear();
    } else {
      this.usersDataSource.forEach(row => this.userSelectionModel.select(row));
    }
  }
  
  areAllUsersSelected(): boolean {
    const numSelected = this.userSelectionModel.selected.length;
    const numRows = this.usersDataSource.length;
    return numSelected === numRows && numRows > 0;
  }
  
  onUserPageChange(event: any): void {
    this.currentUserPage = event.pageIndex + 1;
    this.userPageSize = event.pageSize;
    // In a real application, this would fetch data from the server with pagination
  }
  
  goToUserPage(pageNumber: number): void {
    pageNumber = Math.max(1, Math.min(pageNumber, this.totalUserPages));
    this.currentUserPage = pageNumber;
    if (this.userPaginator) {
      this.userPaginator.pageIndex = pageNumber - 1;
    }
  }
  
  private initMockUserData(): void {
    // Mock data for the user table
    this.usersDataSource = [
      {
        name: 'User 1',
        mailId: '<EMAIL>',
        joinedOn: 'Mar 02, 2024',
        batches: 20,
        lastLogin: 'Jan 01, 2025 - 14:30',
        status: 'Active'
      },
      {
        name: 'User 2',
        mailId: '<EMAIL>',
        joinedOn: 'Mar 02, 2024',
        batches: 40,
        lastLogin: 'Jan 01, 2025 - 14:30',
        status: 'Active'
      },
      {
        name: 'User 3',
        mailId: '<EMAIL>',
        joinedOn: 'Mar 02, 2024',
        batches: 30,
        lastLogin: 'Jan 01, 2025 - 14:30',
        status: 'Active'
      },
      {
        name: 'User 4',
        mailId: '<EMAIL>',
        joinedOn: 'Invite Expired',
        batches: '-',
        lastLogin: '-',
        status: 'Invite Expired'
      },
      {
        name: 'User 5',
        mailId: '<EMAIL>',
        joinedOn: 'Invite Sent',
        batches: '-',
        lastLogin: '-',
        status: 'Invite Sent'
      }
    ];
    
    this.totalUserItems = this.usersDataSource.length;
    this.totalUserPages = Math.ceil(this.totalUserItems / this.userPageSize);
  }

  // File related methods continue below
  getSearchVendorConfigurationFile(searchTerm: string): void {
    // Implement search logic here
  }

  resetSearchVendorConfigurationFile(): void {
    this.searchVendorConfigurationFile = '';
  }

  openUploadFilesPanel(): void {
    // Close any other panels first
    this.sidePanelService.setShowNav(false);
    
    // Reset form state
    this.files = [];
    this.fileUploadFormForVendor.reset();
    
    // Give a small delay to ensure panels don't conflict
    setTimeout(() => {
      // Set the Upload channel as the default for the Files tab
      this.fileUploadFormForVendor.get('channels').setValue(['Upload']);
      
      // Open the sidepanel with the Files tab panel ID
      this.sidePanelService.setShowNav(true, 'uploadPanel');
    }, 100);
  }

  updateChannel(channel: string, checked: boolean): void {
    console.log(channel, checked);
  }

  openFileUploadPanelForChannel(): void {
    // Check if we're in the Files tab - if so, use the Files tab panel
    if (this.selectedTabIndex === 2) { // Files tab index
      this.openUploadFilesPanel();
      return;
    }
    
    // Close any other panels first
    this.sidePanelService.setShowNav(false);
    
    // Reset form state
    this.files = [];
    this.fileUploadFormForVendor.reset();
    
    // Give a small delay to ensure panels don't conflict
    setTimeout(() => {
      // Make sure to clear channel selection for Database when opening from Database channel
      if (this.selectedChannel === 'Database') {
        // For Database channel, ensure 'Database' is preselected in the upload form
        this.fileUploadFormForVendor.get('channels').setValue(['Database']);
      } else {
        // For other channels, ensure the current channel is preselected
        this.fileUploadFormForVendor.get('channels').setValue([this.selectedChannel]);
      }
      
      // Open the sidepanel with the specific panel ID
      this.sidePanelService.setShowNav(true, 'uploadPanelForChannelView');
    }, 100);
  }

  // Method to specifically close the channel view upload panel
  closeUploadPanelForChannelView(): void {
    this.resetUploadPanel();
    this.sidePanelService.setShowNav(false);
    this.showUploadPanel = true;
  }

  closeUploadFilePanel(): void {
    // Get the current active panel ID
    const currentPanelId = this.sidePanelService.getCurrentActivePanelId();
    
    // Reset panel state
    this.resetUploadPanel();
    
    // Close the panel
    this.sidePanelService.setShowNav(false);
    
    // Restore default state
    this.showUploadPanel = true;
  }
  
  // File upload related methods
  onMainFileSelect(event: any): void {
    const files = event.target.files;
    if (files.length > 0) {
      // Check if file is empty
      if (files[0].size === 0) {
        this.showMessage('File cannot be empty');
        return;
      }
      
      this.files.push(...Array.from(files));
    }
  }
  
  onSelect(event: any): void {
    if (event.addedFiles && event.addedFiles.length > 0) {
      // Check each file
      for (const file of event.addedFiles) {
        // Check if file is empty
        if (file.size === 0) {
          this.showMessage('File cannot be empty');
          continue;
        }
        
        // Check file type
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const allowedExtensions = ['csv', 'xlsx', 'xls', 'xml', 'json', 'zip', 'pdf'];
        
        if (!allowedExtensions.includes(fileExtension)) {
          this.showMessage(`File type .${fileExtension} is not supported`);
          continue;
        }
        
        // Add file to arrays if not already present
        if (!this.files.some(f => f.name === file.name && f.size === file.size)) {
          this.files.push({
            ...file,
            selected: true
          });
        }
      }
      
      // Update form with file information
      this.updateFormFromFiles();
    }
  }
  
  /**
   * Updates the form values based on the selected files
   */
  private updateFormFromFiles(): void {
    // Only update if we have files
    if (this.files.length === 0) {
      return;
    }
    
    // Set Upload as default channel if none selected
    const channelsControl = this.fileUploadFormForVendor.get('channels');
    if (!channelsControl?.value?.length) {
      channelsControl?.setValue(['Upload']);
      this.selectedChannels = ['Upload'];
    }
    
    // Mark form as touched to trigger validation
    this.fileUploadFormForVendor.markAllAsTouched();
  }
  
  onRemove(file: any): void {
    // Remove file from array
    this.files = this.files.filter(f => f !== file);
    
    // Reset form fields if all files are removed
    if (this.files.length === 0) {
      this.fileUploadFormForVendor.get('channels').setValue([]);
    }
  }
  
  /**
   * Removes a channel from the selected channels list
   */
  removeChannel(channel: string): void {
    const channels = this.fileUploadFormForVendor.get('channels').value as string[];
    const index = channels.indexOf(channel);
    
    if (index >= 0) {
      channels.splice(index, 1);
      this.fileUploadFormForVendor.get('channels').setValue(channels);
      this.selectedChannels = channels;
    }
  }

  viewFileDetails(element: any): void { 
    // Close any open panel first
    this.sidePanelService.setShowNav(false);
    
    // Set state for this panel
    this.showUploadPanel = false;
    
    // Give a small delay to ensure panels don't conflict
    setTimeout(() => {
      this.fileUploadService.setSelectedFileDetails(element);
      // Use a different panel ID for file details
      this.sidePanelService.setShowNav(true, 'fileDetailsPanel');
    }, 100);
  }
  
  upload(): void {
    this.isFileServiceBusy = true;
    // Simulate upload process
    setTimeout(() => {
      this.isFileServiceBusy = false;
      this.showUploadPanel = false;
      this.showTableViewForFileTab = true;
      
      // Update database files if Database channel is selected
      const selectedChannels = this.fileUploadFormForVendor.get('channels').value;
      if (selectedChannels.includes('Database')) {
        this.hasDatabaseFiles = true;
        // Add uploaded files to databaseFiles
        this.files.forEach(file => {
          this.databaseFiles.push({
            name: file.name,
            type: file.type
          });
        });
      }
      
      // Reset the form
      this.files = [];
      this.fileUploadFormForVendor.reset();
    }, 2000);
  }
  
  uploadNextFile(): void {
    if (this.currentFileIndex < this.files.length) {
      this.uploadFile(this.files[this.currentFileIndex]);
    } else {
      // All files have been uploaded, complete the process
      this.completeUpload();
    }
  }
  
  uploadFile(file: any): void {
    // Use the SAMPLE_CHANNEL value for demonstration purposes
    const SAMPLE_CHANNEL = 'FILE_UPLOAD';
    
    // Generate a file name based on the file's name
    const fileName = file.name.split('.')[0]; 
    
    // Determine input format from file extension
    const fileExtension = file.name.split('.').pop().toUpperCase();
    let inputFormat = '';
    
    // Set input format based on file extension
    if (this.inputFormat.includes(fileExtension)) {
      inputFormat = fileExtension;
    } else if (fileExtension === 'PDF') {
      // Default to CSV for PDF files
      inputFormat = 'CSV';
    }
    
    const sub = this.fileUploadService.uploadData(
      this.subscriptionId,
      this.supplier_code,
      SAMPLE_CHANNEL,
      fileName,
      inputFormat,
      '', // No output format for configuration files
      file,
      true,
      '',
      this.supplier_name
    ).subscribe({
      next: (progress) => {
        // Store the file path if it exists and upload is complete
        if (progress === 100 && this.fileUploadService.main_file_path) {
          this.uploadedFilePaths.push(this.fileUploadService.main_file_path);
          
          // Move to the next file
          this.currentFileIndex++;
          this.uploadNextFile();
        }
      },
      error: (error) => {
        this.isFileServiceBusy = false;
        this.showMessage('Error uploading file: ' + error.error?.detail || 'Unknown error');
      }
    });
    
    this.subscriptions.push(sub);
  }
  
  completeUpload(): void {
    this.isFileServiceBusy = false;
    this.showTableViewForFileTab = true;
    
    // Get the current active panel ID to determine which close method to use
    const currentPanelId = this.sidePanelService.getCurrentActivePanelId();
    
    // Close the appropriate panel based on the panel ID
    if (currentPanelId === 'uploadPanelForChannelView') {
      this.closeUploadPanelForChannelView();
    } else if (currentPanelId === 'uploadPanel') {
      this.closeUploadFilePanel();
    } else {
      // Fallback if no specific panel ID is found
      this.sidePanelService.setShowNav(false);
    }
    
    this.resetUploadPanel();
    
    // After upload is complete, refresh the file table data
    this.initMockFileData();
    
    // Show success message
    this.showMessage('All files uploaded successfully');
  }
  
  resetUploadPanel(): void {
    // Reset form and file state
    this.files = [];
    this.fileUploadFormForVendor.reset();
    this.isFileServiceBusy = false;
    this.currentFileIndex = 0;
    this.uploadedFilePaths = [];
    
    // Reset file input element if it exists
    if (this.mainFileInput) {
      this.mainFileInput.nativeElement.value = '';
    }
    
    // Clear file upload service data
    this.fileUploadService.allSignedURLs.main = '';
    this.fileUploadService.additionalFileList = [];
  }
  
  showMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }

  // Custom validator for channels
  static validateChannels(control: AbstractControl): ValidationErrors | null {
    const channels = control.value as string[];
    if (!channels || channels.length === 0) {
      return { required: true };
    }
    
    // Ensure all channels are from the allowed options
    const allowedChannels = ['Upload', 'FTP', 'Email', 'API', 'S3'];
    const invalidChannels = channels.filter(channel => !allowedChannels.includes(channel));
    
    if (invalidChannels.length > 0) {
      return { invalidChannels: invalidChannels };
    }
    
    return null;
  }

  /**
   * Initializes mock data for the file table
   */
  private initMockFileData(): void {
    // Create mock data for the file table view
    this.fileDataSource = [
      {
        fileName: 'SampleFile2.zip',
        type: 'Folder',
        status: 'In Review',
        uploadedBy: 'US',
        uploadedOn: 'May 01, 2025 - 14:30',
        inputChannel: ['Upload', 'FTP', 'Email']
      },
      {
        fileName: 'SampleFile1.xlsx',
        type: 'File',
        status: 'In Review',
        uploadedBy: 'US',
        uploadedOn: 'May 01, 2025 - 14:30',
        inputChannel: ['Upload', 'FTP', 'Email']
      },
      {
        fileName: 'IF6.zip',
        type: 'Folder',
        status: 'Setup in Progress',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Upload', 'FTP', 'API']
      },
      {
        fileName: 'IF5.xlsx',
        type: 'File',
        status: 'Active',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Upload']
      },
      {
        fileName: 'IF4.xlsx',
        type: 'Folder',
        status: 'Active',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Upload']
      },
      {
        fileName: 'IF3.xlsx',
        type: 'File',
        status: 'Active',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Upload']
      },
      {
        fileName: 'IF2.xlsx',
        type: 'File',
        status: 'Active',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Upload', 'FTP', 'API']
      },
      {
        fileName: 'IF1.xlsx',
        type: 'File',
        status: 'Active',
        uploadedBy: 'US',
        uploadedOn: 'Jan 01, 2025 - 14:30',
        inputChannel: ['Email']
      }
    ];
    
    this.totalFileItems = this.fileDataSource.length;
    this.totalFilePages = Math.ceil(this.totalFileItems / this.filePageSize);
  }
  
  /**
   * Handles pagination events for the file table
   */
  onFilePageChange(event: any): void {
    this.currentFilePage = event.pageIndex + 1;
    this.filePageSize = event.pageSize;
    // In a real application, this would fetch data from the server with pagination
  }
  
  /**
   * Navigates to a specific page in the file table
   */
  goToFilePage(pageNumber: number): void {
    pageNumber = Math.max(1, Math.min(pageNumber, this.totalFilePages));
    this.currentFilePage = pageNumber;
    if (this.filePaginator) {
      this.filePaginator.pageIndex = pageNumber - 1;
      // In a real application, this would fetch data from the server with pagination
    }
  }

  /**
   * Toggles selection for a single file
   * @param file The file to toggle
   */
  onFileToggle(file: any): void {
    this.fileSelectionModel.toggle(file);
  }

  /**
   * Toggles selection for all files
   */
  toggleAllFiles(): void {
    if (this.areAllFilesSelected()) {
      this.fileSelectionModel.clear();
    } else {
      this.fileDataSource.forEach(file => this.fileSelectionModel.select(file));
    }
  }

  /**
   * Checks if all files are selected
   */
  areAllFilesSelected(): boolean {
    const numSelected = this.fileSelectionModel.selected.length;
    const numRows = this.fileDataSource.length;
    return numSelected === numRows && numRows > 0;
  }

  // Create a new email form control
  createEmailControl() {
    return this.fb.control('', [
      Validators.required, 
      Validators.email,
      this.duplicateEmailValidator()
    ]);
  }

  // Custom validator to check for duplicate emails
  duplicateEmailValidator() {
    return (control: FormControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      
      const emails = this.inviteUsersForm?.get('emails') as FormArray;
      if (!emails) {
        return null;
      }
      
      const emailValues = emails.controls
        .map(ctrl => ctrl.value)
        .filter(val => val);
      
      const isDuplicate = emailValues.filter(email => email === control.value).length > 1;
      return isDuplicate ? { duplicate: true } : null;
    };
  }

  // Channel-related methods
  selectChannel(channel: string): void {
    this.selectedChannel = channel;
    
    // Reset the view tab selection based on channel status
    if (channel === 'FTP' && this.channelStatus.FTP && this.ftpConfiguring) {
      // If FTP is in configuring state, no view tabs are needed
      this.selectedChannelView = '';
    } else if (channel === 'Pull API' && this.channelStatus['Pull API'] && this.pullApiConfiguring) {
      // If Pull API is in configuring state, no view tabs are needed
      this.selectedChannelView = '';
    } else if (channel === 'Push API' && this.channelStatus['Push API'] && this.pushApiConfiguring) {
      // If Push API is in configuring state, no view tabs are needed
      this.selectedChannelView = '';
    } else if (channel === 'Database' && this.channelStatus.Database && this.databaseConfiguring) {
      // If Database is in configuring state, no view tabs are needed
      this.selectedChannelView = '';
    } else if (this.channelStatus[channel]) {
      // Default to configuration view for active channels
      this.selectedChannelView = 'configuration';
    }
  }

  toggleChannelStatus(channel: string, event: any): void {
    const isChecked = event.checked;
    this.channelStatus[channel] = isChecked;
    
    // For FTP, simulate configuration in progress when activated
    if (channel === 'FTP' && isChecked) {
      this.ftpConfiguring = true;
      // Simulate FTP configuration completion after 3 seconds
      setTimeout(() => {
        this.ftpConfiguring = false;
        this.selectedChannelView = 'configuration';
      }, 3000);
    }
    
    // For Pull API, simulate configuration in progress when activated
    if (channel === 'Pull API' && isChecked) {
      this.pullApiConfiguring = true;
      // Simulate Pull API configuration completion after 3 seconds
      setTimeout(() => {
        this.pullApiConfiguring = false;
        this.selectedChannelView = 'configuration';
      }, 3000);
    }
    
    // For Push API, simulate configuration in progress when activated
    if (channel === 'Push API' && isChecked) {
      this.pushApiConfiguring = true;
      // Simulate Push API configuration completion after 3 seconds
      setTimeout(() => {
        this.pushApiConfiguring = false;
        this.selectedChannelView = 'configuration';
      }, 3000);
    }
    
    // For Database, simulate configuration in progress when activated
    if (channel === 'Database' && isChecked) {
      this.databaseConfiguring = true;
      // Simulate Database configuration completion after 3 seconds
      setTimeout(() => {
        this.databaseConfiguring = false;
        this.selectedChannelView = 'configuration';
      }, 3000);
    }
  }

  // File selection methods
  toggleFileSelection(file: any): void {
    file.selected = !file.selected;
  }
  
  // Method to add selected files
  addSelectedFiles(): void {
    const selectedFiles = this.files.filter(file => file.selected);
    
    if (selectedFiles.length === 0) {
      this.showMessage('Please select at least one file');
      return;
    }
    
    // Update database files if Database channel is selected
    const selectedChannels = this.fileUploadFormForVendor.get('channels').value;
    if (selectedChannels.includes('Database')) {
      this.hasDatabaseFiles = true;
      
      // Add selected files to databaseFiles
      selectedFiles.forEach(file => {
        // Check if file already exists
        if (!this.databaseFiles.some(f => f.name === file.name)) {
          this.databaseFiles.push({
            name: file.name,
            type: file.type
          });
          
          // Also add to channelSampleFiles for consistency
          if (!this.channelSampleFiles['Database'].includes(file.name)) {
            this.channelSampleFiles['Database'].push(file.name);
          }
        }
      });
      
      // Show success message
      this.showMessage(`${selectedFiles.length} file(s) added successfully`);
    }
    
    // Close the panel and reset
    this.files = [];
    this.fileUploadFormForVendor.reset();
    
    // Get the current active panel ID to determine which close method to use
    const currentPanelId = this.sidePanelService.getCurrentActivePanelId();
    
    // Close the appropriate panel based on the panel ID
    if (currentPanelId === 'uploadPanelForChannelView') {
      this.closeUploadPanelForChannelView();
    } else if (currentPanelId === 'uploadPanel') {
      this.closeUploadFilePanel();
    } else {
      // Fallback if no specific panel ID is found
      this.sidePanelService.setShowNav(false);
    }
  }

  // For testing purposes only
  enableDatabaseChannel(): void {
    this.channelStatus.Database = true;
    this.selectedChannel = 'Database';
    this.selectedChannelView = 'input-files';
  }

  // Open upload panel specifically for Database files
  openDatabaseUploadPanel(): void {
    // Use the standard openSampleFileSelectionPanel method for consistency
    this.openSampleFileSelectionPanel('Database');
  }

  /**
   * Downloads the template file for the selected channel
   * @param channelType The type of channel to download template for
   */
  downloadTemplate(channelType: string): void {
    const fileExtension = this.templateMap[channelType];
    
    if (!fileExtension) {
      this.snackBar.open(`No template available for ${channelType}`, 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Use the same filename that is displayed in the checkbox label
    const templateFileName = this.randomFileLabels[channelType];
    
    // In a real application,we would fetch this from a server
    // For now, we'll simulate a download by creating an anchor element
    
    // Create a link element
    const link = document.createElement('a');
    link.download = templateFileName;
    
    // In a real application, set the href to the actual file URL
    // For demo purposes, we'll just set it to a dummy URL
    link.href = `assets/templates/template${fileExtension}`;
    
    // Append to body, click and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    this.snackBar.open(`Downloading template: ${templateFileName}`, 'Close', {
      duration: 3000
    });
  }

  /**
   * Generates a random filename with letters and numbers
   * @returns A random string to use as filename
   */
  private generateRandomFileName(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const length = 8; // Length of random string
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * Generates random filenames for each channel type
   */
  private generateRandomFileLabels(): void {
    const channelTypes = [
      'Upload', 
      'Email', 
      'FTP', 
      'Push API', 
      'Pull API', 
      'Database', 
      'Email (Alternative)'
    ];
    
    channelTypes.forEach(channelType => {
      const extension = this.templateMap[channelType];
      const randomName = this.generateRandomFileName();
      this.randomFileLabels[channelType] = `${randomName}${extension}`;
    });
  }

  /**
   * Opens the sample file selection panel for a specific channel
   * @param channelType The channel type to open sample file selection for
   */
  openSampleFileSelectionPanel(channelType: string): void {
    // Close any other panels first
    this.sidePanelService.setShowNav(false);
    
    // Set the active channel for file selection
    this.activeChannelForFileSelection = channelType;
    
    // Give a small delay to ensure panels don't conflict
    setTimeout(() => {
      // Open the sidepanel with the sample file selection panel ID
      this.sidePanelService.setShowNav(true, 'sampleFileSelectionPanel');
    }, 100);
  }
  
  /**
   * Closes the sample file selection panel
   */
  closeSampleFileSelectionPanel(): void {
    this.sidePanelService.setShowNav(false);
  }
  
  /**
   * Toggles the selection of a sample file for the active channel
   * @param filename The sample file to toggle
   * @param isSelected Whether the file is selected
   */
  toggleSampleFileSelection(filename: string, isSelected: boolean): void {
    if (!this.activeChannelForFileSelection) {
      return;
    }
    
    // Get the current files for this channel
    const files = [...this.channelSampleFiles[this.activeChannelForFileSelection]];
    
    if (isSelected) {
      // Add the file if it's not already in the list
      if (!files.includes(filename)) {
        files.push(filename);
      }
    } else {
      // Remove the file if it's in the list
      const index = files.indexOf(filename);
      if (index !== -1) {
        files.splice(index, 1);
      }
    }
    
    // Update the files for this channel
    this.channelSampleFiles[this.activeChannelForFileSelection] = files;
    
    // If this is the Database channel, keep databaseFiles in sync
    if (this.activeChannelForFileSelection === 'Database') {
      this.updateDatabaseFilesFromChannelSampleFiles();
    }
  }
  
  /**
   * Saves the selected sample files for the active channel
   */
  saveSampleFileSelection(): void {
    // Close the panel
    this.sidePanelService.setShowNav(false);
    
    // Show a success message
    this.snackBar.open(
      `Sample files updated for ${this.activeChannelForFileSelection} channel`, 
      'Close', 
      { duration: 3000 }
    );
    
    // Reset the active channel
    this.activeChannelForFileSelection = '';
  }
  
  /**
   * Checks if a specific sample file is selected for the active channel
   * @param filename The sample file to check
   * @returns Whether the file is selected for the active channel
   */
  isSampleFileSelected(filename: string): boolean {
    if (!this.activeChannelForFileSelection) {
      return false;
    }
    
    return this.channelSampleFiles[this.activeChannelForFileSelection].includes(filename);
  }
  
  /**
   * Checks if the active channel has any sample files selected
   * @param channelType The channel type to check
   * @returns Whether the channel has any sample files
   */
  hasChannelSampleFiles(channelType: string): boolean {
    return this.channelSampleFiles[channelType]?.length > 0;
  }

  // Remove Push API file
  removePushApiFile(file: string): void {
    // Remove from pushApiFiles array for backward compatibility
    this.pushApiFiles = this.pushApiFiles.filter(f => f !== file);
    
    // Use the standard toggle function for channel sample files
    this.activeChannelForFileSelection = 'Push API';
    this.toggleSampleFileSelection(file, false);
  }

  // Sync databaseFiles with channelSampleFiles['Database'] for backward compatibility
  private syncDatabaseFilesWithChannelSampleFiles(): void {
    // Update channelSampleFiles['Database'] based on databaseFiles
    if (this.databaseFiles && this.databaseFiles.length > 0) {
      this.hasDatabaseFiles = true;
      // Convert databaseFiles to strings for channelSampleFiles
      this.channelSampleFiles['Database'] = this.databaseFiles.map(file => file.name);
    }
    
    // Update hasDatabaseFiles based on channelSampleFiles
    this.hasDatabaseFiles = this.channelSampleFiles['Database'].length > 0;
  }

  // Update databaseFiles based on channelSampleFiles['Database']
  private updateDatabaseFilesFromChannelSampleFiles(): void {
    // Clear the current databaseFiles array
    this.databaseFiles = [];
    
    // Add each file from channelSampleFiles['Database'] to databaseFiles
    this.channelSampleFiles['Database'].forEach(filename => {
      this.databaseFiles.push({
        name: filename,
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // Default to xlsx
      });
    });
    
    // Update hasDatabaseFiles flag
    this.hasDatabaseFiles = this.databaseFiles.length > 0;
  }

  private initVendorEditForm() {
    this.vendorEditForm = this.fb.group({
      registeredName: ['', Validators.required],
      displayName: ['', Validators.required],
      vendorType: ['', Validators.required],
      code: ['', Validators.required],
      nidPub: ['', Validators.required],
      additionalCodes: [''],
      domain: ['', [this.httpsValidator()]]
    });
  }

  private httpsValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      const valid = control.value.startsWith('https://');
      return valid ? null : { httpsRequired: true };
    };
  }

  getDomainErrorMessage(): string {
    const control = this.vendorEditForm.get('domain');
    if (control?.errors?.['httpsRequired']) {
      return 'Domain must start with https://';
    }
    return '';
  }

  closeEditPanel(): void {
    this.sidePanelService.setShowNav(false);
    this.vendorEditForm.reset();
  }

  saveVendorDetails(): void {
    if (this.vendorEditForm.valid) {
      const formData = this.vendorEditForm.value;
      
      // Open confirmation dialog
      const dialogRef = this.dialog.open(ConfirmDialogComponent, {
        width: '520px',
        data: {
          title: 'Are you sure?',
          message: 'Are you sure you want to save these changes to the vendor details?'
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // TODO: Make API call to update vendor details
          console.log('Updating vendor details:', formData);
          
          // Show success message
          this.snackbarService.openSnackBar('Vendor details updated successfully', 'success');
          
          // Close the panel
          this.closeEditPanel();
          
          // TODO: Refresh the vendor details display
        }
      });
    } else {
      this.markFormGroupTouched(this.vendorEditForm);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
