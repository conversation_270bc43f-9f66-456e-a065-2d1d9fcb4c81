<div class="wrapper" fxLayout="column">
  <div class="filter-container" fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="20px" fxFlex="100">
    <div class="back-icon">
      <mat-icon [routerLink]="'/'+ from"
        [queryParams]="from == 'home' ? {sub: subscriptionId} : {sub: subscriptionId, supplier_code:supplier_code, supplier_name:supplier_name, from: 'supplier'}">
        west</mat-icon>
    </div>
    <div class="selected-batch">
      <span>{{supplier_name}} ({{batch_id}})</span>
    </div>
    <!-- batch activity page header -->
    <!-- search box -->
    <mat-form-field appearance="none" class="search-filter">
      <input autocomplete="off" id="search" matInput placeholder="Search by SKU ID" #searchVal name="searchVal"
        [(ngModel)]="search" (keydown.enter)="getSearchValue(search)" />
      <mat-icon matPrefix class="search-icon" (click)="getSearchValue(search)">search</mat-icon>
      <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
    </mat-form-field>

    <!-- brand filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select placeholder="Brand" [(ngModel)]="selectedBrands" appClickDebounce [debounceTime]="1000"
        (debounceClick)="getSkuSelection()" multiple>
        <mat-option *ngFor="let each of brandsList" [value]="each">{{ each }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!--product status filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select placeholder="Product Status" [(ngModel)]="selectedProductStatus" appClickDebounce
        [debounceTime]="1000" (debounceClick)="getSkuSelection()" multiple>
        <mat-option *ngFor="let each of productStatusList" [value]="each">{{ each }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- sku filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select placeholder="SKU Status" [(ngModel)]="selectedSku" appClickDebounce [debounceTime]="1000"
        (debounceClick)="getSkuSelection()" name="selectedProducts" multiple>
        <mat-option #mulVal *ngFor="let item of skuFilterList" [value]="item.value">{{ item.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- asset filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select [disabled]="selectedPrice != '' || selectedAttribute != ''" placeholder="Assets"
        [(ngModel)]="selectedAsset" appClickDebounce [debounceTime]="1000" (debounceClick)="getSkuSelection()">
        <mat-option *ngFor="let each of assetsList" [value]="each.value">{{ each.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!--price filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select [disabled]="selectedAsset.length != 0 || selectedAttribute != ''" placeholder="Price"
        [(ngModel)]="selectedPrice" appClickDebounce [debounceTime]="250" (debounceClick)="getSkuSelection()">
        <mat-option *ngFor="let each of priceList" [value]="each.value">{{ each.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!--global attr. filter -->
    <mat-form-field appearance="none" class="app-dropdown">
      <mat-select [disabled]="selectedAsset.length != 0 || selectedPrice != ''" placeholder="Global Attribute"
        [(ngModel)]="selectedAttribute" appClickDebounce [debounceTime]="250" (debounceClick)="getSkuSelection()">
        <mat-option *ngFor="let each of globalAttribList" [value]="each.value">{{ each.label }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <button mat-button class="reset-btn filled-btn-primary" fxLayout fxLayoutAlign="center center" (click)="reset()">
      Reset
    </button>

    <div class="messages-icon">
      <div class="dot"></div>
      <img matTooltip="Comments" matTooltipPosition="above" [routerLink]="['/comments']" [queryParams]="{
        sub: subscriptionId,
         origin: '/home/<USER>/batch-activities',
         supplier_code: supplier_code,
         supplier_name: supplier_name,
         batch_id: batch_id,
         category_id: sku_id
        }" src="assets/images/home-icons/message.svg" />
    </div>
  </div>

  <!-- send to module -->
  <div class="send-to-btn" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
    <button [disabled]="!isTransferAllow || tableLoading" (click)="openPanel('r2e')" mat-flat-button
      color="primary">Send to Content Enrichment</button>
    <button [disabled]="!isTransferAllow || tableLoading" (click)="openPanel('r3b')" mat-flat-button
      color="primary">Send to Validate Existing SKUs</button>
  </div>
  <!-- data table -->
  <div class="table-wrapper table-right-margin" fxFlex="100" fxLayout="column" fxLayoutGap="20px" *ngIf="!dataLoading">
    <!-- table -->
    <div class="table-section">
      <table mat-table [dataSource]="skuDataSource" matSort matSortActive="SKU_Type" matSortDisableClear matSort>
        <ng-container matColumnDef="sku_id">
          <th mat-header-cell *matHeaderCellDef>SKU ID</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngIf="element.sku_id; else noData"> {{ element.sku_id }}</span>
            </div>
          </td>
        </ng-container>
        <!-- Position Column -->
        <ng-container matColumnDef="Retailer">
          <th mat-header-cell *matHeaderCellDef>Retailer</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngIf="element.retailer; else noData"> {{ element.retailer }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="Supplier">
          <th mat-header-cell *matHeaderCellDef>Supplier</th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data">
                {{ element.supplier_name }}</span>
              <span class="sub_text" *ngIf="element.supplier_code; else noData">{{element.supplier_code}}</span>
            </div>
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="Brand ">
          <th mat-header-cell *matHeaderCellDef>Brand </th>
          <td mat-cell *matCellDef="let element">
            <div fxLayout="column">
              <span class="main-data" *ngIf="element.brand; else noData"> {{ element.brand }}</span>
              <span class="sub_text" *ngIf="element.brand_code; else noData">{{element.brand_code}}</span>
            </div>
          </td>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="Manufacture_Part_No">
          <th mat-header-cell *matHeaderCellDef>MPN</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data" *ngIf="element.mpn; else noData"> {{ element.mpn }} </span>
          </td>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="Image_Name">
          <th mat-header-cell *matHeaderCellDef>Image</th>
          <td mat-cell *matCellDef="let element">
            <!-- <span class="text-theme-primary img-name" *ngIf="element.image_file_name; else noData" [matTooltip]="element.image_file_url ?.length > 20 ? element.image_file_url : null"> 
              <a class="text-theme-primary " [href]="element.image_file_url" target="_blank"> {{ element.image_file_name | truncate: 10 }}</a>
            </span> -->
            <span *ngIf="element.image_file_url; else noData" (click)="openImageSlider(element.image_file_url)">
              <img [src]="element.image_file_url" class="product-img" />
            </span>

          </td>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="SKU_Type">
          <th mat-header-cell *matHeaderCellDef>SKU Type</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data" *ngIf="element.sku_type; else noData"> {{ element.sku_type }}</span>
          </td>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="Price">
          <th mat-header-cell *matHeaderCellDef>Price (USD)</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data" *ngIf="element.price; else noData"> {{ element.price }}</span>
          </td>
          <ng-template #noData>-</ng-template>
        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="Price_Update">
          <th mat-header-cell *matHeaderCellDef>Price Update</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data" *ngIf="element.price_update; else noData"> {{ element.price_update }}</span>
          </td>

        </ng-container>
        <!-- Symbol Column -->
        <ng-container matColumnDef="Status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let element">
            <span class="main-data" *ngIf="element.status == 'OK'"> {{ element.status }}</span>
            <span class="err-data" *ngIf="element.status == 'Errors'"> {{ element.status }}
              <mat-icon class="err-icon" fontSet="material-icons-outlined" [matTooltip]="element.errors"
                [matTooltipPosition]="'above'">error_outline</mat-icon></span>
          </td>
        </ng-container>

        <ng-container matColumnDef="Comments">
          <th mat-header-cell *matHeaderCellDef>Comments</th>
          <td mat-cell *matCellDef="let element">
            <div class="commets-icon-container">
              <div class="dot-comments" *ngIf="element.has_comments"></div>
              <img [routerLink]="['/comments']" [queryParams]="{
                  category_id: element.sku_id,
                  category: 'row',
                  sub: subscriptionId,
                  origin: '/home/<USER>/batch-activities',
                  supplier_code: supplier_code,
                  supplier_name: supplier_name,
                  batch_id: batch_id

                }" src="assets/images/home-icons/message.svg" />
            </div>

          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <!-- progress spinner -->
      <div class="loading-spinner" fxLayoutAlign="center center" *ngIf="tableLoading">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>
      <!-- no data section -->
      <div class="no-data" *ngIf="skuList?.length == 0 && !tableLoading" fxLayout="row" fxLayoutGap="10px">
        <mat-icon fontSet="material-icons-outlined">info</mat-icon>
        <span>Nothing to display.</span>
      </div>
      <!-- paginator -->
      <div class="custom-paginator" fxLayout="row" fxLayoutAlign="space-between center">
        <div class="jump-to-page" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="center center"
          *ngIf="skuList.length > 0 && !tableLoading">
          <span> Page</span>
          <input type="number" min="1" [max]="total_pages" type="number" [value]="page"
            (keydown.enter)="goToPage($event.target.value)" />
          <span>of</span>
          <span>{{ total_pages }}</span>
        </div>
        <mat-paginator #paginator [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons [length]="totalItems"
          [pageSize]="size" [pageIndex]="page - 1" showFirstLastButtons (page)="onPaginationEvent($event)"
          *ngIf="skuList.length > 0 && !tableLoading">
        </mat-paginator>
      </div>
    </div>
  </div>
  <div class="data-loading-spinner" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="90" strokeWidth="3"></mat-spinner>
  </div>
</div>

<!-- batch transfer panel -->
<app-side-panel [sidenavTemplateRef]="panelContent" [direction]="'right'" [navWidth]="380" [duration]="0.5">
</app-side-panel>

<ng-template #panelContent>
  <mat-progress-bar *ngIf="isServiceBusy" class="panel-top-progressbar" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="activeModule" class="upload-batch-panel">
    <div fxLayout="row" fxLayoutAlign="space-between start">
      <span class="panel-header">{{panelTitle}}</span>
      <img style="cursor: pointer" class="batch-stop-icon" src="assets/images/home-icons/close.svg"
        (click)="closePanel()" />
    </div>

    <form class="upload-form" [formGroup]="batchTransferForm" (ngSubmit)="onTransfer()">
      <p>Batch Name*</p>
      <mat-form-field appearance="none" class="manual-upload-input">
        <input autocomplete="off" matInput placeholder="Batch name..." name="batchName" formControlName="batchName" />
      </mat-form-field>
      <mat-error *ngIf="batchTransferForm.controls['batchName'].hasError('required') &&
          batchTransferForm.controls['batchName'].touched
        ">
        <small> Batch Name is required* </small>
      </mat-error>
      <mat-error *ngIf="batchTransferForm.controls['batchName'].hasError('maxlength')">
        <small> Batch Name should not exceed 100 characters* </small>
      </mat-error>
      <mat-error *ngIf="batchTransferForm.controls['batchName'].hasError('whitespace')">
        <small> Batch Name is invalid* </small>
      </mat-error>

      <p>Task</p>
      <mat-form-field appearance="none" class="manual-upload-input">
        <input autocomplete="off" matInput placeholder="Task..." name="taskInput" formControlName="task" />
      </mat-form-field>

      <p>Tag</p>
      <mat-form-field appearance="none" class="app-dropdown">
        <mat-select placeholder="Select tag" formControlName="tag" name="tag">
          <mat-option #mulVal *ngFor="let tag of tagsList" [value]="tag.id">{{ tag.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <p>Input Format*</p>
      <mat-form-field appearance="none" class="app-dropdown input">
        <mat-select placeholder="Select input format" formControlName="inputFormat" name="uploadInput">
          <mat-option #mulVal *ngFor="let input of inputFormatList" [value]="input.template_id">{{ input.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-error *ngIf="
          batchTransferForm.controls['inputFormat'].hasError('required') &&
          batchTransferForm.controls['inputFormat'].touched">
        <small> Input Format is required* </small>
      </mat-error>

      <p>Output Format*</p>
      <mat-form-field appearance="none" class="app-dropdown">
        <mat-select placeholder="Select output format" formControlName="outputFormat" name="uploadOutput">
          <mat-option #mulVal *ngFor="let output of outputFormatList" [value]="output.template_id">{{ output.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-error *ngIf="
      batchTransferForm.controls['outputFormat'].hasError('required') &&
      batchTransferForm.controls['outputFormat'].touched">
        <small> Output Format is required* </small>
      </mat-error>

      <p>Reference</p>
      <mat-form-field class="manual-upload-input" appearance="none">
        <input autocomplete="off" matInput formControlName="reference" placeholder="Reference..." />
      </mat-form-field>
      <mat-error *ngIf="batchTransferForm.controls['reference'].hasError('maxlength')">
        <small> Reference should not exceed 200 characters </small>
      </mat-error>
      <mat-error *ngIf="batchTransferForm.controls['reference'].hasError('whitespace')">
        <small> Reference is invalid </small>
      </mat-error>

      <p>Description</p>
      <mat-form-field class="description" appearance="outline">
        <textarea class="txt-area" autofocus matInput placeholder="Add description here..." rows="5"
          formControlName="description"></textarea>
        <mat-error *ngIf="batchTransferForm.controls['description'].hasError('maxlength')">
          <small> Description should not exceed 2000 characters* </small>
        </mat-error>
        <mat-error *ngIf="batchTransferForm.controls['description'].hasError('whitespace')
           && batchTransferForm.controls['description'].touched">
          Description is invalid*
        </mat-error>
      </mat-form-field>

      <button mat-raised-button class="sidePanel-upload-btn filled-btn-primary" fxLayoutAlign="center center"
        [disabled]="!batchTransferForm.valid">
        <span>Submit</span>
      </button>
    </form>
  </div>
</ng-template>
