@import "../../../styles/variables";

.wrapper-help {
  margin-right: 40px;
  .card {
    width: 488px;
    margin: 0 auto;
    .card-header {
      padding-top: 10px;
      font-family: $site-font;
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: $theme-black;
      margin-bottom: 40px;
    }
    mat-form-field {
      width: 100%;
    }
    button {
      margin-top: 50px;
      bottom: 30px;
      width: 320px;
      height: 40px;
      border-radius: 4px;
      font-weight: 600;
    }
    .send-btn {
      color: $theme-white;
    }
    .cancel-btn {
      border: 1px solid gray;
      background-color: $theme-white;
      color: gray;
    }

    p {
      font-family: $site-font;
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      color: #3b3e48;
      margin-bottom: -3px;
    }
  }
}

// to avoid red border
::ng-deep .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick{
  color: #E6E8F0!important;
  opacity: 0.8!important;
}