<div class="ticket-view-container">
  <div class="ticket-header-row">
    <div class="ticket-header-title">
      <span>Help-Center</span>
    </div>
    <button mat-raised-button color="primary" (click)="onRaiseTicket()">Raise Ticket</button>
  </div>

  <div class="ticket-tabs-row">
    <div
      *ngFor="let tab of tabs"
      class="ticket-tab"
      [class.active]="selectedTab.id === tab.id"
      (click)="selectTab(tab)"
    >
      {{ tab.header }}
      <span *ngIf="tab.id === 'all' && allTicketsCount > 0" class="tab-badge">{{ allTicketsCount }}</span>
      <span *ngIf="tab.id === 'my-tickets' && myTicketsCount > 0" class="tab-badge">{{ myTicketsCount }}</span>
    </div>
  </div>

  <div class="ticket-list">
    <!-- Loader: Centered -->
    <div *ngIf="ticketListLoading" class="sidebar-loading-spinner ticket-list-loader">
      <mat-spinner diameter="32"></mat-spinner>
    </div>

    <!-- No Data Message: Only after first load -->
    <div *ngIf="!ticketListLoading && filteredTickets.length === 0 && ticketListLoadedOnce" class="no-data-message">
      No tickets found.
    </div>

    <!-- Ticket List -->
    <ng-container *ngIf="!ticketListLoading && filteredTickets.length > 0">
      <div
        *ngFor="let ticket of filteredTickets"
        class="ticket-item"
        [class.selected]="selectedTicketId === ticket.id"
        [class.unread]="!ticket.read"
        (click)="onTicketClick(ticket)"
      >
        <div class="ticket-item-main">
          <div class="ticket-item-type-row">
            <span class="ticket-item-type">{{ ticket.title }}</span>
            <span *ngIf="ticket.label" class="ticket-item-badge label-badge">{{ ticket.label }}</span>
            <span class="ticket-item-badge status-badge" [ngClass]="ticket.status">{{ ticket.status | titlecase }}</span>
          </div>
          <div class="ticket-item-message">{{ ticket.message }}</div>
        </div>
        <div class="ticket-item-meta">
          <span class="ticket-item-date">{{ ticket.date | date: 'MMM d' }}</span>
          <span class="ticket-item-user"><span class="by">-- by </span>{{ logDisplayAuthor(ticket) }}</span>
        </div>
      </div>
    </ng-container>
  </div>
</div>

<!-- Sidebar backdrop -->
<div *ngIf="sidebarOpen" class="sidebar-backdrop" [ngClass]="{'sidebar-open': sidebarOpen}" (click)="onCancel()"></div>

<div class="ticket-sidebar" *ngIf="sidebarOpen" [ngClass]="{'sidebar-open': sidebarOpen}">
  <!-- Header: Always visible -->
  <div class="sidebar-header">
    <div class="header-content">
      <h2 class="header-title">
        <span *ngIf="sidebarMode === 'edit'">Edit Ticket</span>
        <span *ngIf="sidebarMode === 'new'">Raise Ticket</span>
      </h2>
      <button class="header-close-btn" (click)="onCancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Content Area: Show spinner or hide while loading -->
  <div class="sidebar-content">
    <!-- Show group labels as chips -->
    <!-- <div *ngIf="groupLabels.length" class="group-labels-row" style="margin-bottom: 12px; display: flex; flex-wrap: wrap; gap: 8px;">
      <span *ngFor="let label of groupLabels" class="label-chip" [ngStyle]="{'background': label.color, 'color': label.text_color, 'padding': '4px 12px', 'border-radius': '12px', 'font-size': '13px', 'font-weight': 500}">
        {{ label.name }}
      </span>
    </div> -->
    <div *ngIf="isSubmitting || commentSectionLoading" class="sidebar-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
      <mat-spinner diameter="48"></mat-spinner>
    </div>
    <ng-container *ngIf="!isSubmitting && !commentSectionLoading">
      <!-- Ticket Info Row (vertical, key-value, key bold/dark, value lighter) -->
      <div *ngIf="sidebarMode === 'edit' && selectedTicket" class="ticket-info-vertical">
        <div class="info-line"><span class="info-label">Ticket ID:</span> <span class="info-value">{{ selectedTicket.ticketId }}</span></div>
        <div class="info-line"><span class="info-label">Created by:</span> <span class="info-value">{{ selectedTicket.user }}</span></div>
        <div class="info-line"><span class="info-label">Date:</span> <span class="info-value">{{ selectedTicket.date | date: 'MMM d, y' }}</span></div>
        <div class="info-line">
          <span class="info-label">Status:</span>
          <span class="info-value status-badge" [ngClass]="selectedTicket.status">
            {{ selectedTicket.status | titlecase }}
          </span>
        </div>
      </div>
      <!-- Form Fields Section -->
      <div class="form-section">
        <div class="form-field-group">
          <label class="form-label">Title <span class="required">*</span></label>
          <input
            class="form-input"
            [class.error]="formErrors.title"
            type="text"
            [(ngModel)]="editTitle"
            (input)="onFieldChange('title')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Enter ticket title" />
          <div *ngIf="formErrors.title" class="error-message">{{ formErrors.title }}</div>
        </div>

        <!-- Label Dropdown -->
        <div class="form-field-group">
          <label class="form-label">Label</label>
          <select
            class="form-input"
            [(ngModel)]="selectedLabel"
            (change)="onLabelChange()">
            <option value="">Select label (optional)</option>
            <ng-container *ngFor="let label of (projectLabels.length ? projectLabels : groupLabels)">
              <option [value]="label.name">{{ label.name }}</option>
            </ng-container>
          </select>
        </div>

        <div class="form-field-group">
          <label class="form-label">Description</label>
          <textarea
            class="form-textarea"
            [class.error]="formErrors.message"
            rows="3"
            [(ngModel)]="editMessage"
            (input)="onFieldChange('message')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Describe your issue or request (optional)"></textarea>
          <div *ngIf="formErrors.message" class="error-message">{{ formErrors.message }}</div>
        </div>
      </div>
      <!-- Timeline Section -->
      <div class="timeline-section">
        <div class="timeline-header">
          <h3 class="timeline-title">Activity</h3>
          <div class="timeline-debug-controls">
            <button mat-stroked-button (click)="debugTimeline()" style="font-size: 12px; padding: 4px 8px;">
              Debug Timeline
            </button>
          </div>
        </div>

        <!-- GitLab-style Timeline -->
        <div class="gitlab-timeline-container">
          <!-- Timeline Items -->
          <div class="timeline-items">
            <ng-container *ngFor="let group of groupedComments; let i = index">

              <!-- System Activity (Simple text with dot) -->
              <div *ngIf="isSystemActivity(group)" class="timeline-item timeline-item--system">
                <div class="timeline-dot timeline-dot--system">
                  <mat-icon [ngSwitch]="getSystemActivityType(group)">
                    <ng-container *ngSwitchCase="'assigned'">person_add</ng-container>
                    <ng-container *ngSwitchCase="'labeled'">label</ng-container>
                    <ng-container *ngSwitchCase="'state_change'">
                      <span *ngIf="group.comments[0].text?.includes('closed')">check_circle</span>
                      <span *ngIf="group.comments[0].text?.includes('created')">radio_button_unchecked</span>
                    </ng-container>
                    <ng-container *ngSwitchDefault>info</ng-container>
                  </mat-icon>
                </div>
                <div class="timeline-content timeline-content--system">
                  <div class="system-activity-text">
                    <strong>{{ group.comments[0].userName || group.comments[0].user }}</strong>
                    <span class="activity-action">{{ getSystemActivityText(group) }}</span>
                    <span class="activity-time">{{ group.comments[0].timestamp | timeAgo }}</span>
                  </div>
                </div>
              </div>

              <!-- Comment/Discussion (Card format) -->
              <div *ngIf="!isSystemActivity(group)" class="timeline-item timeline-item--comment" #parentCard>
                <div class="timeline-dot timeline-dot--comment">
                  <div class="timeline-avatar" [ngStyle]="{'background': getAvatarColor(group.comments[0].userName || group.comments[0].user)}">
                    <ng-container *ngIf="group.comments[0].avatarUrl; else avatarInitials">
                      <img [src]="group.comments[0].avatarUrl" alt="Avatar" />
                    </ng-container>
                    <ng-template #avatarInitials>
                      <span>{{ getAvatarText(group.comments[0].userName || group.comments[0].user) }}</span>
                    </ng-template>
                  </div>
                </div>
                <div class="timeline-content timeline-content--comment">
                  <div class="comment-card">
                    <!-- Comment Header -->
                    <div class="comment-header">
                      <div class="comment-author-info">
                        <span class="comment-author">{{ group.comments[0].userName || group.comments[0].user }}</span>
                        <span class="comment-timestamp">{{ group.comments[0].timestamp | timeAgo }}</span>
                      </div>
                      <div class="comment-actions">
                        <button mat-icon-button (click)="startReply(group.comments[0])">
                          <mat-icon>reply</mat-icon>
                        </button>
                        <button mat-icon-button (click)="startEditComment(group.comments[0])"
                                *ngIf="editingCommentId !== group.comments[0].id && canEditComment(group.comments[0])">
                          <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button (click)="deleteComment(group.comments[0])"
                                *ngIf="canDeleteComment(group.comments[0])">
                          <mat-icon>delete</mat-icon>
                        </button>
                      </div>
                    </div>

                    <!-- Comment Body -->
                    <div class="comment-body" *ngIf="editingCommentId !== group.comments[0].id">
                      <div [innerHTML]="(group.comments[0].text || group.comments[0].body) | safe:'html'"></div>
                      <div *ngIf="group.comments[0].files && group.comments[0].files.length" class="comment-attachments">
                        <div *ngFor="let file of group.comments[0].files" class="comment-attachment-item">
                          <button mat-button (click)="onAttachmentClick(file)">
                            <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
                            <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
                            {{ file.name }}
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Edit Mode -->
                    <div class="comment-edit" *ngIf="editingCommentId === group.comments[0].id">
                      <div class="modern-quill-editor edit-mode" [id]="'edit-' + group.comments[0].id"></div>
                      <div class="edit-actions">
                        <button mat-raised-button color="primary" (click)="saveEditedComment(group.comments[0])"
                                [disabled]="isCommentSubmitting">
                          Save
                        </button>
                        <button mat-button (click)="cancelEditComment()">Cancel</button>
                      </div>
                    </div>

                    <!-- Thread Replies -->
                    <div *ngIf="group.isDiscussion && group.comments.length > 1" class="thread-replies">
                      <div class="replies-header" *ngIf="group.comments.length > 1">
                        <button mat-button class="collapse-replies-btn" (click)="toggleGroupCollapse(group)">
                          <mat-icon>{{ isGroupCollapsed(group) ? 'expand_more' : 'expand_less' }}</mat-icon>
                          {{ isGroupCollapsed(group) ? 'Show' : 'Hide' }} {{ group.comments.length - 1 }}
                          {{ group.comments.length - 1 === 1 ? 'reply' : 'replies' }}
                        </button>
                      </div>

                      <div *ngIf="!isGroupCollapsed(group)" class="replies-list">
                        <div *ngFor="let reply of group.comments.slice(1)" class="reply-item">
                          <div class="reply-avatar">
                            <div class="avatar-circle small" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                              <ng-container *ngIf="reply.avatarUrl; else replyInitials">
                                <img [src]="reply.avatarUrl" alt="Avatar" />
                              </ng-container>
                              <ng-template #replyInitials>
                                <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                              </ng-template>
                            </div>
                          </div>
                          <div class="reply-content">
                            <div class="reply-header">
                              <span class="reply-author">{{ reply.userName || reply.user }}</span>
                              <span class="reply-timestamp">{{ reply.timestamp | timeAgo }}</span>
                              <div class="reply-actions">
                                <button mat-icon-button (click)="startEditComment(reply)"
                                        *ngIf="editingCommentId !== reply.id && canEditComment(reply)">
                                  <mat-icon>edit</mat-icon>
                                </button>
                                <button mat-icon-button (click)="deleteComment(reply)"
                                        *ngIf="canDeleteComment(reply)">
                                  <mat-icon>delete</mat-icon>
                                </button>
                              </div>
                            </div>
                            <div class="reply-body" *ngIf="editingCommentId !== reply.id">
                              <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                              <div *ngIf="reply.files && reply.files.length" class="comment-attachments">
                                <div *ngFor="let file of reply.files" class="comment-attachment-item">
                                  <button mat-button (click)="onAttachmentClick(file)">
                                    <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
                                    <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
                                    {{ file.name }}
                                  </button>
                                </div>
                              </div>
                            </div>
                            <!-- Edit mode for replies -->
                            <div class="reply-edit" *ngIf="editingCommentId === reply.id">
                              <div class="modern-quill-editor edit-mode" [id]="'edit-' + reply.id"></div>
                              <div class="edit-actions">
                                <button mat-raised-button color="primary" (click)="saveEditedComment(reply)"
                                        [disabled]="isCommentSubmitting">
                                  Save
                                </button>
                                <button mat-button (click)="cancelEditComment()">Cancel</button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reply Input -->
                    <div *ngIf="replyingToCommentId === group.comments[0].id" class="reply-input-section">
                      <div class="modern-quill-editor reply-mode" [id]="'reply-' + group.comments[0].id"></div>
                      <div class="reply-input-actions">
                        <button mat-raised-button color="primary" (click)="sendReply(group.comments[0])"
                                [disabled]="isCommentSubmitting">
                          Reply
                        </button>
                        <button mat-button (click)="cancelReply()">Cancel</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Comment Input Section: Always visible -->
  <div class="comment-input-section">
    <div class="comment-input-container" [class.disabled]="isCommentSubmitting || !isCommentEditorEnabled">
      <div *ngIf="isCommentSubmitting" class="comment-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 40px;">
        <mat-spinner diameter="24"></mat-spinner>
      </div>
      <quill-editor
        [(ngModel)]="editorContent"
        [modules]="quillModules"
        (onEditorCreated)="onEditorCreated($event, 'main')"
        [placeholder]="sidebarMode === 'new' && !isCommentEditorEnabled ? 'Please fill in the title first...' : 'Leave a comment... Use @ to mention users...'"
        [disabled]="!isCommentEditorEnabled || isCommentSubmitting"
        class="modern-quill-editor">
      </quill-editor>

      <div class="pending-file-list" *ngIf="pendingFiles.length">
        <div class="pending-file-item" *ngFor="let file of pendingFiles; let i = index">
          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
          <span class="pending-file-name">{{ file.name }}</span>
          <mat-progress-spinner *ngIf="file.uploading" diameter="18" mode="indeterminate" style="margin-left:8px;"></mat-progress-spinner>
          <span *ngIf="file.error" style="color:#d32f2f; margin-left:8px; font-size:12px;">{{ file.error }}</span>
          <button mat-icon-button color="warn" (click)="pendingFiles.splice(i, 1)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>
      <div class="comment-actions" style="position:absolute; bottom:12px; right:12px;">
        <button
          class="comment-send-btn"
          [disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          [class.disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          (click)="sendComment()">
          <mat-icon>send</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Footer: Always visible -->
  <div class="sidebar-footer">
    <button class="btn-secondary" (click)="onCancel()" [disabled]="isSubmitting">Cancel</button>
    <button
      class="btn-primary"
      (click)="saveTicket()"
      [disabled]="(sidebarMode === 'edit' && !isLabelChanged && !isTicketOwner) || isSubmitting">
      <mat-icon *ngIf="isSubmitting" class="spinner">hourglass_empty</mat-icon>
      {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</div>
