.slider {
    width: 40em;
    height: 35em;
    position: relative;
    background-color: #fff;
    padding: 38px 0;
    img{
        width: 100%;
        height: 80%;
    }
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
}
.tag {
    z-index: 900;
    background: #1475eb;
    width: 80px;
    text-align: center;
    border-radius: 20px;
    color: #fff;
    font-size: 9px;
    font-weight: 600;
    position: absolute;
    left: 43px;
    top: 9px;
}
.arrow {
    position: absolute;
    z-index: 1002;
    display: block;
    top: 58%;
    margin-top: -35px;
    width: 38px;
    height: 38px;
    outline: none !important;
    cursor: pointer;
    border: 1px solid #e0e0e0;
    border-radius: 50px;
    background: black;
}

.arrow.prev {
    opacity: 0.4;
    left: 0px;
    transition: 0.2s linear all;
}

.arrow.next {
    opacity: 0.4;
    right: 0px;
    transition: 0.2s linear all;
}

.arrow.prev:hover {
    opacity: 0.6;
}

.arrow.next:hover {
    opacity: 0.6;
}

pinch-zoom {
    overflow: hidden;
    background-color: inherit !important;
    img {
        max-width: 81% !important;
    }
}
::ng-deep.pz-zoom-button.pz-zoom-control-position-bottom {
    height: 36px !important;
    bottom: 0px !important;
    left: 50% !important;
    margin-left: -28px !important;
    position: relative !important;
    width: 36px !important;
}
::ng-deep.pz-zoom-button {
    background-size: 32px !important;
}
