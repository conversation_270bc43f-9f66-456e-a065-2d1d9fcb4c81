import { Component, Inject, OnInit, Optional } from '@angular/core';
import { HomeService } from '../../service/home.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-delete-tag',
  templateUrl: './delete-tag.component.html',
  styleUrls: ['./delete-tag.component.scss']
})
export class DeleteTagComponent implements OnInit {

  subscriptionId;
  batch_id;
  formValues;
  fromDialog;
  values;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<DeleteTagComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public datas: any
    
  ) { }

  ngOnInit(): void {
    
  }

   closeDialog(canDelete: boolean) {
    // console.log(this.values);
    this.dialogRef.close(canDelete);
  }

}

