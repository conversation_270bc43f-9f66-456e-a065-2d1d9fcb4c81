<div class="wrapper white-bg" fxFlex="100">
  <!-- haeder -->
  <div class="filter-container" fxLayoutAlign="start center">
    <div class="filter-head" fxLayout="row" fxLayoutAlign="space-between start">
      <div fxLayout="row" fxLayoutGap="10px" fxFlex="100" class="search-container" fxLayoutAlign="space-between center">
        <!-- toggle button -->
        <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="start center">
          <!-- <mat-icon class="go-back" [routerLink]="[origin]" [queryParams]="
          origin != '/home' ? {
              supplier_code: supplier_code,
              supplier_name: supplier_name,
              sub: subscriptionId,
              from: 'comments',
              batch_id: batch_id
            } : 
            {
              sub: subscriptionId
            }
            ">west
          </mat-icon> -->
          <mat-icon class="go-back" (click)="goBack()">
            west
          </mat-icon>
          <!-- toggle btn -->
          <mat-button-toggle-group #group="matButtonToggleGroup" [value]="commentsListForType" class="toggle-btn"
            (change)="onButtonToggle(group.value)">
            <mat-button-toggle value="batch">Batch Wise</mat-button-toggle>
            <mat-button-toggle value="row">SKU Wise</mat-button-toggle>
          </mat-button-toggle-group>
        </div>
        <!-- search bar -->
        <div class="comment-header" fxLayoutGap="20px" fxLayoutAlign="center center">
          <!-- <p class="text-theme-primary">Comments</p> -->
          <mat-form-field fxFlex="70" fxFlex.gt-md="90" appearance="none" class="search-filter">
            <input id="search" matInput placeholder="Search by Batch or SKU Id..." [(ngModel)]="search" #searchVal
              name="searchVal" (keyup.enter)="getSearchedItem(search)" />
            <mat-icon matPrefix class="search-icon" (click)="getSearchedItem(search)">search</mat-icon>
            <mat-icon matSuffix class="remove-icon" (click)="resetSearch()" *ngIf="search">close</mat-icon>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>
  <!-- comments container -->
  <div class="comments-wrapper" *ngIf="commentsList?.length > 0 && !dataLoading">
    <div class="comment-division" fxFlex="100" fxLayout="row">
      <!-- short comments (left part) -->
      <div class="division-1" fxFlex="30" infiniteScroll [scrollWindow]="false" [infiniteScrollDistance]="4"
        [infiniteScrollUpDistance]="1.5" [infiniteScrollThrottle]="50" (scrolled)="onScrollDown()">
        <!-- batch/sku wise comment card -->
        <mat-card *ngFor="let comment of commentsList; let i = index" (click)="getActiveCommentIndex(i, comment)"
          [class.active-comment]="activeCommentIndex == i + 1">
          <!-- comment title -->
          <!-- <div fxLayout="row" class="card-info" fxLayoutGap="5">
            <p class="batch-caption text-theme-primary">
              {{ comment.category_id }}
            </p>
            <p class="batch-caption text-theme-primary"
              [matTooltip]="comment.title?.length > 25 ? comment.title : null">
              ({{ comment.title | truncate: 25 }})
            </p>
          </div> -->
          <div fxLayout="row" class="card-info" fxLayoutGap="5px" fxLayoutAlign="space-between center" fxLayout="row">
            <p class="batch-caption text-theme-primary" fxLayout="row">
              <span>{{ comment.category_id }}</span>
              <span class="batch-caption text-theme-primary" fxLayout="row" [matTooltip]="
            comment.title?.length > 25
              ? comment.title
              : null
            ">
                ({{comment.title |  truncate: 25}})
              </span>
            </p>

            <!-- <mat-icon *ngIf="commentsListForType !== 'batch'" fxLayout="column" [matTooltip]="comment.batch_id">info_outline</mat-icon> -->
            <img *ngIf="commentsListForType !== 'batch'" src="../../../assets/images/comments/Info-circle.svg"
              [matTooltip]="comment.batch_id" matTooltipPosition="above" />
          </div>
          <!-- comment content -->
          <!-- comment header -->
          <div fxLayout="row" class="profile">
            <img src="{{ comment.profile_picture }}" />
            <div fxLayout="column" class="profile-info">
              <p class="username">{{ comment.created_by }}</p>
              <p class="comment-time">
                {{ comment.created_at | date: "EEEE, MMMM d, y" }}
              </p>
            </div>
          </div>
          <!-- comment body -->
          <div class="comments">
            <p>
              <span class="text-theme-primary"><span class="tagged-users"
                  *ngFor="let user of comment.tagged_users">@{{ user.name }}</span></span>
              {{ comment.latest_comment }}
            </p>
          </div>
        </mat-card>
      </div>
      <!-- detailed view (right part) -->
      <div class="division-2" fxFlex="70" *ngIf="!commentsLoading" fxLayout="column">
        <div class="comment-section" fxLayoutAlign="space-between center" fxLayout="row">
          <div fxLayout="row" class="card-info" fxLayoutGap="20">
            <!-- comment thread main title -->
            <div class="comment-header" fxLayout="row" fxLayoutGap="5px">
              <p class="batch-caption text-theme-primary">
                {{ selectedCommentId }}
              </p>
              <p class="text-theme-primary" [matTooltip]="
                  commentThreadTitle?.length > 25 ? commentThreadTitle : null
                ">
                ({{ commentThreadTitle | truncate: 25 }})
              </p>
            </div>
          </div>
          <!-- header buttons -->
          <div class="action-btn" fxFlex="row" fxLayoutAlign="end center">
            <!-- <button mat-button class="mention-btn stroked-btn-without-border">Only @Mentions</button> -->
            <!-- <button mat-button class="notify-btn stroked-btn-without-border">Notify Always</button> -->
            <button mat-button class="resolve-btn" (click)="resolveComment()" *ngIf="!commentIsResolved">
              Mark as Resolved
            </button>
          </div>
        </div>
        <!-- comment thread scroll container -->
        <div class="comments-scroll-wrapper">
          <div class="no-comments-yet" fxLayout="column" fxLayoutAlign="center center"
            *ngIf="commentThread?.length == 0">
            <span class="main-msg">No Comments yet.</span>
            <span class="sub-msg">To create a new comment, use @username</span>
          </div>
          <!-- comments scroll-->
          <div class="comments-scroll" #scrollContainer fxLayout="column" fxLayoutGap="20px" infiniteScroll
            [scrollWindow]="false" [infiniteScrollDistance]="1" [infiniteScrollUpDistance]="1"
            [infiniteScrollThrottle]="50" (scrolled)="onCommentThreadScroll()" *ngIf="commentThread?.length > 0">
            <div fxLayout="row" class="cmt-profile" *ngFor="let comment of commentThread; let i = index">
              <!-- comment profile picture -->
              <img src="{{
                  comment.profile_picture
                }}" />
              <div fxLayout="column" class="cmt-profile-picture">
                <!-- comment header -->
                <p class="username">{{ comment.created_by }}</p>
                <p class="comment-time">
                  {{ comment.created_at | date: "EEEE, MMMM d, y" }}
                </p>
                <!-- comment content -->
                <div class="comments" [class.hover-color]="row === i" *ngIf="!comment.editable"
                  fxLayoutAlign="space-between center" fxFlex="100" (mouseover)="
                    userData.username == comment.created_by_username
                      ? (row = i)
                      : (row = -1)
                  " (mouseleave)="row = -1">
                  <span style="width: 80%"><span class="tagged-users text-theme-primary"
                      *ngFor="let user of comment.tagged_users">@{{ user.name }}</span>
                    {{ comment.text }}</span>
                  <span fxLayoutGap="10px" *ngIf="
                      userData.username == comment.created_by_username && row == i
                    ">
                    <img (click)="editComment(comment); comment.editable = true"
                      src="../../../assets/images/comments/edit.svg" />
                    <img (click)="deleteComment(comment.comment_id, i)"
                      src="../../../assets/images/comments/trash.svg" />
                  </span>
                </div>
                <!-- edit comment part -->
                <div class="edit-comment-box" fxLayout="row" *ngIf="comment.editable"
                  fxLayoutAlign="space-between center">
                  <!-- text area -->
                  <textarea matInput name="message" class="edit-comment-text" [(ngModel)]="comment.edited_text"
                    #message="ngModel" #editCommentRef></textarea>
                  <!-- tag configuration -->
                  <flx-mentions [textInputElement]="editCommentRef" [menuTemplate]="menuTemplate"
                    [triggerCharacter]="'@'" [selectedChoices]="getSelectedChoices(comment)"
                    [getChoiceLabel]="getChoiceLabel" (search)="loadChoices($event)"
                    (choiceRemoved)="taggedUserRemovedInCommentEdit($event)"
                    (selectedChoicesChange)="onTaggedUserEdited($event, comment)">
                  </flx-mentions>
                  <!-- tag template-->
                  <ng-template #menuTemplate let-selectChoice="selectChoice">
                    <ul class="flx-selectable-list">
                      <li *ngFor="let user of choices" class="flx-selectable-list-item" (click)="selectChoice(user)">
                        <span title="{{ user.name }}">{{ user.name }}</span>
                      </li>
                    </ul>
                  </ng-template>
                  <!-- update and cancel -->
                  <div fxLayout="row" fxLayoutGap="5px" fxLayoutAlign="space-between center">
                    <img fxLayout="column" class="send-icon" src="../../../assets/images/comments/comment-icon.svg"
                      (click)="updateComment(comment)" />
                    <mat-icon (click)="cancelEdit(comment)">cancel</mat-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- comment area -->
          <div class="comment-box" fxLayout="row">
            <div fxLayout="row" class="cmt-textarea" fxLayoutAlign="space-between center">
              <!-- text area -->
              <textarea matInput placeholder="Comment or add others with @" [(ngModel)]="comments[activeCommentIndex]"
                #textareaRef></textarea>
              <!-- tag configuration -->
              <flx-mentions [textInputElement]="textareaRef" [menuTemplate]="tagTemplate" [triggerCharacter]="'@'"
                [getChoiceLabel]="getChoiceLabel" (search)="loadChoices($event)"
                (selectedChoicesChange)="onSelectedChoicesChange($event)">
              </flx-mentions>
              <!-- tag template -->
              <ng-template #tagTemplate let-selectChoice="selectChoice">
                <ul class="flx-selectable-list">
                  <li *ngFor="let user of choices" class="flx-selectable-list-item" (click)="selectChoice(user)">
                    <span title="{{ user.name }}">{{ user.name }}</span>
                  </li>
                </ul>
              </ng-template>
              <!-- post comment -->
              <img fxLayout="column" class="send-icon" src="../../../assets/images/comments/comment-icon.svg" (click)="
                  postComment(comments[activeCommentIndex], activeCommentIndex)
                " />
            </div>
          </div>
        </div>
      </div>
      <!-- division 2/comment thread loading spinner -->
      <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="commentsLoading">
        <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
      </div>
    </div>
  </div>

  <!-- progress spinner for page -->
  <div class="loading-spinner center-placement" fxLayoutAlign="center center" *ngIf="dataLoading">
    <mat-spinner fxLayoutAlign="center center" diameter="60" strokeWidth="3"></mat-spinner>
  </div>

  <!-- no data section -->
  <div class="no-data" *ngIf="commentsList?.length == 0 && !dataLoading" fxLayout="row" fxLayoutGap="10px">
    <mat-icon fontSet="material-icons-outlined">info</mat-icon>
    <span>No comments to display.</span>
  </div>
</div>
