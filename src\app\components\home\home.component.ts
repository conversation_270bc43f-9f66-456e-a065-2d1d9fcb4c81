import {
  AfterViewInit,
  Component,
  OnInit,
  ViewChild,
  Input,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import moment from 'moment';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HomeService } from '../../service/home.service';
import { UserService } from '../../service/user.service';
import { Auth0Service } from '../../service/auth0.service';
import { SidePanelService } from '../../service/side-panel.service';
import { SnackbarService } from '../../service/snackbar.service';

export interface SupplierStats {
  supplier: string;
  brand: string;
  batches: number;
  total_rows: number;
  new_rows: number;
  maintenance_rows: number;
  price_changes: number;
  status: string;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit {
  displayedColumns: string[] = [
    'supplier',
    'brand',
    'batches',
    'total_rows',
    'new_rows',
    'maintenance_rows',
    'price_changes',
    'status',
  ];
  supplierDataSource;
  datePickerOptions: any[];
  currentDate: any;
  customEndDate: Date;
  customStartDate: Date;
  maxDate: Date;
  minDate: Date;
  pageNumber;
  selected = 'recent';
  start_date;
  end_date;
  page: number;
  size: number;
  totalItems: number;
  total_pages: number;
  SubscriptionID: any;
  searchedMultipleVals: string[] = [];
  selectedSupplier;
  filterObj: Object = {};
  subscriptionId: any;
  searchedItems: string[] = [];
  datePickerValue;
  filterList: any;
  supplierStats;
  search;
  label;
  tableLoading: boolean = false;
  dataLoading: boolean = false;
  viewAllBrands: boolean = false;
  brandList: any;
  supplier_brand: any;
  total_batches;
  total_error_sku;
  total_new_sku;
  total_sku;
  total_suppliers;
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private homeService: HomeService,
    public matSnackbar: MatSnackBar,
    private userService: UserService,
    private auth0Service: Auth0Service,
    private sidepanel: SidePanelService,
    private snackbarService: SnackbarService
  ) {}

  @ViewChild(MatPaginator) paginator: MatPaginator;

  @Input() showPaginator: boolean;
  @Input() totalPage: number;

  @ViewChild(MatPaginator) MatPaginator: MatPaginator;

  ngOnInit() {
    this.sidepanel.setShowNav(false);

    this.dataLoading = true;
    // retrieve subscription id
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.SubscriptionID = params.sub;
    });
    const d = new Date();
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.start_date = '';
    this.end_date = '';
    this.label = '';
    this.currentDate = moment().format('YYYY-MM-DD');
    const currentyear = new Date().getFullYear();
    this.minDate = new Date(currentyear - 20, 0, 1);
    this.maxDate = new Date();
    this.selected = 'recent';
    this.datePickerOptions = [
      {
        display: 'Recent',
        value: 'recent',
        start_date: '',
      },
      {
        display: 'Last Week',
        value: 'last_week',
        start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
      },
      {
        display: 'Last Month',
        value: 'last_month',
        start_date: moment()
          .subtract(1, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        display: 'Last Quarter',
        value: 'last_quarter',
        start_date: moment()
          .subtract(3, 'month')
          .add(1, 'day')
          .format('YYYY-MM-DD'),
      },
      {
        value: 'custom_range',
      },
    ];

    this.getSupplierList(this.SubscriptionID);
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  }

  /**
   * get Date
   * @param range
   * @returns
   */
  getDataForDate = (range: string) => {
    // do nothing for custom range
    if (this.selected == 'custom_range') {
      return null;
    }
    (this.customStartDate = null), (this.customEndDate = null);
    // for others
    let interval = this.datePickerOptions.filter((item) => {
      return item.value === range;
    })[0];
    this.start_date = interval['start_date'];
    if (this.selected == 'recent') {
      this.end_date = '';
    } else {
      this.end_date = this.currentDate;
    }

    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  sd;
  ed;
  /**
   * set date range
   * @param dateRangeStart
   * @param dateRangeEnd
   */
  dateRangeChange = (dateRangeStart, dateRangeEnd) => {
    if (moment(dateRangeStart).isValid() && moment(dateRangeEnd).isValid()) {
      this.selected = 'custom_range';
      this.sd = moment(dateRangeStart).format('YYYY-MM-DD');
      this.ed = moment(dateRangeEnd).format('YYYY-MM-DD');
      this.page = 1;
      this.size = 50;
      if (this.selected == 'custom_range') {
        this.start_date = this.sd;
        this.end_date = this.ed;
      }
      this.supplierDataSource = [];
      this.tableLoading = true;
      this.getSupplierTable(
        this.page,
        this.size,
        this.search,
        this.start_date,
        this.end_date,
        this.label,
        this.SubscriptionID
      );
    }
  };

  /**
   * Search based on keyword
   * @param value
   */

  getSearchValue = (value) => {
    this.search = value.trim();
    this.page = 1;
    this.size = 50;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
    this.paginator.firstPage();
  };

  /**
   * Get module table data on pagination
   * @param identifier
   * @param tabName
   */
  onPaginationEvent = (identifier) => {
    this.page = identifier.pageIndex + 1;
    this.size = identifier.pageSize;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  /**
   * Jump pages
   * @param event
   */
  goToPage = (event) => {
    this.page = event;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  /**
   * product filters
   */
  getProductSelection = () => {
    this.searchedMultipleVals = [];
    this.filterObj[this.subscriptionId] = {
      q: this.searchedItems,
      start_date: this.start_date,
      end_date: this.end_date,
      product_list_model: this.selectedSupplier,
      product_list: this.searchedMultipleVals,
      date_picker_value: this.datePickerValue,
    };
    this.selectedSupplier.forEach((element) => {
      this.searchedMultipleVals.push(element.supplier_code);
    });
    this.label = this.searchedMultipleVals;
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  reset = () => {
    this.search = '';
    this.page = 1;
    this.size = 50;
    this.start_date = '';
    this.end_date = '';
    this.customStartDate = null;
    this.customEndDate = null;
    this.label = '';
    this.selected = 'recent';
    this.selectedSupplier = '';
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  resetSearch = () => {
    this.search = '';
    this.supplierDataSource = [];
    this.tableLoading = true;
    this.getSupplierTable(
      this.page,
      this.size,
      this.search,
      this.start_date,
      this.end_date,
      this.label,
      this.SubscriptionID
    );
  };

  /**
   * Supplier filter list
   * @param subscription_id
   */
  getSupplierList = (subscription_id) => {
    this.homeService.getSupplierList(subscription_id).subscribe({
      next: (resp) => {
        this.filterList = resp.result;
      },
      error: (HttpResponse: HttpErrorResponse) => {
        this.snackbarService.openSnackBar(`${HttpResponse.error.detail}`, 'OK');
      },
    });
  };

  /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param start_date
   * @param end_date
   * @param label
   * @param subscription_id
   */
  getSupplierTable = (
    page,
    size,
    search,
    start_date,
    end_date,
    label,
    subscription_id
  ) => {
    // this.dataload = true;
    this.homeService
      .getSupplierStats(
        page,
        size,
        search,
        start_date,
        end_date,
        label,
        subscription_id
      )
      .subscribe({
        next: (resp) => {
          this.supplierStats = resp.result;
          this.totalItems = resp.total_items;
          this.size = resp.page_size;
          this.page = resp.page;
          this.total_pages = resp.total_pages;

          this.total_batches = resp.total_batches;
          this.total_error_sku = resp.total_error_sku;
          this.total_new_sku = resp.total_new_sku;
          this.total_sku = resp.total_sku;
          this.total_suppliers = resp.total_suppliers;
          const HOME_DATA: SupplierStats[] = this.supplierStats;

          this.supplierDataSource = new MatTableDataSource<SupplierStats>(
            HOME_DATA
          );
          this.tableLoading = false;
          this.dataLoading = false;
        },
        error: (HttpResponse: HttpErrorResponse) => {
          this.matSnackbar.open(`${HttpResponse.statusText}`, 'OK', {
            duration: 3000,
          });
        },
      });
  };

  /**
   * closeSidepanel
   */
  closeSidepanel = () => {
    this.sidepanel.toggleNavState();
  };

  /**
   * toggleSideNav
   */
  viewAllBrandsPanelToggle = (brandListValue, supplier_brandValue) => {
    this.brandList = brandListValue;
    this.supplier_brand = supplier_brandValue;
    this.viewAllBrands = true;
    this.sidepanel.setShowNav(true);
  };
}
