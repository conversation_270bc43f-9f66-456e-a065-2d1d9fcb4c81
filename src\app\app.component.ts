import { HttpErrorResponse } from '@angular/common/http';
import { Component } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DomSanitizer, Title } from '@angular/platform-browser';
import {
  ActivatedRoute,
  NavigationEnd,
  NavigationStart,
  Router,
} from '@angular/router';
import { filter, timer, map, mergeMap, Subscription, merge, fromEvent, of, Observable } from 'rxjs';
import { AuthService } from '@auth0/auth0-angular';
import { UserService } from './service/user.service';
import { SharedDataService } from './service/shared-data.service';
import { SidePanelService } from './service/side-panel.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'datax-v2';
  isLowerResolution: boolean | undefined;
  subscriptionId;
  permissionsAvailable;
  isOnline: boolean;
  offlineEvent: Observable<Event>;

  subscription$: Subscription;

  constructor(
    public router: Router,
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private snackbar: MatSnackBar,
    public auth: AuthService,
    public userService: UserService,
    public sharedDaraService:SharedDataService,
    private sidepanel: SidePanelService,

  ) {}

  ngOnInit() {
    this.checkNetworkStatus();
    this.userService.isAppPermissionsAvailable.subscribe((val) => {
      this.permissionsAvailable = val;
    });
    // get subscription id from location url
    const params = new URL(location.href).searchParams;
    // save to local storage if valid param subscription id
    // this happens only one when app loads first time
    // so just grab and keep it
    if (params.get('sub')) {
      localStorage.setItem('SubscriptionID', params.get('sub'));
    }
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    if (this.subscriptionId && this.subscriptionId != 'undefined') {
      // set app theme
      this.userService.setTheme(this.subscriptionId);
    }

    // check for window width and height to show 'resolution unsupported' msg
    if (window.innerWidth < 1280) {
      this.isLowerResolution = true;
      this.sidepanel.setShowNav(false);
    } else {
      this.isLowerResolution = false;
    }

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute),
        map((route) => {
          // tslint:disable-next-line:whitespace
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        }),
        filter((route) => route.outlet === 'primary'),
        mergeMap((route) => route.data)
      )
      .subscribe((event) => this.titleService.setTitle(event['title']));
  }

  /**
   * Low resolution info
   * @param event
   */
  onResize = (event: any) => {
    if (event.target.innerWidth < 1280) {
      this.isLowerResolution = true;
      this.sidepanel.setShowNav(false);
    } else {
      this.isLowerResolution = false;
    }
  };

  ngOnDestroy(): void {
    if(this.subscription$)
    this.subscription$.unsubscribe();
  }

  checkNetworkStatus() {
    this.subscription$ = fromEvent(window, 'offline').subscribe(e => {
      this.sidepanel.setShowNav(false);
      this.sharedDaraService.isOffline = true;
      this.router.navigate(['/offline']);
    });
  }
}
