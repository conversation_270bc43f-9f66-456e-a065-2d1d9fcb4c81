<!-- side navigation -->
<div class="sidebar">
  <div class="sidebar__nav" fxLayout="column">
    <ul fxLayout="column" fxLayoutGap="5px">
      <!-- nav list -->
      <li matRipple>
        <a fxLayout="column" fxLayoutAlign="space-between center" [routerLink]="['/home']" [queryParams]="{sub: subscriptionId}">
          <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
            <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active" #home="routerLinkActive">
              <img [src]="
                home.isActive
                    ? 'assets/images/side-nav/home-active.svg'
                    : 'assets/images/side-nav/home.svg'
                " />
            </div>
            <span class="menu-text" routerLinkActive="active-text">Home</span>
          </div>
        </a>
      </li>
      <li>
        <a fxLayout="column" fxLayoutAlign="space-between center" [routerLink]="['/supplier']" [queryParams]="{ sub: subscriptionId }">
          <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
            <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active"  #supplier="routerLinkActive">
              <img [src]="
                supplier.isActive
                    ? 'assets/images/side-nav/supplier-active.svg'
                    : 'assets/images/side-nav/supplier.svg'
                " />
            </div>
            <span class="menu-text" routerLinkActive="active-text">Supplier</span>
          </div>
        </a>
      </li>
      <!-- <li>
        <a fxLayout="column" fxLayoutAlign="space-between center" [routerLink]="['/dataquality']" [queryParams]="{ sub: subscriptionId }">
          <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
            <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active"  #dataquality="routerLinkActive">
              <img [src]="
                dataquality.isActive
                    ? 'assets/images/side-nav/data-quality-active.svg'
                    : 'assets/images/side-nav/data-quality.svg'
                " />
            </div>
            <span class="menu-text" routerLinkActive="active-text">Data Quality</span>
          </div>
        </a>
      </li> -->
      <li>
        <a fxLayout="column" fxLayoutAlign="space-between center" [routerLink]="['/settings']" [queryParams]="{ sub: subscriptionId }">
          <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
            <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active"  #settings="routerLinkActive">
              <img [src]="
                settings.isActive
                    ? 'assets/images/side-nav/settings-active.svg'
                    : 'assets/images/side-nav/setting.svg'
                " />
            </div>
            <p class="menu-text" routerLinkActive="active-text">Settings</p>
          </div>
        </a>
      </li>
      <!-- <div fxLayout="column" class="support-icon"> -->
        <!-- [routerLink]="['/help']" [queryParams]="{ sub: subscriptionId }" -->
        <!-- <ul>
          <li>
            <a fxLayout="column" href="https://supplierconnector.help.datax.ai/" target="_blank" fxLayoutAlign="space-between center"  >
              <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
                <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active"  #help="routerLinkActive">
                  <img [src]="
                    help.isActive
                        ? 'assets/images/side-nav/help-active.svg'
                        : 'assets/images/side-nav/message.svg'
                    " />
                </div>
                <p class="menu-text" routerLinkActive="active-text">Help</p>
              </div>
            </a>
          </li><!-->




    <div fxLayout="column" class="support-icon">
       <li>
        <a fxLayout="column" fxLayoutAlign="space-between center" [routerLink]="['/Help-Center']" [queryParams]="{ sub: subscriptionId }">
          <div class="menu-wrapper" fxLayout="column" fxLayoutAlign="space-between center">
            <div class="menu-title" fxLayoutAlign="center center" routerLinkActive="active" #helpcenter="routerLinkActive">
              <img [src]="helpcenter.isActive ? 'assets/images/side-nav/help-active.svg' : 'assets/images/side-nav/message.svg'" />
            </div>
            <p class="menu-text" routerLinkActive="active-text">Help Center</p>
          </div>
        </a>
      </li>
    </div>
  </ul>
  </div>
</div>
