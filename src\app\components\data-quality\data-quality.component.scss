@import "../../../styles/variables";

.wrapper-data-quality {
  margin-right: 40px;
  .data-quality-filter {
    margin-left: 20px;
  }
  .stats-container {
    mat-card {
      background: $theme-white;
      box-shadow: 3px 3px 6px 1px rgba(59, 62, 72, 0.02);
      border-radius: 4px;
      height: 97px;
      .card-head {
        font-family: $site-font;
        font-style: normal;
        font-weight: bold;
        font-size: 27px;
        line-height: 40px;
        color: $theme-black;
      }
      .card-sub-head {
        margin-top: 2px;
        padding: 10px;
      }
      .card-actions {
        img {
          height: 40%;
          cursor: pointer;
        }
      }
    }
  }
  .flex-grow {
    flex-grow: 1;
    flex-basis: auto;
  }
}
.data-loading-spinner {
  height: 500px;
}