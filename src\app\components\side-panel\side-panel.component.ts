import { Component, Input, OnInit } from '@angular/core';
import { Observable, combineLatest, map } from 'rxjs';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { SidePanelService } from '../../service/side-panel.service';

export enum SideNavDirection {
  Left = 'left',
  Right = 'right',
}
@Component({
  selector: 'app-side-panel',
  templateUrl: './side-panel.component.html',
  styleUrls: ['./side-panel.component.scss'],
})
export class SidePanelComponent implements OnInit {
  showSideNav: Observable<boolean>;

  @Input() panelId: string = 'default';
  @Input() sidenavTemplateRef: any;
  @Input() duration: number = 0.25;
  @Input() navWidth: number = window.innerWidth;
  @Input() direction: SideNavDirection = SideNavDirection.Left;

  constructor(private sidepanel: SidePanelService) {}

  ngOnInit(): void {
    // Combine both observables to determine if this specific panel should be shown
    this.showSideNav = combineLatest([
      this.sidepanel.getShowNav(),
      this.sidepanel.getActivePanelId()
    ]).pipe(
      map(([isOpen, activePanelId]) => {
        // IMPORTANT: Only show this panel if it's the exact panel being requested
        // Remove the backward compatibility (activePanelId === null) condition
        const shouldShow = isOpen && activePanelId === this.panelId;
        
        return shouldShow;
      })
    );
  }

  // close panel
  onSidebarClose() {
    this.sidepanel.setShowNav(false);
  }
  // panel direction / show
  getSideNavBarStyle(showNav: boolean) {
    let navBarStyle: any = {};

    navBarStyle.transition =
      this.direction +
      ' ' +
      this.duration +
      's, visibility ' +
      this.duration +
      's';
    navBarStyle.width = this.navWidth + 'px';
    navBarStyle[this.direction] = (showNav ? 0 : this.navWidth * -1) + 'px';

    return navBarStyle;
  }
}
