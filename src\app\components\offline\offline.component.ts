import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { fromEvent, Observable, Subscription } from 'rxjs';
import { SharedDataService } from 'src/app/service/shared-data.service';

@Component({
  selector: 'app-offline',
  templateUrl: './offline.component.html',
  styleUrls: ['./offline.component.scss'],
})
export class OfflineComponent implements OnInit {
  SubscriptionID;
  onlineEvent$: Observable<Event>;
  subscription$: Subscription;

  constructor(
    private router: Router,
    private sharedDataService: SharedDataService,
    private location:Location
  ) {}

  ngOnInit(): void {
    this.SubscriptionID = localStorage.getItem('SubscriptionID');
    this.checkNetworkStatus();
    if (navigator.onLine) {
      this.location.back();
      // this.router.navigate(['/home'], {
      //   queryParams: { sub: this.SubscriptionID },
      // });
    }
  }

  checkNetworkStatus() {
    this.subscription$ = fromEvent(window, 'online').subscribe((e) => {
      this.sharedDataService.isOffline = false;
      this.location.back();
      // this.router.navigate(['/home'], {
      //   queryParams: { sub: this.SubscriptionID },
      // });
    });
  }

  ngOnDestroy(): void {
    if (this.subscription$) this.subscription$.unsubscribe();
  }
}
