import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, map, catchError, throwError } from 'rxjs';

export interface AddVendorPayload {
  registered_name: string;
  display_name: string;
  code: number;
  nid_pub: number[];
  additional_codes?: number[];
  domain: string;
  invited_email?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

    /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param start_date
   * @param end_date
   * @param supplier_filter
   * @param subscription_id
   * @returns
   */
     getSupplierInfo = (
      page,
      size,
      search,
      subscription_id
    ): Observable<any> => {
      const options = {
        params: new HttpParams()
          .set('page', page)
          .set('page_size', size)
          .set('q', search)
      };
 
      const SupplierListEndpoint = this.globals.urlJoin('supplier', 'supplierInfo');
      return this.http
        .get(SupplierListEndpoint + subscription_id + '/suppliers_info/', options)
        .pipe(
          map((response: any) => {
            return response;
          }),
          catchError((error) => throwError(error))
        );
    };

    exportUsers = (subscription_id): Observable<any> => {
      const exportUsersEndpoint = this.globals.urlJoinWithParam(
          'supplier',
          'exportUsers',
          subscription_id
        );
      return this.http
      .post(exportUsersEndpoint, {})
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
    }

    /**
     * Add a new vendor
     * @param subscription_id - The subscription ID
     * @param payload - The vendor details
     * @returns Observable of the API response
     */
    addVendor = (subscription_id: string, payload: AddVendorPayload): Observable<any> => {
      const addVendorEndpoint = this.globals.urlJoinWithParam(
        'supplier',
        'addVendor',
        subscription_id
      );
      
      return this.http
        .post(addVendorEndpoint, payload)
        .pipe(
          map((response: any) => {
            return response;
          }),
          catchError((error) => throwError(error))
        );
    };
}