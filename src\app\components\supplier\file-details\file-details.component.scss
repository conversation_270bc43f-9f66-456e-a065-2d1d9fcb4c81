@import "../../../../styles/variables";
.file-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  font-family: 'Poppins', sans-serif;
  margin-top: 50px;
    height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 16px 0px;
    // border-bottom: 1px solid #edeff5;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #434956;
    }

    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      color: #6b7280;
      
      .material-icons {
        font-size: 24px;
      }
      
      &:hover {
        color: #434956;
      }
    }
  }

  .content {
    flex: 1;
    padding: 16px 0px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .checkbox-section {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;

      .checkbox-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        
        ::ng-deep {
          .mat-checkbox-label {
            font-size: 16px;
            color: #434956;
          }
          
          // .mat-checkbox-checked.mat-primary .mat-checkbox-background {
          //   background-color: #4CAF50;
          // }
        }
      }
    }

    .actions {
      display: flex;
      justify-content: space-between;
      margin-top: auto;
      margin-bottom: 20px;

      button {
        padding: 10px 24px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 100px;
      }

      .back-button {
        background-color: #ffffff;
        color: #434956;
        border: 1px solid #d1d5db;
        
        &:hover {
          background-color: #f3f4f6;
        }
      }

      .save-button {
        background-color: #3366ff;
        color: #ffffff;
        border: none;
        
        &:hover {
          background-color: #2952cc;
        }
        
        &:disabled {
          background-color: rgba(51, 102, 255, 0.6);
          cursor: not-allowed;
        }
      }
    }
  }
}

.panel-header {
  margin-bottom: 16px;
  font-family: $site-font;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}