import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../_globals/endpoints.global';
import { Observable, map, catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DataQualityService {
  private httpOptions: HttpHeaders;
  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get batchList filters
   * @param subscription_id
   * @returns
   */
  getBatchListFilter = (subscription_id, supplier_filter): Observable<any> => {
    const options = {
      params: new HttpParams(),
    };
    const FilterListEndpoint = this.globals.urlJoin('dataQuality', 'filter');
    if (supplier_filter !== undefined && supplier_filter.length > 0) {
      supplier_filter.forEach((item) => {
        options.params = options.params.append('supplier_filter', item);
      });
    }
    
    return this.http
      .get(
        FilterListEndpoint + subscription_id + '/data_quality_batches/',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Supplier tabel
   * @param page
   * @param size
   * @param search
   * @param start_date
   * @param end_date
   * @param supplier_filter
   * @param subscription_id
   * @returns
   */
  getdataQualitystats = (
    search,
    start_date,
    end_date,
    supplier_filter,
    batch_filter,
    subscription_id
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    if (supplier_filter !== undefined && supplier_filter.length > 0) {
      supplier_filter.forEach((item) => {
        options.params = options.params.append('supplier_filter', item);
      });
    }
    if (batch_filter !== undefined && batch_filter.length > 0) {
      batch_filter.forEach((item) => {
        options.params = options.params.append('batch_filter', item);
      });
    }
    const SupplierListEndpoint = this.globals.urlJoin(
      'dataQuality',
      'data_quality_stats'
    );
    return this.http
      .get(
        SupplierListEndpoint + subscription_id + '/data_quality_stats/',
        options
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
